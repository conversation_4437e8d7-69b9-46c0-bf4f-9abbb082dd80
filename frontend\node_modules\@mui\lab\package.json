{"name": "@mui/lab", "version": "5.0.0-alpha.177", "private": false, "author": "MUI Team", "description": "Laboratory for new MUI modules.", "main": "./node/index.js", "keywords": ["react", "react-component", "mui", "material-ui", "material design", "lab"], "repository": {"type": "git", "url": "https://github.com/mui/material-ui.git", "directory": "packages/mui-lab"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://v5.mui.com/material-ui/about-the-lab/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "dependencies": {"@babel/runtime": "^7.23.9", "clsx": "^2.1.0", "prop-types": "^15.8.1", "@mui/system": "^5.18.0", "@mui/types": "~7.2.15", "@mui/utils": "^5.17.1", "@mui/base": "5.0.0-beta.40-1"}, "peerDependencies": {"@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@mui/material": ">=5.15.0", "@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "engines": {"node": ">=12.0.0"}, "module": "./index.js", "types": "./index.d.ts"}