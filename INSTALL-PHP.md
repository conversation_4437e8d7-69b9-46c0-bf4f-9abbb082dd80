# 🐘 PHP Installation Guide for Islamic AI Animation Maker

## Quick PHP Installation for Windows

### Method 1: Download PHP (Recommended)
1. **Download PHP**: Go to https://windows.php.net/download/
2. **Choose Version**: Download "Thread Safe" version (PHP 8.2 or 8.3)
3. **Extract**: Extract to `C:\php`
4. **Add to PATH**: 
   - Open System Properties → Environment Variables
   - Add `C:\php` to PATH variable
5. **Test**: Open new CMD/PowerShell and run `php --version`

### Method 2: Using Chocolatey (If you have it)
```powershell
choco install php
```

### Method 3: Using XAMPP (Easiest)
1. Download XAMPP from https://www.apachefriends.org/
2. Install XAMPP
3. PHP will be available at `C:\xampp\php\php.exe`
4. Add `C:\xampp\php` to PATH

## After PHP Installation

### 1. Test PHP
```bash
php --version
```

### 2. Start Islamic AI Animation Maker
```bash
# Navigate to project folder
cd "C:\Users\<USER>\OneDrive\Desktop\story maker"

# Start PHP server
php -S localhost:8000

# Open in browser
# http://localhost:8000/demo.html
```

## Alternative: Portable PHP Setup

If you can't install PHP system-wide, I can create a portable version that works without installation.

---

**Choose your preferred method and let me know when PHP is ready!**
