<?php

namespace App\Http\Controllers;

use App\Models\Animation;
use App\Models\Story;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;

class AnimationController extends Controller
{
    /**
     * Get animation by ID
     */
    public function getAnimation($id): JsonResponse
    {
        try {
            $animation = Animation::with(['story', 'scenes'])->find($id);
            
            if (!$animation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Animation not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $animation->id,
                    'status' => $animation->status,
                    'video_url' => $animation->video_path ? url('/videos/' . basename($animation->video_path)) : null,
                    'thumbnail_url' => $animation->thumbnail_path ? url('/videos/' . basename($animation->thumbnail_path)) : null,
                    'duration' => $animation->duration,
                    'file_size' => $animation->file_size,
                    'story' => [
                        'title' => $animation->story->title,
                        'content' => $animation->story->content,
                        'language' => $animation->story->language
                    ],
                    'scenes' => $animation->scenes->map(function ($scene) {
                        return [
                            'id' => $scene->id,
                            'text' => $scene->dialogue_text,
                            'duration' => $scene->duration,
                            'background' => $scene->background_type,
                            'characters' => $scene->characters_data
                        ];
                    }),
                    'created_at' => $animation->created_at->toISOString(),
                    'updated_at' => $animation->updated_at->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving animation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download animation video
     */
    public function downloadAnimation($id)
    {
        try {
            $animation = Animation::find($id);
            
            if (!$animation || !$animation->video_path) {
                return response()->json([
                    'success' => false,
                    'message' => 'Animation video not found'
                ], 404);
            }

            $filePath = storage_path('app/' . $animation->video_path);
            
            if (!file_exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Video file not found on disk'
                ], 404);
            }

            $fileName = 'islamic_animation_' . $animation->id . '.mp4';
            
            return Response::download($filePath, $fileName, [
                'Content-Type' => 'video/mp4',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error downloading animation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent animations
     */
    public function getRecentAnimations(Request $request): JsonResponse
    {
        try {
            $limit = $request->get('limit', 10);
            $userIp = $request->ip();

            $animations = Animation::with(['story'])
                ->where('status', Animation::STATUS_COMPLETED)
                ->whereHas('story', function ($query) use ($userIp) {
                    $query->where('user_ip', $userIp);
                })
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();

            $data = $animations->map(function ($animation) {
                return [
                    'id' => $animation->id,
                    'title' => $animation->story->title,
                    'thumbnail_url' => $animation->thumbnail_path ? url('/videos/' . basename($animation->thumbnail_path)) : null,
                    'duration' => $animation->duration,
                    'language' => $animation->story->language,
                    'created_at' => $animation->created_at->diffForHumans()
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving recent animations: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get animation statistics
     */
    public function getStats(): JsonResponse
    {
        try {
            $stats = [
                'total_animations' => Animation::where('status', Animation::STATUS_COMPLETED)->count(),
                'total_duration' => Animation::where('status', Animation::STATUS_COMPLETED)->sum('duration'),
                'languages' => Animation::join('stories', 'animations.story_id', '=', 'stories.id')
                    ->where('animations.status', Animation::STATUS_COMPLETED)
                    ->groupBy('stories.language')
                    ->selectRaw('stories.language, count(*) as count')
                    ->pluck('count', 'language'),
                'recent_count' => Animation::where('status', Animation::STATUS_COMPLETED)
                    ->where('created_at', '>=', now()->subDays(7))
                    ->count()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving statistics: ' . $e->getMessage()
            ], 500);
        }
    }
}
