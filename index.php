<?php
/**
 * Islamic AI Animation Maker - Main Entry Point
 * Pure PHP Implementation - <PERSON> Required
 */

// Start session for user tracking
session_start();

// Set timezone
date_default_timezone_set('Asia/Karachi');

// Enable error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'includes/functions.php';
require_once 'includes/templates.php';

// Get request path
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$path = str_replace('/index.php', '', $path);

// Handle different routes
if (strpos($path, '/api/') === 0) {
    // API routes
    handleApiRequest($path);
} elseif ($path === '/' || $path === '') {
    // Main page
    showMainPage();
} elseif ($path === '/demo') {
    // Demo page
    showDemoPage();
} elseif (strpos($path, '/videos/') === 0) {
    // Serve video files
    serveVideoFile($path);
} else {
    // 404
    http_response_code(404);
    echo "Page not found";
}

/**
 * Handle API requests
 */
function handleApiRequest($path) {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
    
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
    
    switch ($path) {
        case '/api/templates':
            echo json_encode(getStoryTemplates());
            break;
            
        case '/api/generate':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);
                echo json_encode(generateAnimation($input));
            } else {
                http_response_code(405);
                echo json_encode(['error' => 'Method not allowed']);
            }
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['error' => 'API endpoint not found']);
    }
}

/**
 * Show main page
 */
function showMainPage() {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🕌 Islamic AI Animation Maker</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
                color: white;
                text-align: center;
                padding: 50px;
                margin: 0;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 40px;
                backdrop-filter: blur(10px);
            }
            .btn {
                background: #1B5E20;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 10px;
                font-size: 18px;
                margin: 10px;
                display: inline-block;
                transition: transform 0.2s;
            }
            .btn:hover {
                transform: translateY(-2px);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🕌 Islamic AI Animation Maker</h1>
            <p>Create beautiful Islamic story animations with AI</p>
            <p>Following Islamic guidelines with respectful character representation</p>
            
            <div style="margin: 30px 0;">
                <a href="/demo" class="btn">🎬 Start Creating Animations</a>
                <a href="/api/templates" class="btn">📚 View Story Templates</a>
            </div>
            
            <div style="margin-top: 40px; font-size: 14px; opacity: 0.8;">
                <p>✅ PHP Backend Active</p>
                <p>✅ Animation Engine Ready</p>
                <p>✅ Islamic Guidelines Enabled</p>
            </div>
        </div>
    </body>
    </html>
    <?php
}

/**
 * Show demo page
 */
function showDemoPage() {
    // Include the demo HTML file content
    include 'demo.html';
}

/**
 * Serve video files
 */
function serveVideoFile($path) {
    $filename = basename($path);
    $filepath = 'videos/' . $filename;
    
    if (file_exists($filepath)) {
        $mime_type = 'video/mp4';
        if (pathinfo($filename, PATHINFO_EXTENSION) === 'html') {
            $mime_type = 'text/html';
        }
        
        header('Content-Type: ' . $mime_type);
        readfile($filepath);
    } else {
        http_response_code(404);
        echo "Video not found";
    }
}
?>
