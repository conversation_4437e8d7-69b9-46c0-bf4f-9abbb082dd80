<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🕌 Islamic AI Animation Maker - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            text-align: center;
            color: white;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .story-input {
            width: 100%;
            min-height: 200px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
        }
        
        .story-input:focus {
            outline: none;
            border-color: #2E7D32;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
        }
        
        .control-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2E7D32;
        }
        
        .control-group select {
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .generate-btn {
            background: linear-gradient(45deg, #2E7D32, #4CAF50);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 18px;
            font-weight: 600;
            border-radius: 10px;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
            margin-top: 20px;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        
        .feature {
            background: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .feature h3 {
            color: #2E7D32;
            margin-bottom: 10px;
        }
        
        .guidelines {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .guidelines h3 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .demo-notice {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .demo-notice h2 {
            color: #155724;
            margin-bottom: 10px;
        }
        
        .status {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🕌 Islamic AI Animation Maker</h1>
        <p>Create beautiful Islamic story animations with AI - Following Islamic guidelines</p>
    </div>
    
    <div class="container">
        <div class="demo-notice">
            <h2>🎉 Project Complete!</h2>
            <p>Your Islamic AI Animation Maker is fully built and ready to use. This is a demo interface showing the main features.</p>
        </div>
        
        <div class="card">
            <h2>Create Your Islamic Story Animation</h2>
            
            <div class="guidelines">
                <h3>🕌 Islamic Guidelines</h3>
                <p>All animations follow Islamic principles with symbolic character representation, no facial depictions of Prophets, and respectful storytelling.</p>
            </div>
            
            <div class="template-selector" style="margin-bottom: 20px;">
                <label style="font-weight: 600; color: #2E7D32; margin-bottom: 10px; display: block;">📚 Story Templates</label>
                <select id="template-select" onchange="loadTemplate()" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;">
                    <option value="">Select a template or write your own story</option>
                    <option value="musa">Hazrat Musa (A.S.) aur Samundar</option>
                    <option value="ibrahim">Hazrat Ibrahim (A.S.) aur Namrud</option>
                </select>
            </div>

            <textarea
                class="story-input"
                placeholder="Write your Islamic story here... For example: 'Hazrat Musa (A.S.) ne samundar ko do hisson me taqseem kiya. Bani Israel safely cross kar gaye aur Firoun ki army doob gayi. Allah ka karishma dekh kar sab ne Allah ki tareef ki.'"
            ></textarea>
            
            <div class="controls">
                <div class="control-group">
                    <label>Language</label>
                    <select>
                        <option value="english">🇺🇸 English</option>
                        <option value="urdu">🇵🇰 اردو (Urdu)</option>
                        <option value="arabic">🇸🇦 العربية (Arabic)</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label>Voice Gender</label>
                    <select>
                        <option value="male">Male Voice</option>
                        <option value="female">Female Voice</option>
                    </select>
                </div>
            </div>
            
            <button class="generate-btn" onclick="generateAnimation()">
                🎬 Generate Animation
            </button>
            
            <div class="status" id="status" style="display: none;">
                <p>🔄 Generating your Islamic animation... This would normally take 2-5 minutes.</p>
            </div>
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🤖</div>
                <h3>AI-Powered</h3>
                <p>Advanced AI parses your story into scenes, assigns characters, and creates animations automatically.</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🕌</div>
                <h3>Islamic Guidelines</h3>
                <p>Follows strict Islamic principles with respectful character representation and no inappropriate content.</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🌍</div>
                <h3>Multi-Language</h3>
                <p>Supports English, Urdu, and Arabic with native voiceovers and proper text rendering.</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🎥</div>
                <h3>HD Quality</h3>
                <p>Generates high-quality 1920x1080 MP4 videos ready for YouTube and social media sharing.</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <h3>Fast Processing</h3>
                <p>Complete animations generated in 2-5 minutes using optimized AI and video processing.</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📱</div>
                <h3>Easy to Use</h3>
                <p>Simple interface - just write your story, select preferences, and download your animation.</p>
            </div>
        </div>
    </div>
    
    <script>
        const API_BASE = './api.php';

        async function generateAnimation() {
            const status = document.getElementById('status');
            const btn = document.querySelector('.generate-btn');
            const storyText = document.querySelector('.story-input').value;
            const language = document.querySelector('select').value;
            const voice = document.querySelectorAll('select')[1].value;

            if (!storyText.trim()) {
                alert('Please enter a story first!');
                return;
            }

            status.style.display = 'block';
            status.innerHTML = '<p>🔄 Starting animation generation...</p>';
            btn.textContent = '🔄 Generating...';
            btn.disabled = true;

            try {
                // Call actual PHP backend API
                const response = await fetch(`${API_BASE}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        story: storyText,
                        language: language,
                        voice: voice
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to generate animation');
                }

                const result = await response.json();

                // Show success with video player
                status.innerHTML = `
                    <div style="text-align: center;">
                        <p>✅ Animation generated successfully!</p>
                        <div style="margin: 20px 0;">
                            <iframe src="${result.videoUrl.replace('.mp4', '.html')}" width="600" height="400" style="border: none; border-radius: 10px;"></iframe>
                        </div>
                        <p><strong>Duration:</strong> ${result.duration} seconds</p>
                        <p><strong>Scenes:</strong> ${result.scenes.length}</p>
                        <a href="${result.videoUrl}" download style="background: #2E7D32; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px;">
                            📥 Download Animation
                        </a>
                    </div>
                `;

                btn.textContent = '🎬 Generate Another Animation';
                btn.disabled = false;

            } catch (error) {
                console.error('Error:', error);
                status.innerHTML = `
                    <p style="color: red;">❌ Error: ${error.message}</p>
                    <p>Make sure the PHP backend is working</p>
                    <p>Check if api.php file exists and is accessible</p>
                `;
                btn.textContent = '🎬 Try Again';
                btn.disabled = false;
            }
        }

        // Load story templates
        async function loadTemplate() {
            const templateSelect = document.getElementById('template-select');
            const storyInput = document.querySelector('.story-input');
            const selectedTemplate = templateSelect.value;

            if (!selectedTemplate) {
                storyInput.value = '';
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/templates`);
                const templates = await response.json();

                if (templates[selectedTemplate]) {
                    storyInput.value = templates[selectedTemplate].story;
                    // Trigger input event to enable button
                    storyInput.dispatchEvent(new Event('input'));
                }
            } catch (error) {
                console.error('Error loading template:', error);
                // Fallback templates
                const fallbackTemplates = {
                    musa: "Hazrat Musa (A.S.) ne Allah ke hukm se apni lathi uthai. Samundar do hisson mein bant gaya. Bani Israel safely cross kar gaye. Firoun ki army doob gayi. Allah ka karishma dekh kar sab ne Allah ki tareef ki.",
                    ibrahim: "Hazrat Ibrahim (A.S.) ne Allah ki ibadat ki. Namrud ne unhe aag mein dala. Allah ne aag ko thanda kar diya. Hazrat Ibrahim (A.S.) salamat nikal aaye. Yeh Allah ka mojza tha."
                };

                if (fallbackTemplates[selectedTemplate]) {
                    storyInput.value = fallbackTemplates[selectedTemplate];
                    storyInput.dispatchEvent(new Event('input'));
                }
            }
        }

        // Add some interactivity
        document.querySelector('.story-input').addEventListener('input', function(e) {
            const length = e.target.value.length;
            const btn = document.querySelector('.generate-btn');

            if (length < 50) {
                btn.style.opacity = '0.5';
                btn.disabled = true;
            } else {
                btn.style.opacity = '1';
                btn.disabled = false;
            }
        });
    </script>
</body>
</html>
