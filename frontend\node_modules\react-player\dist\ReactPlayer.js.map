{"version": 3, "sources": ["global-externals-plugin:react", "../node_modules/load-script/index.js", "../node_modules/deepmerge/dist/cjs.js", "../src/utils.js", "../src/patterns.js", "../src/players/YouTube.js", "../src/players/SoundCloud.js", "../src/players/Vimeo.js", "../src/players/Mux.js", "../src/players/Facebook.js", "../src/players/Streamable.js", "../src/players/Wistia.js", "../src/players/Twitch.js", "../src/players/DailyMotion.js", "../src/players/Mixcloud.js", "../src/players/Vidyard.js", "../src/players/Kaltura.js", "../src/players/FilePlayer.js", "../node_modules/react-fast-compare/index.js", "../node_modules/prop-types/lib/ReactPropTypesSecret.js", "../node_modules/prop-types/factoryWithThrowingShims.js", "../node_modules/prop-types/index.js", "../src/Preview.js", "../src/index.js", "../src/players/index.js", "../src/ReactPlayer.js", "../node_modules/memoize-one/dist/memoize-one.esm.js", "../src/props.js", "../src/Player.js"], "sourcesContent": ["module.exports = globalThis.React", "\nmodule.exports = function load (src, opts, cb) {\n  var head = document.head || document.getElementsByTagName('head')[0]\n  var script = document.createElement('script')\n\n  if (typeof opts === 'function') {\n    cb = opts\n    opts = {}\n  }\n\n  opts = opts || {}\n  cb = cb || function() {}\n\n  script.type = opts.type || 'text/javascript'\n  script.charset = opts.charset || 'utf8';\n  script.async = 'async' in opts ? !!opts.async : true\n  script.src = src\n\n  if (opts.attrs) {\n    setAttributes(script, opts.attrs)\n  }\n\n  if (opts.text) {\n    script.text = '' + opts.text\n  }\n\n  var onend = 'onload' in script ? stdOnEnd : ieOnEnd\n  onend(script, cb)\n\n  // some good legacy browsers (firefox) fail the 'in' detection above\n  // so as a fallback we always set onload\n  // old IE will ignore this and new IE will set onload\n  if (!script.onload) {\n    stdOnEnd(script, cb);\n  }\n\n  head.appendChild(script)\n}\n\nfunction setAttributes(script, attrs) {\n  for (var attr in attrs) {\n    script.setAttribute(attr, attrs[attr]);\n  }\n}\n\nfunction stdOnEnd (script, cb) {\n  script.onload = function () {\n    this.onerror = this.onload = null\n    cb(null, script)\n  }\n  script.onerror = function () {\n    // this.onload = null here is necessary\n    // because even IE9 works not like others\n    this.onerror = this.onload = null\n    cb(new Error('Failed to load ' + this.src), script)\n  }\n}\n\nfunction ieOnEnd (script, cb) {\n  script.onreadystatechange = function () {\n    if (this.readyState != 'complete' && this.readyState != 'loaded') return\n    this.onreadystatechange = null\n    cb(null, script) // there is no way to catch loading errors in IE8\n  }\n}\n", "'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n", "import React from 'react'\nimport loadScript from 'load-script'\nimport merge from 'deepmerge'\n\n/**\n * Dynamic import is supported in CJS modules but needs interop require default logic.\n */\nexport const lazy = (componentImportFn) => React.lazy(async () => {\n  const obj = await componentImportFn()\n  return typeof obj.default === 'function' ? obj : obj.default\n})\n\nconst MATCH_START_QUERY = /[?&#](?:start|t)=([0-9hms]+)/\nconst MATCH_END_QUERY = /[?&#]end=([0-9hms]+)/\nconst MATCH_START_STAMP = /(\\d+)(h|m|s)/g\nconst MATCH_NUMERIC = /^\\d+$/\n\n// Parse YouTube URL for a start time param, ie ?t=1h14m30s\n// and return the start time in seconds\nfunction parseTimeParam (url, pattern) {\n  if (url instanceof Array) {\n    return undefined\n  }\n  const match = url.match(pattern)\n  if (match) {\n    const stamp = match[1]\n    if (stamp.match(MATCH_START_STAMP)) {\n      return parseTimeString(stamp)\n    }\n    if (MATCH_NUMERIC.test(stamp)) {\n      return parseInt(stamp)\n    }\n  }\n  return undefined\n}\n\nfunction parseTimeString (stamp) {\n  let seconds = 0\n  let array = MATCH_START_STAMP.exec(stamp)\n  while (array !== null) {\n    const [, count, period] = array\n    if (period === 'h') seconds += parseInt(count, 10) * 60 * 60\n    if (period === 'm') seconds += parseInt(count, 10) * 60\n    if (period === 's') seconds += parseInt(count, 10)\n    array = MATCH_START_STAMP.exec(stamp)\n  }\n  return seconds\n}\n\nexport function parseStartTime (url) {\n  return parseTimeParam(url, MATCH_START_QUERY)\n}\n\nexport function parseEndTime (url) {\n  return parseTimeParam(url, MATCH_END_QUERY)\n}\n\n// http://stackoverflow.com/a/38622545\nexport function randomString () {\n  return Math.random().toString(36).substr(2, 5)\n}\n\nexport function queryString (object) {\n  return Object\n    .keys(object)\n    .map(key => `${key}=${object[key]}`)\n    .join('&')\n}\n\nfunction getGlobal (key) {\n  if (window[key]) {\n    return window[key]\n  }\n  if (window.exports && window.exports[key]) {\n    return window.exports[key]\n  }\n  if (window.module && window.module.exports && window.module.exports[key]) {\n    return window.module.exports[key]\n  }\n  return null\n}\n\n// Util function to load an external SDK\n// or return the SDK if it is already loaded\nconst requests = {}\nexport const getSDK = enableStubOn(function getSDK (url, sdkGlobal, sdkReady = null, isLoaded = () => true, fetchScript = loadScript) {\n  const existingGlobal = getGlobal(sdkGlobal)\n  if (existingGlobal && isLoaded(existingGlobal)) {\n    return Promise.resolve(existingGlobal)\n  }\n  return new Promise((resolve, reject) => {\n    // If we are already loading the SDK, add the resolve and reject\n    // functions to the existing array of requests\n    if (requests[url]) {\n      requests[url].push({ resolve, reject })\n      return\n    }\n    requests[url] = [{ resolve, reject }]\n    const onLoaded = sdk => {\n      // When loaded, resolve all pending request promises\n      requests[url].forEach(request => request.resolve(sdk))\n    }\n    if (sdkReady) {\n      const previousOnReady = window[sdkReady]\n      window[sdkReady] = function () {\n        if (previousOnReady) previousOnReady()\n        onLoaded(getGlobal(sdkGlobal))\n      }\n    }\n    fetchScript(url, err => {\n      if (err) {\n        // Loading the SDK failed – reject all requests and\n        // reset the array of requests for this SDK\n        requests[url].forEach(request => request.reject(err))\n        requests[url] = null\n      } else if (!sdkReady) {\n        onLoaded(getGlobal(sdkGlobal))\n      }\n    })\n  })\n})\n\nexport function getConfig (props, defaultProps) {\n  return merge(defaultProps.config, props.config)\n}\n\nexport function omit (object, ...arrays) {\n  const omitKeys = [].concat(...arrays)\n  const output = {}\n  const keys = Object.keys(object)\n  for (const key of keys) {\n    if (omitKeys.indexOf(key) === -1) {\n      output[key] = object[key]\n    }\n  }\n  return output\n}\n\nexport function callPlayer (method, ...args) {\n  // Util method for calling a method on this.player\n  // but guard against errors and console.warn instead\n  if (!this.player || !this.player[method]) {\n    let message = `ReactPlayer: ${this.constructor.displayName} player could not call %c${method}%c – `\n    if (!this.player) {\n      message += 'The player was not available'\n    } else if (!this.player[method]) {\n      message += 'The method was not available'\n    }\n    console.warn(message, 'font-weight: bold', '')\n    return null\n  }\n  return this.player[method](...args)\n}\n\nexport function isMediaStream (url) {\n  return (\n    typeof window !== 'undefined' &&\n    typeof window.MediaStream !== 'undefined' &&\n    url instanceof window.MediaStream\n  )\n}\n\nexport function isBlobUrl (url) {\n  return /^blob:/.test(url)\n}\n\nexport function supportsWebKitPresentationMode (video = document.createElement('video')) {\n  // Check if Safari supports PiP, and is not on mobile (other than iPad)\n  // iPhone safari appears to \"support\" PiP through the check, however PiP does not function\n  const notMobile = /iPhone|iPod/.test(navigator.userAgent) === false\n  return video.webkitSupportsPresentationMode && typeof video.webkitSetPresentationMode === 'function' && notMobile\n}\n\n// Workaround for being able to stub out functions in ESM exports.\n// https://github.com/evanw/esbuild/issues/412#issuecomment-723047255\nfunction enableStubOn (fn) {\n  if (globalThis.__TEST__) {\n    const wrap = (...args) => wrap.stub(...args)\n    wrap.stub = fn\n    return wrap\n  }\n  return fn\n}\n", "import { isMediaStream, isBlobUrl } from './utils'\n\nexport const MATCH_URL_YOUTUBE = /(?:youtu\\.be\\/|youtube(?:-nocookie|education)?\\.com\\/(?:embed\\/|v\\/|watch\\/|watch\\?v=|watch\\?.+&v=|shorts\\/|live\\/))((\\w|-){11})|youtube\\.com\\/playlist\\?list=|youtube\\.com\\/user\\//\nexport const MATCH_URL_SOUNDCLOUD = /(?:soundcloud\\.com|snd\\.sc)\\/[^.]+$/\nexport const MATCH_URL_VIMEO = /vimeo\\.com\\/(?!progressive_redirect).+/\n// Match Mux m3u8 URLs without the extension so users can use hls.js with Mux by adding the `.m3u8` extension. https://regexr.com/7um5f\nexport const MATCH_URL_MUX = /stream\\.mux\\.com\\/(?!\\w+\\.m3u8)(\\w+)/\nexport const MATCH_URL_FACEBOOK = /^https?:\\/\\/(www\\.)?facebook\\.com.*\\/(video(s)?|watch|story)(\\.php?|\\/).+$/\nexport const MATCH_URL_FACEBOOK_WATCH = /^https?:\\/\\/fb\\.watch\\/.+$/\nexport const MATCH_URL_STREAMABLE = /streamable\\.com\\/([a-z0-9]+)$/\nexport const MATCH_URL_WISTIA = /(?:wistia\\.(?:com|net)|wi\\.st)\\/(?:medias|embed)\\/(?:iframe\\/)?([^?]+)/\nexport const MATCH_URL_TWITCH_VIDEO = /(?:www\\.|go\\.)?twitch\\.tv\\/videos\\/(\\d+)($|\\?)/\nexport const MATCH_URL_TWITCH_CHANNEL = /(?:www\\.|go\\.)?twitch\\.tv\\/([a-zA-Z0-9_]+)($|\\?)/\nexport const MATCH_URL_DAILYMOTION = /^(?:(?:https?):)?(?:\\/\\/)?(?:www\\.)?(?:(?:dailymotion\\.com(?:\\/embed)?\\/video)|dai\\.ly)\\/([a-zA-Z0-9]+)(?:_[\\w_-]+)?(?:[\\w.#_-]+)?/\nexport const MATCH_URL_MIXCLOUD = /mixcloud\\.com\\/([^/]+\\/[^/]+)/\nexport const MATCH_URL_VIDYARD = /vidyard.com\\/(?:watch\\/)?([a-zA-Z0-9-_]+)/\nexport const MATCH_URL_KALTURA = /^https?:\\/\\/[a-zA-Z]+\\.kaltura.(com|org)\\/p\\/([0-9]+)\\/sp\\/([0-9]+)00\\/embedIframeJs\\/uiconf_id\\/([0-9]+)\\/partner_id\\/([0-9]+)(.*)entry_id.([a-zA-Z0-9-_].*)$/\nexport const AUDIO_EXTENSIONS = /\\.(m4a|m4b|mp4a|mpga|mp2|mp2a|mp3|m2a|m3a|wav|weba|aac|oga|spx)($|\\?)/i\nexport const VIDEO_EXTENSIONS = /\\.(mp4|og[gv]|webm|mov|m4v)(#t=[,\\d+]+)?($|\\?)/i\nexport const HLS_EXTENSIONS = /\\.(m3u8)($|\\?)/i\nexport const DASH_EXTENSIONS = /\\.(mpd)($|\\?)/i\nexport const FLV_EXTENSIONS = /\\.(flv)($|\\?)/i\n\nconst canPlayFile = url => {\n  if (url instanceof Array) {\n    for (const item of url) {\n      if (typeof item === 'string' && canPlayFile(item)) {\n        return true\n      }\n      if (canPlayFile(item.src)) {\n        return true\n      }\n    }\n    return false\n  }\n  if (isMediaStream(url) || isBlobUrl(url)) {\n    return true\n  }\n  return (\n    AUDIO_EXTENSIONS.test(url) ||\n    VIDEO_EXTENSIONS.test(url) ||\n    HLS_EXTENSIONS.test(url) ||\n    DASH_EXTENSIONS.test(url) ||\n    FLV_EXTENSIONS.test(url)\n  )\n}\n\nexport const canPlay = {\n  youtube: url => {\n    if (url instanceof Array) {\n      return url.every(item => MATCH_URL_YOUTUBE.test(item))\n    }\n    return MATCH_URL_YOUTUBE.test(url)\n  },\n  soundcloud: url => MATCH_URL_SOUNDCLOUD.test(url) && !AUDIO_EXTENSIONS.test(url),\n  vimeo: url => MATCH_URL_VIMEO.test(url) && !VIDEO_EXTENSIONS.test(url) && !HLS_EXTENSIONS.test(url),\n  mux: url => MATCH_URL_MUX.test(url),\n  facebook: url => MATCH_URL_FACEBOOK.test(url) || MATCH_URL_FACEBOOK_WATCH.test(url),\n  streamable: url => MATCH_URL_STREAMABLE.test(url),\n  wistia: url => MATCH_URL_WISTIA.test(url),\n  twitch: url => MATCH_URL_TWITCH_VIDEO.test(url) || MATCH_URL_TWITCH_CHANNEL.test(url),\n  dailymotion: url => MATCH_URL_DAILYMOTION.test(url),\n  mixcloud: url => MATCH_URL_MIXCLOUD.test(url),\n  vidyard: url => MATCH_URL_VIDYARD.test(url),\n  kaltura: url => MATCH_URL_KALTURA.test(url),\n  file: canPlayFile\n}\n", "import React, { Component } from 'react'\n\nimport { callPlayer, getSDK, parseStartTime, parseEndTime } from '../utils'\nimport { canPlay, MATCH_URL_YOUTUBE } from '../patterns'\n\nconst SDK_URL = 'https://www.youtube.com/iframe_api'\nconst SDK_GLOBAL = 'YT'\nconst SDK_GLOBAL_READY = 'onYouTubeIframeAPIReady'\nconst MATCH_PLAYLIST = /[?&](?:list|channel)=([a-zA-Z0-9_-]+)/\nconst MATCH_USER_UPLOADS = /user\\/([a-zA-Z0-9_-]+)\\/?/\nconst MATCH_NOCOOKIE = /youtube-nocookie\\.com/\nconst NOCOOKIE_HOST = 'https://www.youtube-nocookie.com'\n\nexport default class YouTube extends Component {\n  static displayName = 'YouTube'\n  static canPlay = canPlay.youtube\n  callPlayer = callPlayer\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n  }\n\n  getID (url) {\n    if (!url || url instanceof Array || MATCH_PLAYLIST.test(url)) {\n      return null\n    }\n    return url.match(MATCH_URL_YOUTUBE)[1]\n  }\n\n  load (url, isReady) {\n    const { playing, muted, playsinline, controls, loop, config, onError } = this.props\n    const { playerVars, embedOptions } = config\n    const id = this.getID(url)\n    if (isReady) {\n      if (MATCH_PLAYLIST.test(url) || MATCH_USER_UPLOADS.test(url) || url instanceof Array) {\n        this.player.loadPlaylist(this.parsePlaylist(url))\n        return\n      }\n      this.player.cueVideoById({\n        videoId: id,\n        startSeconds: parseStartTime(url) || playerVars.start,\n        endSeconds: parseEndTime(url) || playerVars.end\n      })\n      return\n    }\n    getSDK(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY, YT => YT.loaded).then(YT => {\n      if (!this.container) return\n      this.player = new YT.Player(this.container, {\n        width: '100%',\n        height: '100%',\n        videoId: id,\n        playerVars: {\n          autoplay: playing ? 1 : 0,\n          mute: muted ? 1 : 0,\n          controls: controls ? 1 : 0,\n          start: parseStartTime(url),\n          end: parseEndTime(url),\n          origin: window.location.origin,\n          playsinline: playsinline ? 1 : 0,\n          ...this.parsePlaylist(url),\n          ...playerVars\n        },\n        events: {\n          onReady: () => {\n            if (loop) {\n              this.player.setLoop(true) // Enable playlist looping\n            }\n            this.props.onReady()\n          },\n          onPlaybackRateChange: event => this.props.onPlaybackRateChange(event.data),\n          onPlaybackQualityChange: event => this.props.onPlaybackQualityChange(event),\n          onStateChange: this.onStateChange,\n          onError: event => onError(event.data)\n        },\n        host: MATCH_NOCOOKIE.test(url) ? NOCOOKIE_HOST : undefined,\n        ...embedOptions\n      })\n    }, onError)\n    if (embedOptions.events) {\n      console.warn('Using `embedOptions.events` will likely break things. Use ReactPlayer’s callback props instead, eg onReady, onPlay, onPause')\n    }\n  }\n\n  parsePlaylist = (url) => {\n    if (url instanceof Array) {\n      return {\n        listType: 'playlist',\n        playlist: url.map(this.getID).join(',')\n      }\n    }\n    if (MATCH_PLAYLIST.test(url)) {\n      const [, playlistId] = url.match(MATCH_PLAYLIST)\n      return {\n        listType: 'playlist',\n        list: playlistId.replace(/^UC/, 'UU')\n      }\n    }\n    if (MATCH_USER_UPLOADS.test(url)) {\n      const [, username] = url.match(MATCH_USER_UPLOADS)\n      return {\n        listType: 'user_uploads',\n        list: username\n      }\n    }\n    return {}\n  }\n\n  onStateChange = (event) => {\n    const { data } = event\n    const { onPlay, onPause, onBuffer, onBufferEnd, onEnded, onReady, loop, config: { playerVars, onUnstarted } } = this.props\n    const { UNSTARTED, PLAYING, PAUSED, BUFFERING, ENDED, CUED } = window[SDK_GLOBAL].PlayerState\n    if (data === UNSTARTED) onUnstarted()\n    if (data === PLAYING) {\n      onPlay()\n      onBufferEnd()\n    }\n    if (data === PAUSED) onPause()\n    if (data === BUFFERING) onBuffer()\n    if (data === ENDED) {\n      const isPlaylist = !!this.callPlayer('getPlaylist')\n      // Only loop manually if not playing a playlist\n      if (loop && !isPlaylist) {\n        if (playerVars.start) {\n          this.seekTo(playerVars.start)\n        } else {\n          this.play()\n        }\n      }\n      onEnded()\n    }\n    if (data === CUED) onReady()\n  }\n\n  play () {\n    this.callPlayer('playVideo')\n  }\n\n  pause () {\n    this.callPlayer('pauseVideo')\n  }\n\n  stop () {\n    if (!document.body.contains(this.callPlayer('getIframe'))) return\n    this.callPlayer('stopVideo')\n  }\n\n  seekTo (amount, keepPlaying = false) {\n    this.callPlayer('seekTo', amount)\n    if (!keepPlaying && !this.props.playing) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.callPlayer('setVolume', fraction * 100)\n  }\n\n  mute = () => {\n    this.callPlayer('mute')\n  }\n\n  unmute = () => {\n    this.callPlayer('unMute')\n  }\n\n  setPlaybackRate (rate) {\n    this.callPlayer('setPlaybackRate', rate)\n  }\n\n  setLoop (loop) {\n    this.callPlayer('setLoop', loop)\n  }\n\n  getDuration () {\n    return this.callPlayer('getDuration')\n  }\n\n  getCurrentTime () {\n    return this.callPlayer('getCurrentTime')\n  }\n\n  getSecondsLoaded () {\n    return this.callPlayer('getVideoLoadedFraction') * this.getDuration()\n  }\n\n  ref = container => {\n    this.container = container\n  }\n\n  render () {\n    const { display } = this.props\n    const style = {\n      width: '100%',\n      height: '100%',\n      display\n    }\n    return (\n      <div style={style}>\n        <div ref={this.ref} />\n      </div>\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { callPlayer, getSDK } from '../utils'\nimport { canPlay } from '../patterns'\n\nconst SDK_URL = 'https://w.soundcloud.com/player/api.js'\nconst SDK_GLOBAL = 'SC'\n\nexport default class SoundCloud extends Component {\n  static displayName = 'SoundCloud'\n  static canPlay = canPlay.soundcloud\n  static loopOnEnded = true\n  callPlayer = callPlayer\n  duration = null\n  currentTime = null\n  fractionLoaded = null\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n  }\n\n  load (url, isReady) {\n    getSDK(SDK_URL, SDK_GLOBAL).then(SC => {\n      if (!this.iframe) return\n      const { PLAY, PLAY_PROGRESS, PAUSE, FINISH, ERROR } = SC.Widget.Events\n      if (!isReady) {\n        this.player = SC.Widget(this.iframe)\n        this.player.bind(PLAY, this.props.onPlay)\n        this.player.bind(PAUSE, () => {\n          const remaining = this.duration - this.currentTime\n          if (remaining < 0.05) {\n            // Prevent onPause firing right before onEnded\n            return\n          }\n          this.props.onPause()\n        })\n        this.player.bind(PLAY_PROGRESS, e => {\n          this.currentTime = e.currentPosition / 1000\n          this.fractionLoaded = e.loadedProgress\n        })\n        this.player.bind(FINISH, () => this.props.onEnded())\n        this.player.bind(ERROR, e => this.props.onError(e))\n      }\n      this.player.load(url, {\n        ...this.props.config.options,\n        callback: () => {\n          this.player.getDuration(duration => {\n            this.duration = duration / 1000\n            this.props.onReady()\n          })\n        }\n      })\n    })\n  }\n\n  play () {\n    this.callPlayer('play')\n  }\n\n  pause () {\n    this.callPlayer('pause')\n  }\n\n  stop () {\n    // Nothing to do\n  }\n\n  seekTo (seconds, keepPlaying = true) {\n    this.callPlayer('seekTo', seconds * 1000)\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.callPlayer('setVolume', fraction * 100)\n  }\n\n  mute = () => {\n    this.setVolume(0)\n  }\n\n  unmute = () => {\n    if (this.props.volume !== null) {\n      this.setVolume(this.props.volume)\n    }\n  }\n\n  getDuration () {\n    return this.duration\n  }\n\n  getCurrentTime () {\n    return this.currentTime\n  }\n\n  getSecondsLoaded () {\n    return this.fractionLoaded * this.duration\n  }\n\n  ref = iframe => {\n    this.iframe = iframe\n  }\n\n  render () {\n    const { display } = this.props\n    const style = {\n      width: '100%',\n      height: '100%',\n      display\n    }\n    return (\n      <iframe\n        ref={this.ref}\n        src={`https://w.soundcloud.com/player/?url=${encodeURIComponent(this.props.url)}`}\n        style={style}\n        frameBorder={0}\n        allow='autoplay'\n      />\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { callPlayer, getSDK } from '../utils'\nimport { canPlay } from '../patterns'\n\nconst SDK_URL = 'https://player.vimeo.com/api/player.js'\nconst SDK_GLOBAL = 'Vimeo'\n\nconst cleanUrl = url => {\n  return url.replace('/manage/videos', '')\n}\n\nexport default class Vimeo extends Component {\n  static displayName = 'Vimeo'\n  static canPlay = canPlay.vimeo\n  static forceLoad = true // Prevent checking isLoading when URL changes\n  callPlayer = callPlayer\n  duration = null\n  currentTime = null\n  secondsLoaded = null\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n  }\n\n  load (url) {\n    this.duration = null\n    getSDK(SDK_URL, SDK_GLOBAL).then(Vimeo => {\n      if (!this.container) return\n      const { playerOptions, title } = this.props.config\n      this.player = new Vimeo.Player(this.container, {\n        url: cleanUrl(url),\n        autoplay: this.props.playing,\n        muted: this.props.muted,\n        loop: this.props.loop,\n        playsinline: this.props.playsinline,\n        controls: this.props.controls,\n        ...playerOptions\n      })\n      this.player.ready().then(() => {\n        const iframe = this.container.querySelector('iframe')\n        iframe.style.width = '100%'\n        iframe.style.height = '100%'\n        if (title) {\n          iframe.title = title\n        }\n      }).catch(this.props.onError)\n      this.player.on('loaded', () => {\n        this.props.onReady()\n        this.refreshDuration()\n      })\n      this.player.on('play', () => {\n        this.props.onPlay()\n        this.refreshDuration()\n      })\n      this.player.on('pause', this.props.onPause)\n      this.player.on('seeked', e => this.props.onSeek(e.seconds))\n      this.player.on('ended', this.props.onEnded)\n      this.player.on('error', this.props.onError)\n      this.player.on('timeupdate', ({ seconds }) => {\n        this.currentTime = seconds\n      })\n      this.player.on('progress', ({ seconds }) => {\n        this.secondsLoaded = seconds\n      })\n      this.player.on('bufferstart', this.props.onBuffer)\n      this.player.on('bufferend', this.props.onBufferEnd)\n      this.player.on('playbackratechange', e => this.props.onPlaybackRateChange(e.playbackRate))\n    }, this.props.onError)\n  }\n\n  refreshDuration () {\n    this.player.getDuration().then(duration => {\n      this.duration = duration\n    })\n  }\n\n  play () {\n    const promise = this.callPlayer('play')\n    if (promise) {\n      promise.catch(this.props.onError)\n    }\n  }\n\n  pause () {\n    this.callPlayer('pause')\n  }\n\n  stop () {\n    this.callPlayer('unload')\n  }\n\n  seekTo (seconds, keepPlaying = true) {\n    this.callPlayer('setCurrentTime', seconds)\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.callPlayer('setVolume', fraction)\n  }\n\n  setMuted (muted) {\n    this.callPlayer('setMuted', muted)\n  }\n\n  setLoop (loop) {\n    this.callPlayer('setLoop', loop)\n  }\n\n  setPlaybackRate (rate) {\n    this.callPlayer('setPlaybackRate', rate)\n  }\n\n  mute = () => {\n    this.setMuted(true)\n  }\n\n  unmute = () => {\n    this.setMuted(false)\n  }\n\n  getDuration () {\n    return this.duration\n  }\n\n  getCurrentTime () {\n    return this.currentTime\n  }\n\n  getSecondsLoaded () {\n    return this.secondsLoaded\n  }\n\n  ref = container => {\n    this.container = container\n  }\n\n  render () {\n    const { display } = this.props\n    const style = {\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      display\n    }\n    return (\n      <div\n        key={this.props.url}\n        ref={this.ref}\n        style={style}\n      />\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { canPlay, MATCH_URL_MUX } from '../patterns'\n\nconst SDK_URL = 'https://cdn.jsdelivr.net/npm/@mux/mux-player@VERSION/dist/mux-player.mjs'\n\nexport default class Mux extends Component {\n  static displayName = 'Mux'\n  static canPlay = canPlay.mux\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n    this.addListeners(this.player)\n    const playbackId = this.getPlaybackId(this.props.url) // Ensure src is set in strict mode\n    if (playbackId) {\n      this.player.playbackId = playbackId\n    }\n  }\n\n  componentWillUnmount () {\n    this.player.playbackId = null\n    this.removeListeners(this.player)\n  }\n\n  addListeners (player) {\n    const { playsinline } = this.props\n    player.addEventListener('play', this.onPlay)\n    player.addEventListener('waiting', this.onBuffer)\n    player.addEventListener('playing', this.onBufferEnd)\n    player.addEventListener('pause', this.onPause)\n    player.addEventListener('seeked', this.onSeek)\n    player.addEventListener('ended', this.onEnded)\n    player.addEventListener('error', this.onError)\n    player.addEventListener('ratechange', this.onPlayBackRateChange)\n    player.addEventListener('enterpictureinpicture', this.onEnablePIP)\n    player.addEventListener('leavepictureinpicture', this.onDisablePIP)\n    player.addEventListener('webkitpresentationmodechanged', this.onPresentationModeChange)\n    player.addEventListener('canplay', this.onReady)\n    if (playsinline) {\n      player.setAttribute('playsinline', '')\n    }\n  }\n\n  removeListeners (player) {\n    player.removeEventListener('canplay', this.onReady)\n    player.removeEventListener('play', this.onPlay)\n    player.removeEventListener('waiting', this.onBuffer)\n    player.removeEventListener('playing', this.onBufferEnd)\n    player.removeEventListener('pause', this.onPause)\n    player.removeEventListener('seeked', this.onSeek)\n    player.removeEventListener('ended', this.onEnded)\n    player.removeEventListener('error', this.onError)\n    player.removeEventListener('ratechange', this.onPlayBackRateChange)\n    player.removeEventListener('enterpictureinpicture', this.onEnablePIP)\n    player.removeEventListener('leavepictureinpicture', this.onDisablePIP)\n    player.removeEventListener('canplay', this.onReady)\n  }\n\n  // Proxy methods to prevent listener leaks\n  onReady = (...args) => this.props.onReady(...args)\n  onPlay = (...args) => this.props.onPlay(...args)\n  onBuffer = (...args) => this.props.onBuffer(...args)\n  onBufferEnd = (...args) => this.props.onBufferEnd(...args)\n  onPause = (...args) => this.props.onPause(...args)\n  onEnded = (...args) => this.props.onEnded(...args)\n  onError = (...args) => this.props.onError(...args)\n  onPlayBackRateChange = (event) => this.props.onPlaybackRateChange(event.target.playbackRate)\n  onEnablePIP = (...args) => this.props.onEnablePIP(...args)\n\n  onSeek = e => {\n    this.props.onSeek(e.target.currentTime)\n  }\n\n  async load (url) {\n    const { onError, config } = this.props\n\n    if (!globalThis.customElements?.get('mux-player')) {\n      try {\n        const sdkUrl = SDK_URL.replace('VERSION', config.version)\n        await import(/* webpackIgnore: true */ `${sdkUrl}`)\n        this.props.onLoaded()\n      } catch (error) {\n        onError(error)\n      }\n    }\n\n    const [, id] = url.match(MATCH_URL_MUX)\n    this.player.playbackId = id\n  }\n\n  onDurationChange = () => {\n    const duration = this.getDuration()\n    this.props.onDuration(duration)\n  }\n\n  play () {\n    const promise = this.player.play()\n    if (promise) {\n      promise.catch(this.props.onError)\n    }\n  }\n\n  pause () {\n    this.player.pause()\n  }\n\n  stop () {\n    this.player.playbackId = null\n  }\n\n  seekTo (seconds, keepPlaying = true) {\n    this.player.currentTime = seconds\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.player.volume = fraction\n  }\n\n  mute = () => {\n    this.player.muted = true\n  }\n\n  unmute = () => {\n    this.player.muted = false\n  }\n\n  enablePIP () {\n    if (this.player.requestPictureInPicture && document.pictureInPictureElement !== this.player) {\n      this.player.requestPictureInPicture()\n    }\n  }\n\n  disablePIP () {\n    if (document.exitPictureInPicture && document.pictureInPictureElement === this.player) {\n      document.exitPictureInPicture()\n    }\n  }\n\n  setPlaybackRate (rate) {\n    try {\n      this.player.playbackRate = rate\n    } catch (error) {\n      this.props.onError(error)\n    }\n  }\n\n  getDuration () {\n    if (!this.player) return null\n    const { duration, seekable } = this.player\n    // on iOS, live streams return Infinity for the duration\n    // so instead we use the end of the seekable timerange\n    if (duration === Infinity && seekable.length > 0) {\n      return seekable.end(seekable.length - 1)\n    }\n    return duration\n  }\n\n  getCurrentTime () {\n    if (!this.player) return null\n    return this.player.currentTime\n  }\n\n  getSecondsLoaded () {\n    if (!this.player) return null\n    const { buffered } = this.player\n    if (buffered.length === 0) {\n      return 0\n    }\n    const end = buffered.end(buffered.length - 1)\n    const duration = this.getDuration()\n    if (end > duration) {\n      return duration\n    }\n    return end\n  }\n\n  getPlaybackId (url) {\n    const [, id] = url.match(MATCH_URL_MUX)\n    return id\n  }\n\n  ref = player => {\n    this.player = player\n  }\n\n  render () {\n    const { url, playing, loop, controls, muted, config, width, height } = this.props\n    const style = {\n      width: width === 'auto' ? width : '100%',\n      height: height === 'auto' ? height : '100%'\n    }\n    if (controls === false) {\n      style['--controls'] = 'none'\n    }\n    return (\n      <mux-player\n        ref={this.ref}\n        playback-id={this.getPlaybackId(url)}\n        style={style}\n        preload='auto'\n        autoPlay={playing || undefined}\n        muted={muted ? '' : undefined}\n        loop={loop ? '' : undefined}\n        {...config.attributes}\n      />\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { callPlayer, getSDK, randomString } from '../utils'\nimport { canPlay } from '../patterns'\n\nconst SDK_URL = 'https://connect.facebook.net/en_US/sdk.js'\nconst SDK_GLOBAL = 'FB'\nconst SDK_GLOBAL_READY = 'fbAsyncInit'\nconst PLAYER_ID_PREFIX = 'facebook-player-'\n\nexport default class Facebook extends Component {\n  static displayName = 'Facebook'\n  static canPlay = canPlay.facebook\n  static loopOnEnded = true\n  callPlayer = callPlayer\n  playerID = this.props.config.playerId || `${PLAYER_ID_PREFIX}${randomString()}`\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n  }\n\n  load (url, isReady) {\n    if (isReady) {\n      getSDK(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then(FB => FB.XFBML.parse())\n      return\n    }\n    getSDK(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then(FB => {\n      FB.init({\n        appId: this.props.config.appId,\n        xfbml: true,\n        version: this.props.config.version\n      })\n      FB.Event.subscribe('xfbml.render', msg => {\n        // Here we know the SDK has loaded, even if onReady/onPlay\n        // is not called due to a video that cannot be embedded\n        this.props.onLoaded()\n      })\n      FB.Event.subscribe('xfbml.ready', msg => {\n        if (msg.type === 'video' && msg.id === this.playerID) {\n          this.player = msg.instance\n          this.player.subscribe('startedPlaying', this.props.onPlay)\n          this.player.subscribe('paused', this.props.onPause)\n          this.player.subscribe('finishedPlaying', this.props.onEnded)\n          this.player.subscribe('startedBuffering', this.props.onBuffer)\n          this.player.subscribe('finishedBuffering', this.props.onBufferEnd)\n          this.player.subscribe('error', this.props.onError)\n          if (this.props.muted) {\n            this.callPlayer('mute')\n          } else {\n            this.callPlayer('unmute')\n          }\n          this.props.onReady()\n\n          // For some reason Facebook have added `visibility: hidden`\n          // to the iframe when autoplay fails, so here we set it back\n          document.getElementById(this.playerID).querySelector('iframe').style.visibility = 'visible'\n        }\n      })\n    })\n  }\n\n  play () {\n    this.callPlayer('play')\n  }\n\n  pause () {\n    this.callPlayer('pause')\n  }\n\n  stop () {\n    // Nothing to do\n  }\n\n  seekTo (seconds, keepPlaying = true) {\n    this.callPlayer('seek', seconds)\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.callPlayer('setVolume', fraction)\n  }\n\n  mute = () => {\n    this.callPlayer('mute')\n  }\n\n  unmute = () => {\n    this.callPlayer('unmute')\n  }\n\n  getDuration () {\n    return this.callPlayer('getDuration')\n  }\n\n  getCurrentTime () {\n    return this.callPlayer('getCurrentPosition')\n  }\n\n  getSecondsLoaded () {\n    return null\n  }\n\n  render () {\n    const { attributes } = this.props.config\n    const style = {\n      width: '100%',\n      height: '100%'\n    }\n    return (\n      <div\n        style={style}\n        id={this.playerID}\n        className='fb-video'\n        data-href={this.props.url}\n        data-autoplay={this.props.playing ? 'true' : 'false'}\n        data-allowfullscreen='true'\n        data-controls={this.props.controls ? 'true' : 'false'}\n        {...attributes}\n      />\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { callPlayer, getSDK } from '../utils'\nimport { canPlay, MATCH_URL_STREAMABLE } from '../patterns'\n\nconst SDK_URL = 'https://cdn.embed.ly/player-0.1.0.min.js'\nconst SDK_GLOBAL = 'playerjs'\n\nexport default class Streamable extends Component {\n  static displayName = 'Streamable'\n  static canPlay = canPlay.streamable\n  callPlayer = callPlayer\n  duration = null\n  currentTime = null\n  secondsLoaded = null\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n  }\n\n  load (url) {\n    getSDK(SDK_URL, SDK_GLOBAL).then(playerjs => {\n      if (!this.iframe) return\n      this.player = new playerjs.Player(this.iframe)\n      this.player.setLoop(this.props.loop)\n      this.player.on('ready', this.props.onReady)\n      this.player.on('play', this.props.onPlay)\n      this.player.on('pause', this.props.onPause)\n      this.player.on('seeked', this.props.onSeek)\n      this.player.on('ended', this.props.onEnded)\n      this.player.on('error', this.props.onError)\n      this.player.on('timeupdate', ({ duration, seconds }) => {\n        this.duration = duration\n        this.currentTime = seconds\n      })\n      this.player.on('buffered', ({ percent }) => {\n        if (this.duration) {\n          this.secondsLoaded = this.duration * percent\n        }\n      })\n      if (this.props.muted) {\n        this.player.mute()\n      }\n    }, this.props.onError)\n  }\n\n  play () {\n    this.callPlayer('play')\n  }\n\n  pause () {\n    this.callPlayer('pause')\n  }\n\n  stop () {\n    // Nothing to do\n  }\n\n  seekTo (seconds, keepPlaying = true) {\n    this.callPlayer('setCurrentTime', seconds)\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.callPlayer('setVolume', fraction * 100)\n  }\n\n  setLoop (loop) {\n    this.callPlayer('setLoop', loop)\n  }\n\n  mute = () => {\n    this.callPlayer('mute')\n  }\n\n  unmute = () => {\n    this.callPlayer('unmute')\n  }\n\n  getDuration () {\n    return this.duration\n  }\n\n  getCurrentTime () {\n    return this.currentTime\n  }\n\n  getSecondsLoaded () {\n    return this.secondsLoaded\n  }\n\n  ref = iframe => {\n    this.iframe = iframe\n  }\n\n  render () {\n    const id = this.props.url.match(MATCH_URL_STREAMABLE)[1]\n    const style = {\n      width: '100%',\n      height: '100%'\n    }\n    return (\n      <iframe\n        ref={this.ref}\n        src={`https://streamable.com/o/${id}`}\n        frameBorder='0'\n        scrolling='no'\n        style={style}\n        allow='encrypted-media; autoplay; fullscreen;'\n      />\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { callPlayer, getSDK, randomString } from '../utils'\nimport { canPlay, MATCH_URL_WISTIA } from '../patterns'\n\nconst SDK_URL = 'https://fast.wistia.com/assets/external/E-v1.js'\nconst SDK_GLOBAL = 'Wistia'\nconst PLAYER_ID_PREFIX = 'wistia-player-'\n\nexport default class Wistia extends Component {\n  static displayName = 'Wistia'\n  static canPlay = canPlay.wistia\n  static loopOnEnded = true\n  callPlayer = callPlayer\n  playerID = this.props.config.playerId || `${PLAYER_ID_PREFIX}${randomString()}`\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n  }\n\n  load (url) {\n    const { playing, muted, controls, onReady, config, onError } = this.props\n    getSDK(SDK_URL, SDK_GLOBAL).then(Wistia => {\n      if (config.customControls) {\n        config.customControls.forEach(control => Wistia.defineControl(control))\n      }\n      window._wq = window._wq || []\n      window._wq.push({\n        id: this.playerID,\n        options: {\n          autoPlay: playing,\n          silentAutoPlay: 'allow',\n          muted,\n          controlsVisibleOnLoad: controls,\n          fullscreenButton: controls,\n          playbar: controls,\n          playbackRateControl: controls,\n          qualityControl: controls,\n          volumeControl: controls,\n          settingsControl: controls,\n          smallPlayButton: controls,\n          ...config.options\n        },\n        onReady: player => {\n          this.player = player\n          this.unbind()\n          this.player.bind('play', this.onPlay)\n          this.player.bind('pause', this.onPause)\n          this.player.bind('seek', this.onSeek)\n          this.player.bind('end', this.onEnded)\n          this.player.bind('playbackratechange', this.onPlaybackRateChange)\n          onReady()\n        }\n      })\n    }, onError)\n  }\n\n  unbind () {\n    this.player.unbind('play', this.onPlay)\n    this.player.unbind('pause', this.onPause)\n    this.player.unbind('seek', this.onSeek)\n    this.player.unbind('end', this.onEnded)\n    this.player.unbind('playbackratechange', this.onPlaybackRateChange)\n  }\n\n  // Proxy methods to prevent listener leaks\n  onPlay = (...args) => this.props.onPlay(...args)\n  onPause = (...args) => this.props.onPause(...args)\n  onSeek = (...args) => this.props.onSeek(...args)\n  onEnded = (...args) => this.props.onEnded(...args)\n  onPlaybackRateChange = (...args) => this.props.onPlaybackRateChange(...args)\n\n  play () {\n    this.callPlayer('play')\n  }\n\n  pause () {\n    this.callPlayer('pause')\n  }\n\n  stop () {\n    this.unbind()\n    this.callPlayer('remove')\n  }\n\n  seekTo (seconds, keepPlaying = true) {\n    this.callPlayer('time', seconds)\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.callPlayer('volume', fraction)\n  }\n\n  mute = () => {\n    this.callPlayer('mute')\n  }\n\n  unmute = () => {\n    this.callPlayer('unmute')\n  }\n\n  setPlaybackRate (rate) {\n    this.callPlayer('playbackRate', rate)\n  }\n\n  getDuration () {\n    return this.callPlayer('duration')\n  }\n\n  getCurrentTime () {\n    return this.callPlayer('time')\n  }\n\n  getSecondsLoaded () {\n    return null\n  }\n\n  render () {\n    const { url } = this.props\n    const videoID = url && url.match(MATCH_URL_WISTIA)[1]\n    const className = `wistia_embed wistia_async_${videoID}`\n    const style = {\n      width: '100%',\n      height: '100%'\n    }\n    return (\n      <div id={this.playerID} key={videoID} className={className} style={style} />\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { callPlayer, getSDK, parseStartTime, randomString } from '../utils'\nimport { canPlay, MATCH_URL_TWITCH_CHANNEL, MATCH_URL_TWITCH_VIDEO } from '../patterns'\n\nconst SDK_URL = 'https://player.twitch.tv/js/embed/v1.js'\nconst SDK_GLOBAL = 'Twitch'\nconst PLAYER_ID_PREFIX = 'twitch-player-'\n\nexport default class Twitch extends Component {\n  static displayName = 'Twitch'\n  static canPlay = canPlay.twitch\n  static loopOnEnded = true\n  callPlayer = callPlayer\n  playerID = this.props.config.playerId || `${PLAYER_ID_PREFIX}${randomString()}`\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n  }\n\n  load (url, isReady) {\n    const { playsinline, onError, config, controls } = this.props\n    const isChannel = MATCH_URL_TWITCH_CHANNEL.test(url)\n    const id = isChannel ? url.match(MATCH_URL_TWITCH_CHANNEL)[1] : url.match(MATCH_URL_TWITCH_VIDEO)[1]\n    if (isReady) {\n      if (isChannel) {\n        this.player.setChannel(id)\n      } else {\n        this.player.setVideo('v' + id)\n      }\n      return\n    }\n    getSDK(SDK_URL, SDK_GLOBAL).then(Twitch => {\n      this.player = new Twitch.Player(this.playerID, {\n        video: isChannel ? '' : id,\n        channel: isChannel ? id : '',\n        height: '100%',\n        width: '100%',\n        playsinline,\n        autoplay: this.props.playing,\n        muted: this.props.muted,\n        // https://github.com/CookPete/react-player/issues/733#issuecomment-549085859\n        controls: isChannel ? true : controls,\n        time: parseStartTime(url),\n        ...config.options\n      })\n      const { READY, PLAYING, PAUSE, ENDED, ONLINE, OFFLINE, SEEK } = Twitch.Player\n      this.player.addEventListener(READY, this.props.onReady)\n      this.player.addEventListener(PLAYING, this.props.onPlay)\n      this.player.addEventListener(PAUSE, this.props.onPause)\n      this.player.addEventListener(ENDED, this.props.onEnded)\n      this.player.addEventListener(SEEK, this.props.onSeek)\n\n      // Prevent weird isLoading behaviour when streams are offline\n      this.player.addEventListener(ONLINE, this.props.onLoaded)\n      this.player.addEventListener(OFFLINE, this.props.onLoaded)\n    }, onError)\n  }\n\n  play () {\n    this.callPlayer('play')\n  }\n\n  pause () {\n    this.callPlayer('pause')\n  }\n\n  stop () {\n    this.callPlayer('pause')\n  }\n\n  seekTo (seconds, keepPlaying = true) {\n    this.callPlayer('seek', seconds)\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.callPlayer('setVolume', fraction)\n  }\n\n  mute = () => {\n    this.callPlayer('setMuted', true)\n  }\n\n  unmute = () => {\n    this.callPlayer('setMuted', false)\n  }\n\n  getDuration () {\n    return this.callPlayer('getDuration')\n  }\n\n  getCurrentTime () {\n    return this.callPlayer('getCurrentTime')\n  }\n\n  getSecondsLoaded () {\n    return null\n  }\n\n  render () {\n    const style = {\n      width: '100%',\n      height: '100%'\n    }\n    return (\n      <div style={style} id={this.playerID} />\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { callPlayer, getSDK, parseStartTime } from '../utils'\nimport { canPlay, MATCH_URL_DAILYMOTION } from '../patterns'\n\nconst SDK_URL = 'https://api.dmcdn.net/all.js'\nconst SDK_GLOBAL = 'DM'\nconst SDK_GLOBAL_READY = 'dmAsyncInit'\n\nexport default class DailyMotion extends Component {\n  static displayName = 'DailyMotion'\n  static canPlay = canPlay.dailymotion\n  static loopOnEnded = true\n  callPlayer = callPlayer\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n  }\n\n  load (url) {\n    const { controls, config, onError, playing } = this.props\n    const [, id] = url.match(MATCH_URL_DAILYMOTION)\n    if (this.player) {\n      this.player.load(id, {\n        start: parseStartTime(url),\n        autoplay: playing\n      })\n      return\n    }\n    getSDK(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY, DM => DM.player).then(DM => {\n      if (!this.container) return\n      const Player = DM.player\n      this.player = new Player(this.container, {\n        width: '100%',\n        height: '100%',\n        video: id,\n        params: {\n          controls,\n          autoplay: this.props.playing,\n          mute: this.props.muted,\n          start: parseStartTime(url),\n          origin: window.location.origin,\n          ...config.params\n        },\n        events: {\n          apiready: this.props.onReady,\n          seeked: () => this.props.onSeek(this.player.currentTime),\n          video_end: this.props.onEnded,\n          durationchange: this.onDurationChange,\n          pause: this.props.onPause,\n          playing: this.props.onPlay,\n          waiting: this.props.onBuffer,\n          error: event => onError(event)\n        }\n      })\n    }, onError)\n  }\n\n  onDurationChange = () => {\n    const duration = this.getDuration()\n    this.props.onDuration(duration)\n  }\n\n  play () {\n    this.callPlayer('play')\n  }\n\n  pause () {\n    this.callPlayer('pause')\n  }\n\n  stop () {\n    // Nothing to do\n  }\n\n  seekTo (seconds, keepPlaying = true) {\n    this.callPlayer('seek', seconds)\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.callPlayer('setVolume', fraction)\n  }\n\n  mute = () => {\n    this.callPlayer('setMuted', true)\n  }\n\n  unmute = () => {\n    this.callPlayer('setMuted', false)\n  }\n\n  getDuration () {\n    return this.player.duration || null\n  }\n\n  getCurrentTime () {\n    return this.player.currentTime\n  }\n\n  getSecondsLoaded () {\n    return this.player.bufferedTime\n  }\n\n  ref = container => {\n    this.container = container\n  }\n\n  render () {\n    const { display } = this.props\n    const style = {\n      width: '100%',\n      height: '100%',\n      display\n    }\n    return (\n      <div style={style}>\n        <div ref={this.ref} />\n      </div>\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { callPlayer, getSDK, queryString } from '../utils'\nimport { canPlay, MATCH_URL_MIXCLOUD } from '../patterns'\n\nconst SDK_URL = 'https://widget.mixcloud.com/media/js/widgetApi.js'\nconst SDK_GLOBAL = 'Mixcloud'\n\nexport default class Mixcloud extends Component {\n  static displayName = 'Mixcloud'\n  static canPlay = canPlay.mixcloud\n  static loopOnEnded = true\n  callPlayer = callPlayer\n  duration = null\n  currentTime = null\n  secondsLoaded = null\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n  }\n\n  load (url) {\n    getSDK(SDK_URL, SDK_GLOBAL).then(Mixcloud => {\n      this.player = Mixcloud.PlayerWidget(this.iframe)\n      this.player.ready.then(() => {\n        this.player.events.play.on(this.props.onPlay)\n        this.player.events.pause.on(this.props.onPause)\n        this.player.events.ended.on(this.props.onEnded)\n        this.player.events.error.on(this.props.error)\n        this.player.events.progress.on((seconds, duration) => {\n          this.currentTime = seconds\n          this.duration = duration\n        })\n        this.props.onReady()\n      })\n    }, this.props.onError)\n  }\n\n  play () {\n    this.callPlayer('play')\n  }\n\n  pause () {\n    this.callPlayer('pause')\n  }\n\n  stop () {\n    // Nothing to do\n  }\n\n  seekTo (seconds, keepPlaying = true) {\n    this.callPlayer('seek', seconds)\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    // No volume support\n  }\n\n  mute = () => {\n    // No volume support\n  }\n\n  unmute = () => {\n    // No volume support\n  }\n\n  getDuration () {\n    return this.duration\n  }\n\n  getCurrentTime () {\n    return this.currentTime\n  }\n\n  getSecondsLoaded () {\n    return null\n  }\n\n  ref = iframe => {\n    this.iframe = iframe\n  }\n\n  render () {\n    const { url, config } = this.props\n    const id = url.match(MATCH_URL_MIXCLOUD)[1]\n    const style = {\n      width: '100%',\n      height: '100%'\n    }\n    const query = queryString({\n      ...config.options,\n      feed: `/${id}/`\n    })\n    // We have to give the iframe a key here to prevent a\n    // weird dialog appearing when loading a new track\n    return (\n      <iframe\n        key={id}\n        ref={this.ref}\n        style={style}\n        src={`https://player-widget.mixcloud.com/widget/iframe/?${query}`}\n        frameBorder='0'\n        allow='autoplay'\n      />\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { callPlayer, getSDK } from '../utils'\nimport { canPlay, MATCH_URL_VIDYARD } from '../patterns'\n\nconst SDK_URL = 'https://play.vidyard.com/embed/v4.js'\nconst SDK_GLOBAL = 'VidyardV4'\nconst SDK_GLOBAL_READY = 'onVidyardAPI'\n\nexport default class Vidyard extends Component {\n  static displayName = 'Vidyard'\n  static canPlay = canPlay.vidyard\n  callPlayer = callPlayer\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n  }\n\n  load (url) {\n    const { playing, config, onError, onDuration } = this.props\n    const id = url && url.match(MATCH_URL_VIDYARD)[1]\n    if (this.player) {\n      this.stop()\n    }\n    getSDK(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then(Vidyard => {\n      if (!this.container) return\n      Vidyard.api.addReadyListener((data, player) => {\n        if (this.player) {\n          return\n        }\n        this.player = player\n        this.player.on('ready', this.props.onReady)\n        this.player.on('play', this.props.onPlay)\n        this.player.on('pause', this.props.onPause)\n        this.player.on('seek', this.props.onSeek)\n        this.player.on('playerComplete', this.props.onEnded)\n      }, id)\n      Vidyard.api.renderPlayer({\n        uuid: id,\n        container: this.container,\n        autoplay: playing ? 1 : 0,\n        ...config.options\n      })\n      Vidyard.api.getPlayerMetadata(id).then(meta => {\n        this.duration = meta.length_in_seconds\n        onDuration(meta.length_in_seconds)\n      })\n    }, onError)\n  }\n\n  play () {\n    this.callPlayer('play')\n  }\n\n  pause () {\n    this.callPlayer('pause')\n  }\n\n  stop () {\n    window.VidyardV4.api.destroyPlayer(this.player)\n  }\n\n  seekTo (amount, keepPlaying = true) {\n    this.callPlayer('seek', amount)\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.callPlayer('setVolume', fraction)\n  }\n\n  mute = () => {\n    this.setVolume(0)\n  }\n\n  unmute = () => {\n    if (this.props.volume !== null) {\n      this.setVolume(this.props.volume)\n    }\n  }\n\n  setPlaybackRate (rate) {\n    this.callPlayer('setPlaybackSpeed', rate)\n  }\n\n  getDuration () {\n    return this.duration\n  }\n\n  getCurrentTime () {\n    return this.callPlayer('currentTime')\n  }\n\n  getSecondsLoaded () {\n    return null\n  }\n\n  ref = container => {\n    this.container = container\n  }\n\n  render () {\n    const { display } = this.props\n    const style = {\n      width: '100%',\n      height: '100%',\n      display\n    }\n    return (\n      <div style={style}>\n        <div ref={this.ref} />\n      </div>\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { callPlayer, getSDK } from '../utils'\nimport { canPlay } from '../patterns'\n\nconst SDK_URL = 'https://cdn.embed.ly/player-0.1.0.min.js'\nconst SDK_GLOBAL = 'playerjs'\n\nexport default class Kaltura extends Component {\n  static displayName = 'Kaltura'\n  static canPlay = canPlay.kaltura\n  callPlayer = callPlayer\n  duration = null\n  currentTime = null\n  secondsLoaded = null\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n  }\n\n  load (url) {\n    getSDK(SDK_URL, SDK_GLOBAL).then(playerjs => {\n      if (!this.iframe) return\n      this.player = new playerjs.Player(this.iframe)\n      this.player.on('ready', () => {\n        // An arbitrary timeout is required otherwise\n        // the event listeners won’t work\n        setTimeout(() => {\n          this.player.isReady = true\n          this.player.setLoop(this.props.loop)\n          if (this.props.muted) {\n            this.player.mute()\n          }\n          this.addListeners(this.player, this.props)\n          this.props.onReady()\n        }, 500)\n      })\n    }, this.props.onError)\n  }\n\n  addListeners (player, props) {\n    player.on('play', props.onPlay)\n    player.on('pause', props.onPause)\n    player.on('ended', props.onEnded)\n    player.on('error', props.onError)\n    player.on('timeupdate', ({ duration, seconds }) => {\n      this.duration = duration\n      this.currentTime = seconds\n    })\n  }\n\n  play () {\n    this.callPlayer('play')\n  }\n\n  pause () {\n    this.callPlayer('pause')\n  }\n\n  stop () {\n    // Nothing to do\n  }\n\n  seekTo (seconds, keepPlaying = true) {\n    this.callPlayer('setCurrentTime', seconds)\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.callPlayer('setVolume', fraction)\n  }\n\n  setLoop (loop) {\n    this.callPlayer('setLoop', loop)\n  }\n\n  mute = () => {\n    this.callPlayer('mute')\n  }\n\n  unmute = () => {\n    this.callPlayer('unmute')\n  }\n\n  getDuration () {\n    return this.duration\n  }\n\n  getCurrentTime () {\n    return this.currentTime\n  }\n\n  getSecondsLoaded () {\n    return this.secondsLoaded\n  }\n\n  ref = iframe => {\n    this.iframe = iframe\n  }\n\n  render () {\n    const style = {\n      width: '100%',\n      height: '100%'\n    }\n    return (\n      <iframe\n        ref={this.ref}\n        src={this.props.url}\n        frameBorder='0'\n        scrolling='no'\n        style={style}\n        allow='encrypted-media; autoplay; fullscreen;'\n        referrerPolicy='no-referrer-when-downgrade'\n      />\n    )\n  }\n}\n", "import React, { Component } from 'react'\n\nimport { getSDK, isMediaStream, supportsWebKitPresentationMode } from '../utils'\nimport { canPlay, AUDIO_EXTENSIONS, HLS_EXTENSIONS, DASH_EXTENSIONS, FLV_EXTENSIONS } from '../patterns'\n\nconst HAS_NAVIGATOR = typeof navigator !== 'undefined'\nconst IS_IPAD_PRO = HAS_NAVIGATOR && navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1\nconst IS_IOS = HAS_NAVIGATOR && (/iPad|iPhone|iPod/.test(navigator.userAgent) || IS_IPAD_PRO) && !window.MSStream\nconst IS_SAFARI = HAS_NAVIGATOR && (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) && !window.MSStream\nconst HLS_SDK_URL = 'https://cdn.jsdelivr.net/npm/hls.js@VERSION/dist/hls.min.js'\nconst HLS_GLOBAL = 'Hls'\nconst DASH_SDK_URL = 'https://cdnjs.cloudflare.com/ajax/libs/dashjs/VERSION/dash.all.min.js'\nconst DASH_GLOBAL = 'dashjs'\nconst FLV_SDK_URL = 'https://cdn.jsdelivr.net/npm/flv.js@VERSION/dist/flv.min.js'\nconst FLV_GLOBAL = 'flvjs'\nconst MATCH_DROPBOX_URL = /www\\.dropbox\\.com\\/.+/\nconst MATCH_CLOUDFLARE_STREAM = /https:\\/\\/watch\\.cloudflarestream\\.com\\/([a-z0-9]+)/\nconst REPLACE_CLOUDFLARE_STREAM = 'https://videodelivery.net/{id}/manifest/video.m3u8'\n\nexport default class FilePlayer extends Component {\n  static displayName = 'FilePlayer'\n  static canPlay = canPlay.file\n\n  componentDidMount () {\n    this.props.onMount && this.props.onMount(this)\n    this.addListeners(this.player)\n    const src = this.getSource(this.props.url) // Ensure src is set in strict mode\n    if (src) {\n      this.player.src = src\n    }\n    if (IS_IOS || this.props.config.forceDisableHls) {\n      this.player.load()\n    }\n  }\n\n  componentDidUpdate (prevProps) {\n    if (this.shouldUseAudio(this.props) !== this.shouldUseAudio(prevProps)) {\n      this.removeListeners(this.prevPlayer, prevProps.url)\n      this.addListeners(this.player)\n    }\n\n    if (\n      this.props.url !== prevProps.url &&\n      !isMediaStream(this.props.url) &&\n      !(this.props.url instanceof Array) // Avoid infinite loop\n    ) {\n      this.player.srcObject = null\n    }\n  }\n\n  componentWillUnmount () {\n    this.player.removeAttribute('src')\n    this.removeListeners(this.player)\n    if (this.hls) {\n      this.hls.destroy()\n    }\n  }\n\n  addListeners (player) {\n    const { url, playsinline } = this.props\n    player.addEventListener('play', this.onPlay)\n    player.addEventListener('waiting', this.onBuffer)\n    player.addEventListener('playing', this.onBufferEnd)\n    player.addEventListener('pause', this.onPause)\n    player.addEventListener('seeked', this.onSeek)\n    player.addEventListener('ended', this.onEnded)\n    player.addEventListener('error', this.onError)\n    player.addEventListener('ratechange', this.onPlayBackRateChange)\n    player.addEventListener('enterpictureinpicture', this.onEnablePIP)\n    player.addEventListener('leavepictureinpicture', this.onDisablePIP)\n    player.addEventListener('webkitpresentationmodechanged', this.onPresentationModeChange)\n    if (!this.shouldUseHLS(url)) { // onReady is handled by hls.js\n      player.addEventListener('canplay', this.onReady)\n    }\n    if (playsinline) {\n      player.setAttribute('playsinline', '')\n      player.setAttribute('webkit-playsinline', '')\n      player.setAttribute('x5-playsinline', '')\n    }\n  }\n\n  removeListeners (player, url) {\n    player.removeEventListener('canplay', this.onReady)\n    player.removeEventListener('play', this.onPlay)\n    player.removeEventListener('waiting', this.onBuffer)\n    player.removeEventListener('playing', this.onBufferEnd)\n    player.removeEventListener('pause', this.onPause)\n    player.removeEventListener('seeked', this.onSeek)\n    player.removeEventListener('ended', this.onEnded)\n    player.removeEventListener('error', this.onError)\n    player.removeEventListener('ratechange', this.onPlayBackRateChange)\n    player.removeEventListener('enterpictureinpicture', this.onEnablePIP)\n    player.removeEventListener('leavepictureinpicture', this.onDisablePIP)\n    player.removeEventListener('webkitpresentationmodechanged', this.onPresentationModeChange)\n    if (!this.shouldUseHLS(url)) { // onReady is handled by hls.js\n      player.removeEventListener('canplay', this.onReady)\n    }\n  }\n\n  // Proxy methods to prevent listener leaks\n  onReady = (...args) => this.props.onReady(...args)\n  onPlay = (...args) => this.props.onPlay(...args)\n  onBuffer = (...args) => this.props.onBuffer(...args)\n  onBufferEnd = (...args) => this.props.onBufferEnd(...args)\n  onPause = (...args) => this.props.onPause(...args)\n  onEnded = (...args) => this.props.onEnded(...args)\n  onError = (...args) => this.props.onError(...args)\n  onPlayBackRateChange = (event) => this.props.onPlaybackRateChange(event.target.playbackRate)\n  onEnablePIP = (...args) => this.props.onEnablePIP(...args)\n\n  onDisablePIP = e => {\n    const { onDisablePIP, playing } = this.props\n    onDisablePIP(e)\n    if (playing) {\n      this.play()\n    }\n  }\n\n  onPresentationModeChange = e => {\n    if (this.player && supportsWebKitPresentationMode(this.player)) {\n      const { webkitPresentationMode } = this.player\n      if (webkitPresentationMode === 'picture-in-picture') {\n        this.onEnablePIP(e)\n      } else if (webkitPresentationMode === 'inline') {\n        this.onDisablePIP(e)\n      }\n    }\n  }\n\n  onSeek = e => {\n    this.props.onSeek(e.target.currentTime)\n  }\n\n  shouldUseAudio (props) {\n    if (props.config.forceVideo) {\n      return false\n    }\n    if (props.config.attributes.poster) {\n      return false // Use <video> so that poster is shown\n    }\n    return AUDIO_EXTENSIONS.test(props.url) || props.config.forceAudio\n  }\n\n  shouldUseHLS (url) {\n    if ((IS_SAFARI && this.props.config.forceSafariHLS) || this.props.config.forceHLS) {\n      return true\n    }\n    if (IS_IOS || this.props.config.forceDisableHls) {\n      return false\n    }\n    return HLS_EXTENSIONS.test(url) || MATCH_CLOUDFLARE_STREAM.test(url)\n  }\n\n  shouldUseDASH (url) {\n    return DASH_EXTENSIONS.test(url) || this.props.config.forceDASH\n  }\n\n  shouldUseFLV (url) {\n    return FLV_EXTENSIONS.test(url) || this.props.config.forceFLV\n  }\n\n  load (url) {\n    const { hlsVersion, hlsOptions, dashVersion, flvVersion } = this.props.config\n    if (this.hls) {\n      this.hls.destroy()\n    }\n    if (this.dash) {\n      this.dash.reset()\n    }\n    if (this.shouldUseHLS(url)) {\n      getSDK(HLS_SDK_URL.replace('VERSION', hlsVersion), HLS_GLOBAL).then(Hls => {\n        this.hls = new Hls(hlsOptions)\n        this.hls.on(Hls.Events.MANIFEST_PARSED, () => {\n          this.props.onReady()\n        })\n        this.hls.on(Hls.Events.ERROR, (e, data) => {\n          this.props.onError(e, data, this.hls, Hls)\n        })\n        if (MATCH_CLOUDFLARE_STREAM.test(url)) {\n          const id = url.match(MATCH_CLOUDFLARE_STREAM)[1]\n          this.hls.loadSource(REPLACE_CLOUDFLARE_STREAM.replace('{id}', id))\n        } else {\n          this.hls.loadSource(url)\n        }\n        this.hls.attachMedia(this.player)\n        this.props.onLoaded()\n      })\n    }\n    if (this.shouldUseDASH(url)) {\n      getSDK(DASH_SDK_URL.replace('VERSION', dashVersion), DASH_GLOBAL).then(dashjs => {\n        this.dash = dashjs.MediaPlayer().create()\n        this.dash.initialize(this.player, url, this.props.playing)\n        this.dash.on('error', this.props.onError)\n        if (parseInt(dashVersion) < 3) {\n          this.dash.getDebug().setLogToBrowserConsole(false)\n        } else {\n          this.dash.updateSettings({ debug: { logLevel: dashjs.Debug.LOG_LEVEL_NONE } })\n        }\n        this.props.onLoaded()\n      })\n    }\n    if (this.shouldUseFLV(url)) {\n      getSDK(FLV_SDK_URL.replace('VERSION', flvVersion), FLV_GLOBAL).then(flvjs => {\n        this.flv = flvjs.createPlayer({ type: 'flv', url })\n        this.flv.attachMediaElement(this.player)\n        this.flv.on(flvjs.Events.ERROR, (e, data) => {\n          this.props.onError(e, data, this.flv, flvjs)\n        })\n        this.flv.load()\n        this.props.onLoaded()\n      })\n    }\n\n    if (url instanceof Array) {\n      // When setting new urls (<source>) on an already loaded video,\n      // HTMLMediaElement.load() is needed to reset the media element\n      // and restart the media resource. Just replacing children source\n      // dom nodes is not enough\n      this.player.load()\n    } else if (isMediaStream(url)) {\n      try {\n        this.player.srcObject = url\n      } catch (e) {\n        this.player.src = window.URL.createObjectURL(url)\n      }\n    }\n  }\n\n  play () {\n    const promise = this.player.play()\n    if (promise) {\n      promise.catch(this.props.onError)\n    }\n  }\n\n  pause () {\n    this.player.pause()\n  }\n\n  stop () {\n    this.player.removeAttribute('src')\n    if (this.dash) {\n      this.dash.reset()\n    }\n  }\n\n  seekTo (seconds, keepPlaying = true) {\n    this.player.currentTime = seconds\n    if (!keepPlaying) {\n      this.pause()\n    }\n  }\n\n  setVolume (fraction) {\n    this.player.volume = fraction\n  }\n\n  mute = () => {\n    this.player.muted = true\n  }\n\n  unmute = () => {\n    this.player.muted = false\n  }\n\n  enablePIP () {\n    if (this.player.requestPictureInPicture && document.pictureInPictureElement !== this.player) {\n      this.player.requestPictureInPicture()\n    } else if (supportsWebKitPresentationMode(this.player) && this.player.webkitPresentationMode !== 'picture-in-picture') {\n      this.player.webkitSetPresentationMode('picture-in-picture')\n    }\n  }\n\n  disablePIP () {\n    if (document.exitPictureInPicture && document.pictureInPictureElement === this.player) {\n      document.exitPictureInPicture()\n    } else if (supportsWebKitPresentationMode(this.player) && this.player.webkitPresentationMode !== 'inline') {\n      this.player.webkitSetPresentationMode('inline')\n    }\n  }\n\n  setPlaybackRate (rate) {\n    try {\n      this.player.playbackRate = rate\n    } catch (error) {\n      this.props.onError(error)\n    }\n  }\n\n  getDuration () {\n    if (!this.player) return null\n    const { duration, seekable } = this.player\n    // on iOS, live streams return Infinity for the duration\n    // so instead we use the end of the seekable timerange\n    if (duration === Infinity && seekable.length > 0) {\n      return seekable.end(seekable.length - 1)\n    }\n    return duration\n  }\n\n  getCurrentTime () {\n    if (!this.player) return null\n    return this.player.currentTime\n  }\n\n  getSecondsLoaded () {\n    if (!this.player) return null\n    const { buffered } = this.player\n    if (buffered.length === 0) {\n      return 0\n    }\n    const end = buffered.end(buffered.length - 1)\n    const duration = this.getDuration()\n    if (end > duration) {\n      return duration\n    }\n    return end\n  }\n\n  getSource (url) {\n    const useHLS = this.shouldUseHLS(url)\n    const useDASH = this.shouldUseDASH(url)\n    const useFLV = this.shouldUseFLV(url)\n    if (url instanceof Array || isMediaStream(url) || useHLS || useDASH || useFLV) {\n      return undefined\n    }\n    if (MATCH_DROPBOX_URL.test(url)) {\n      return url.replace('www.dropbox.com', 'dl.dropboxusercontent.com')\n    }\n    return url\n  }\n\n  renderSourceElement = (source, index) => {\n    if (typeof source === 'string') {\n      return <source key={index} src={source} />\n    }\n    return <source key={index} {...source} />\n  }\n\n  renderTrack = (track, index) => {\n    return <track key={index} {...track} />\n  }\n\n  ref = player => {\n    if (this.player) {\n      // Store previous player to be used by removeListeners()\n      this.prevPlayer = this.player\n    }\n    this.player = player\n  }\n\n  render () {\n    const { url, playing, loop, controls, muted, config, width, height } = this.props\n    const useAudio = this.shouldUseAudio(this.props)\n    const Element = useAudio ? 'audio' : 'video'\n    const style = {\n      width: width === 'auto' ? width : '100%',\n      height: height === 'auto' ? height : '100%'\n    }\n    return (\n      <Element\n        ref={this.ref}\n        src={this.getSource(url)}\n        style={style}\n        preload='auto'\n        autoPlay={playing || undefined}\n        controls={controls}\n        muted={muted}\n        loop={loop}\n        {...config.attributes}\n      >\n        {url instanceof Array &&\n          url.map(this.renderSourceElement)}\n        {config.tracks.map(this.renderTrack)}\n      </Element>\n    )\n  }\n}\n", "/* global Map:readonly, Set:readonly, ArrayBuffer:readonly */\n\nvar hasElementType = typeof Element !== 'undefined';\nvar hasMap = typeof Map === 'function';\nvar hasSet = typeof Set === 'function';\nvar hasArrayBuffer = typeof ArrayBuffer === 'function' && !!ArrayBuffer.isView;\n\n// Note: We **don't** need `envHasBigInt64Array` in fde es6/index.js\n\nfunction equal(a, b) {\n  // START: fast-deep-equal es6/index.js 3.1.3\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    // START: Modifications:\n    // 1. Extra `has<Type> &&` helpers in initial condition allow es6 code\n    //    to co-exist with es5.\n    // 2. Replace `for of` with es5 compliant iteration using `for`.\n    //    Basically, take:\n    //\n    //    ```js\n    //    for (i of a.entries())\n    //      if (!b.has(i[0])) return false;\n    //    ```\n    //\n    //    ... and convert to:\n    //\n    //    ```js\n    //    it = a.entries();\n    //    while (!(i = it.next()).done)\n    //      if (!b.has(i.value[0])) return false;\n    //    ```\n    //\n    //    **Note**: `i` access switches to `i.value`.\n    var it;\n    if (hasMap && (a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!equal(i.value[1], b.get(i.value[0]))) return false;\n      return true;\n    }\n\n    if (hasSet && (a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      return true;\n    }\n    // END: Modifications\n\n    if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    // START: Modifications:\n    // Apply guards for `Object.create(null)` handling. See:\n    // - https://github.com/FormidableLabs/react-fast-compare/issues/64\n    // - https://github.com/epoberezkin/fast-deep-equal/issues/49\n    if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === 'function' && typeof b.valueOf === 'function') return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString && typeof a.toString === 'function' && typeof b.toString === 'function') return a.toString() === b.toString();\n    // END: Modifications\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    // END: fast-deep-equal\n\n    // START: react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element) return false;\n\n    // custom handling for React/Preact\n    for (i = length; i-- !== 0;) {\n      if ((keys[i] === '_owner' || keys[i] === '__v' || keys[i] === '__o') && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner\n        // Preact-specific: avoid traversing Preact elements' __v and __o\n        //    __v = $_original / $_vnode\n        //    __o = $_owner\n        // These properties contain circular references and are not needed when\n        // comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of elements\n\n        continue;\n      }\n\n      // all other properties should be traversed as usual\n      if (!equal(a[keys[i]], b[keys[i]])) return false;\n    }\n    // END: react-fast-compare\n\n    // START: fast-deep-equal\n    return true;\n  }\n\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function isEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if (((error.message || '').match(/stack|recursion/i))) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('react-fast-compare cannot handle circular refs');\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n\nfunction emptyFunction() {}\nfunction emptyFunctionWithReset() {}\nemptyFunctionWithReset.resetWarningCache = emptyFunction;\n\nmodule.exports = function() {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n    var err = new Error(\n      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n      'Use PropTypes.checkPropTypes() to call them. ' +\n      'Read more at http://fb.me/use-check-prop-types'\n    );\n    err.name = 'Invariant Violation';\n    throw err;\n  };\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  };\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  var ReactPropTypes = {\n    array: shim,\n    bigint: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    elementType: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim,\n    exact: getShim,\n\n    checkPropTypes: emptyFunctionWithReset,\n    resetWarningCache: emptyFunction\n  };\n\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "import React, { Component } from 'react'\n\nconst ICON_SIZE = '64px'\n\nconst cache = {}\n\nexport default class Preview extends Component {\n  mounted = false\n  state = {\n    image: null\n  }\n\n  componentDidMount () {\n    this.mounted = true\n    this.fetchImage(this.props)\n  }\n\n  componentDidUpdate (prevProps) {\n    const { url, light } = this.props\n    if (prevProps.url !== url || prevProps.light !== light) {\n      this.fetchImage(this.props)\n    }\n  }\n\n  componentWillUnmount () {\n    this.mounted = false\n  }\n\n  fetchImage ({ url, light, oEmbedUrl }) {\n    if (React.isValidElement(light)) {\n      return\n    }\n    if (typeof light === 'string') {\n      this.setState({ image: light })\n      return\n    }\n    if (cache[url]) {\n      this.setState({ image: cache[url] })\n      return\n    }\n    this.setState({ image: null })\n    return window.fetch(oEmbedUrl.replace('{url}', url))\n      .then(response => response.json())\n      .then(data => {\n        if (data.thumbnail_url && this.mounted) {\n          const image = data.thumbnail_url.replace('height=100', 'height=480').replace('-d_295x166', '-d_640')\n          this.setState({ image })\n          cache[url] = image\n        }\n      })\n  }\n\n  handleKeyPress = e => {\n    if (e.key === 'Enter' || e.key === ' ') {\n      this.props.onClick()\n    }\n  }\n\n  render () {\n    const { light, onClick, playIcon, previewTabIndex, previewAriaLabel } = this.props\n    const { image } = this.state\n    const isElement = React.isValidElement(light)\n    const flexCenter = {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    }\n    const styles = {\n      preview: {\n        width: '100%',\n        height: '100%',\n        backgroundImage: image && !isElement ? `url(${image})` : undefined,\n        backgroundSize: 'cover',\n        backgroundPosition: 'center',\n        cursor: 'pointer',\n        ...flexCenter\n      },\n      shadow: {\n        background: 'radial-gradient(rgb(0, 0, 0, 0.3), rgba(0, 0, 0, 0) 60%)',\n        borderRadius: ICON_SIZE,\n        width: ICON_SIZE,\n        height: ICON_SIZE,\n        position: isElement ? 'absolute' : undefined,\n        ...flexCenter\n      },\n      playIcon: {\n        borderStyle: 'solid',\n        borderWidth: '16px 0 16px 26px',\n        borderColor: 'transparent transparent transparent white',\n        marginLeft: '7px'\n      }\n    }\n    const defaultPlayIcon = (\n      <div style={styles.shadow} className='react-player__shadow'>\n        <div style={styles.playIcon} className='react-player__play-icon' />\n      </div>\n    )\n    return (\n      <div\n        style={styles.preview}\n        className='react-player__preview'\n        onClick={onClick}\n        tabIndex={previewTabIndex}\n        onKeyPress={this.handleKeyPress}\n        {...(previewAriaLabel ? { 'aria-label': previewAriaLabel } : {})}\n      >\n        {isElement ? light : null}\n        {playIcon || defaultPlayIcon}\n      </div>\n    )\n  }\n}\n", "import players from './players'\nimport { createReactPlayer } from './ReactPlayer'\n\n// Fall back to FilePlayer if nothing else can play the URL\nconst fallback = players[players.length - 1]\n\nexport default createReactPlayer(players, fallback)\n", "import { lazy, supportsWebKitPresentationMode } from '../utils'\nimport { canPlay, AUDIO_EXTENSIONS } from '../patterns'\n\nexport default [\n  {\n    key: 'youtube',\n    name: 'YouTube',\n    canPlay: canPlay.youtube,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerYouTube' */'./YouTube'))\n  },\n  {\n    key: 'soundcloud',\n    name: 'SoundCloud',\n    canPlay: canPlay.soundcloud,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerSoundCloud' */'./SoundCloud'))\n  },\n  {\n    key: 'vimeo',\n    name: 'Vimeo',\n    canPlay: canPlay.vimeo,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerVimeo' */'./Vimeo'))\n  },\n  {\n    key: 'mux',\n    name: 'Mux',\n    canPlay: canPlay.mux,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerMux' */'./Mux'))\n  },\n  {\n    key: 'facebook',\n    name: 'Facebook',\n    canPlay: canPlay.facebook,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerFacebook' */'./Facebook'))\n  },\n  {\n    key: 'streamable',\n    name: 'Streamable',\n    canPlay: canPlay.streamable,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerStreamable' */'./Streamable'))\n  },\n  {\n    key: 'wistia',\n    name: 'Wistia',\n    canPlay: canPlay.wistia,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerWistia' */'./Wistia'))\n  },\n  {\n    key: 'twitch',\n    name: 'Twitch',\n    canPlay: canPlay.twitch,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerTwitch' */'./Twitch'))\n  },\n  {\n    key: 'dailymotion',\n    name: 'DailyMotion',\n    canPlay: canPlay.dailymotion,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerDailyMotion' */'./DailyMotion'))\n  },\n  {\n    key: 'mixcloud',\n    name: 'Mixcloud',\n    canPlay: canPlay.mixcloud,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerMixcloud' */'./Mixcloud'))\n  },\n  {\n    key: 'vidyard',\n    name: 'Vidyard',\n    canPlay: canPlay.vidyard,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerVidyard' */'./Vidyard'))\n  },\n  {\n    key: 'kaltura',\n    name: 'Kaltura',\n    canPlay: canPlay.kaltura,\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerKaltura' */'./Kaltura'))\n  },\n  {\n    key: 'file',\n    name: 'FilePlayer',\n    canPlay: canPlay.file,\n    canEnablePIP: url => {\n      return canPlay.file(url) && (document.pictureInPictureEnabled || supportsWebKitPresentationMode()) && !AUDIO_EXTENSIONS.test(url)\n    },\n    lazyPlayer: lazy(() => import(/* webpackChunkName: 'reactPlayerFilePlayer' */'./FilePlayer'))\n  }\n]\n", "import React, { Component, Suspense } from 'react'\nimport merge from 'deepmerge'\nimport memoize from 'memoize-one'\nimport isEqual from 'react-fast-compare'\n\nimport { propTypes, defaultProps } from './props'\nimport { omit, lazy } from './utils'\nimport Player from './Player'\n\nconst Preview = lazy(() => import(/* webpackChunkName: 'reactPlayerPreview' */'./Preview'))\n\nconst IS_BROWSER = typeof window !== 'undefined' && window.document && typeof document !== 'undefined'\nconst IS_GLOBAL = typeof global !== 'undefined' && global.window && global.window.document\nconst SUPPORTED_PROPS = Object.keys(propTypes)\n\n// Return null when rendering on the server\n// as Suspense is not supported yet\nconst UniversalSuspense = IS_BROWSER || IS_GLOBAL ? Suspense : () => null\n\nconst customPlayers = []\n\nexport const createReactPlayer = (players, fallback) => {\n  return class ReactPlayer extends Component {\n    static displayName = 'ReactPlayer'\n    static propTypes = propTypes\n    static defaultProps = defaultProps\n    static addCustomPlayer = player => { customPlayers.push(player) }\n    static removeCustomPlayers = () => { customPlayers.length = 0 }\n\n    static canPlay = url => {\n      for (const Player of [...customPlayers, ...players]) {\n        if (Player.canPlay(url)) {\n          return true\n        }\n      }\n      return false\n    }\n\n    static canEnablePIP = url => {\n      for (const Player of [...customPlayers, ...players]) {\n        if (Player.canEnablePIP && Player.canEnablePIP(url)) {\n          return true\n        }\n      }\n      return false\n    }\n\n    state = {\n      showPreview: !!this.props.light\n    }\n\n    // Use references, as refs is used by React\n    references = {\n      wrapper: wrapper => { this.wrapper = wrapper },\n      player: player => { this.player = player }\n    }\n\n    shouldComponentUpdate (nextProps, nextState) {\n      return !isEqual(this.props, nextProps) || !isEqual(this.state, nextState)\n    }\n\n    componentDidUpdate (prevProps) {\n      const { light } = this.props\n      if (!prevProps.light && light) {\n        this.setState({ showPreview: true })\n      }\n      if (prevProps.light && !light) {\n        this.setState({ showPreview: false })\n      }\n    }\n\n    handleClickPreview = (e) => {\n      this.setState({ showPreview: false })\n      this.props.onClickPreview(e)\n    }\n\n    showPreview = () => {\n      this.setState({ showPreview: true })\n    }\n\n    getDuration = () => {\n      if (!this.player) return null\n      return this.player.getDuration()\n    }\n\n    getCurrentTime = () => {\n      if (!this.player) return null\n      return this.player.getCurrentTime()\n    }\n\n    getSecondsLoaded = () => {\n      if (!this.player) return null\n      return this.player.getSecondsLoaded()\n    }\n\n    getInternalPlayer = (key = 'player') => {\n      if (!this.player) return null\n      return this.player.getInternalPlayer(key)\n    }\n\n    seekTo = (fraction, type, keepPlaying) => {\n      if (!this.player) return null\n      this.player.seekTo(fraction, type, keepPlaying)\n    }\n\n    handleReady = () => {\n      this.props.onReady(this)\n    }\n\n    getActivePlayer = memoize(url => {\n      for (const player of [...customPlayers, ...players]) {\n        if (player.canPlay(url)) {\n          return player\n        }\n      }\n      if (fallback) {\n        return fallback\n      }\n      return null\n    })\n\n    getConfig = memoize((url, key) => {\n      const { config } = this.props\n      return merge.all([\n        defaultProps.config,\n        defaultProps.config[key] || {},\n        config,\n        config[key] || {}\n      ])\n    })\n\n    getAttributes = memoize(url => {\n      return omit(this.props, SUPPORTED_PROPS)\n    })\n\n    renderPreview (url) {\n      if (!url) return null\n      const { light, playIcon, previewTabIndex, oEmbedUrl, previewAriaLabel } = this.props\n      return (\n        <Preview\n          url={url}\n          light={light}\n          playIcon={playIcon}\n          previewTabIndex={previewTabIndex}\n          previewAriaLabel={previewAriaLabel}\n          oEmbedUrl={oEmbedUrl}\n          onClick={this.handleClickPreview}\n        />\n      )\n    }\n\n    renderActivePlayer = url => {\n      if (!url) return null\n      const player = this.getActivePlayer(url)\n      if (!player) return null\n      const config = this.getConfig(url, player.key)\n      return (\n        <Player\n          {...this.props}\n          key={player.key}\n          ref={this.references.player}\n          config={config}\n          activePlayer={player.lazyPlayer || player}\n          onReady={this.handleReady}\n        />\n      )\n    }\n\n    render () {\n      const { url, style, width, height, fallback, wrapper: Wrapper } = this.props\n      const { showPreview } = this.state\n      const attributes = this.getAttributes(url)\n      const wrapperRef = typeof Wrapper === 'string' ? this.references.wrapper : undefined\n      return (\n        <Wrapper ref={wrapperRef} style={{ ...style, width, height }} {...attributes}>\n          <UniversalSuspense fallback={fallback}>\n            {showPreview\n              ? this.renderPreview(url)\n              : this.renderActivePlayer(url)}\n          </UniversalSuspense>\n        </Wrapper>\n      )\n    }\n  }\n}\n", "var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var lastThis;\n    var lastArgs = [];\n    var lastResult;\n    var calledOnce = false;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (calledOnce && lastThis === this && isEqual(newArgs, lastArgs)) {\n            return lastResult;\n        }\n        lastResult = resultFn.apply(this, newArgs);\n        calledOnce = true;\n        lastThis = this;\n        lastArgs = newArgs;\n        return lastResult;\n    }\n    return memoized;\n}\n\nexport default memoizeOne;\n", "import PropTypes from 'prop-types'\n\nconst { string, bool, number, array, oneOfType, shape, object, func, node } = PropTypes\n\nexport const propTypes = {\n  url: oneOfType([string, array, object]),\n  playing: bool,\n  loop: bool,\n  controls: bool,\n  volume: number,\n  muted: bool,\n  playbackRate: number,\n  width: oneOfType([string, number]),\n  height: oneOfType([string, number]),\n  style: object,\n  progressInterval: number,\n  playsinline: bool,\n  pip: bool,\n  stopOnUnmount: bool,\n  light: oneOfType([bool, string, object]),\n  playIcon: node,\n  previewTabIndex: number,\n  previewAriaLabel: string,\n  fallback: node,\n  oEmbedUrl: string,\n  wrapper: oneOfType([\n    string,\n    func,\n    shape({ render: func.isRequired })\n  ]),\n  config: shape({\n    soundcloud: shape({\n      options: object\n    }),\n    youtube: shape({\n      playerVars: object,\n      embedOptions: object,\n      onUnstarted: func\n    }),\n    facebook: shape({\n      appId: string,\n      version: string,\n      playerId: string,\n      attributes: object\n    }),\n    dailymotion: shape({\n      params: object\n    }),\n    vimeo: shape({\n      playerOptions: object,\n      title: string\n    }),\n    mux: shape({\n      attributes: object,\n      version: string\n    }),\n    file: shape({\n      attributes: object,\n      tracks: array,\n      forceVideo: bool,\n      forceAudio: bool,\n      forceHLS: bool,\n      forceSafariHLS: bool,\n      forceDisableHls: bool,\n      forceDASH: bool,\n      forceFLV: bool,\n      hlsOptions: object,\n      hlsVersion: string,\n      dashVersion: string,\n      flvVersion: string\n    }),\n    wistia: shape({\n      options: object,\n      playerId: string,\n      customControls: array\n    }),\n    mixcloud: shape({\n      options: object\n    }),\n    twitch: shape({\n      options: object,\n      playerId: string\n    }),\n    vidyard: shape({\n      options: object\n    })\n  }),\n  onReady: func,\n  onStart: func,\n  onPlay: func,\n  onPause: func,\n  onBuffer: func,\n  onBufferEnd: func,\n  onEnded: func,\n  onError: func,\n  onDuration: func,\n  onSeek: func,\n  onPlaybackRateChange: func,\n  onPlaybackQualityChange: func,\n  onProgress: func,\n  onClickPreview: func,\n  onEnablePIP: func,\n  onDisablePIP: func\n}\n\nconst noop = () => {}\n\nexport const defaultProps = {\n  playing: false,\n  loop: false,\n  controls: false,\n  volume: null,\n  muted: false,\n  playbackRate: 1,\n  width: '640px',\n  height: '360px',\n  style: {},\n  progressInterval: 1000,\n  playsinline: false,\n  pip: false,\n  stopOnUnmount: true,\n  light: false,\n  fallback: null,\n  wrapper: 'div',\n  previewTabIndex: 0,\n  previewAriaLabel: '',\n  oEmbedUrl: 'https://noembed.com/embed?url={url}',\n  config: {\n    soundcloud: {\n      options: {\n        visual: true, // Undocumented, but makes player fill container and look better\n        buying: false,\n        liking: false,\n        download: false,\n        sharing: false,\n        show_comments: false,\n        show_playcount: false\n      }\n    },\n    youtube: {\n      playerVars: {\n        playsinline: 1,\n        showinfo: 0,\n        rel: 0,\n        iv_load_policy: 3,\n        modestbranding: 1\n      },\n      embedOptions: {},\n      onUnstarted: noop\n    },\n    facebook: {\n      appId: '1309697205772819',\n      version: 'v3.3',\n      playerId: null,\n      attributes: {}\n    },\n    dailymotion: {\n      params: {\n        api: 1,\n        'endscreen-enable': false\n      }\n    },\n    vimeo: {\n      playerOptions: {\n        autopause: false,\n        byline: false,\n        portrait: false,\n        title: false\n      },\n      title: null\n    },\n    mux: {\n      attributes: {},\n      version: '2'\n    },\n    file: {\n      attributes: {},\n      tracks: [],\n      forceVideo: false,\n      forceAudio: false,\n      forceHLS: false,\n      forceDASH: false,\n      forceFLV: false,\n      hlsOptions: {},\n      hlsVersion: '1.1.4',\n      dashVersion: '3.1.3',\n      flvVersion: '1.5.0',\n      forceDisableHls: false\n    },\n    wistia: {\n      options: {},\n      playerId: null,\n      customControls: null\n    },\n    mixcloud: {\n      options: {\n        hide_cover: 1\n      }\n    },\n    twitch: {\n      options: {},\n      playerId: null\n    },\n    vidyard: {\n      options: {}\n    }\n  },\n  onReady: noop,\n  onStart: noop,\n  onPlay: noop,\n  onPause: noop,\n  onBuffer: noop,\n  onBufferEnd: noop,\n  onEnded: noop,\n  onError: noop,\n  onDuration: noop,\n  onSeek: noop,\n  onPlaybackRateChange: noop,\n  onPlaybackQualityChange: noop,\n  onProgress: noop,\n  onClickPreview: noop,\n  onEnablePIP: noop,\n  onDisablePIP: noop\n}\n", "import React, { Component } from 'react'\nimport isEqual from 'react-fast-compare'\n\nimport { propTypes, defaultProps } from './props'\nimport { isMediaStream } from './utils'\n\nconst SEEK_ON_PLAY_EXPIRY = 5000\n\nexport default class Player extends Component {\n  static displayName = 'Player'\n  static propTypes = propTypes\n  static defaultProps = defaultProps\n\n  mounted = false\n  isReady = false\n  isPlaying = false // Track playing state internally to prevent bugs\n  isLoading = true // Use isLoading to prevent onPause when switching URL\n  loadOnReady = null\n  startOnPlay = true\n  seekOnPlay = null\n  onDurationCalled = false\n\n  componentDidMount () {\n    this.mounted = true\n  }\n\n  componentWillUnmount () {\n    clearTimeout(this.progressTimeout)\n    clearTimeout(this.durationCheckTimeout)\n    if (this.isReady && this.props.stopOnUnmount) {\n      this.player.stop()\n\n      if (this.player.disablePIP) {\n        this.player.disablePIP()\n      }\n    }\n    this.mounted = false\n  }\n\n  componentDidUpdate (prevProps) {\n    // If there isn’t a player available, don’t do anything\n    if (!this.player) {\n      return\n    }\n    // Invoke player methods based on changed props\n    const { url, playing, volume, muted, playbackRate, pip, loop, activePlayer, disableDeferredLoading } = this.props\n    if (!isEqual(prevProps.url, url)) {\n      if (this.isLoading && !activePlayer.forceLoad && !disableDeferredLoading && !isMediaStream(url)) {\n        console.warn(`ReactPlayer: the attempt to load ${url} is being deferred until the player has loaded`)\n        this.loadOnReady = url\n        return\n      }\n      this.isLoading = true\n      this.startOnPlay = true\n      this.onDurationCalled = false\n      this.player.load(url, this.isReady)\n    }\n    if (!prevProps.playing && playing && !this.isPlaying) {\n      this.player.play()\n    }\n    if (prevProps.playing && !playing && this.isPlaying) {\n      this.player.pause()\n    }\n    if (!prevProps.pip && pip && this.player.enablePIP) {\n      this.player.enablePIP()\n    }\n    if (prevProps.pip && !pip && this.player.disablePIP) {\n      this.player.disablePIP()\n    }\n    if (prevProps.volume !== volume && volume !== null) {\n      this.player.setVolume(volume)\n    }\n    if (prevProps.muted !== muted) {\n      if (muted) {\n        this.player.mute()\n      } else {\n        this.player.unmute()\n        if (volume !== null) {\n          // Set volume next tick to fix a bug with DailyMotion\n          setTimeout(() => this.player.setVolume(volume))\n        }\n      }\n    }\n    if (prevProps.playbackRate !== playbackRate && this.player.setPlaybackRate) {\n      this.player.setPlaybackRate(playbackRate)\n    }\n    if (prevProps.loop !== loop && this.player.setLoop) {\n      this.player.setLoop(loop)\n    }\n  }\n\n  handlePlayerMount = player => {\n    if (this.player) {\n      this.progress() // Ensure onProgress is still called in strict mode\n      return // Return here to prevent loading twice in strict mode\n    }\n    this.player = player\n    this.player.load(this.props.url)\n    this.progress()\n  }\n\n  getDuration () {\n    if (!this.isReady) return null\n    return this.player.getDuration()\n  }\n\n  getCurrentTime () {\n    if (!this.isReady) return null\n    return this.player.getCurrentTime()\n  }\n\n  getSecondsLoaded () {\n    if (!this.isReady) return null\n    return this.player.getSecondsLoaded()\n  }\n\n  getInternalPlayer = (key) => {\n    if (!this.player) return null\n    return this.player[key]\n  }\n\n  progress = () => {\n    if (this.props.url && this.player && this.isReady) {\n      const playedSeconds = this.getCurrentTime() || 0\n      const loadedSeconds = this.getSecondsLoaded()\n      const duration = this.getDuration()\n      if (duration) {\n        const progress = {\n          playedSeconds,\n          played: playedSeconds / duration\n        }\n        if (loadedSeconds !== null) {\n          progress.loadedSeconds = loadedSeconds\n          progress.loaded = loadedSeconds / duration\n        }\n        // Only call onProgress if values have changed\n        if (progress.playedSeconds !== this.prevPlayed || progress.loadedSeconds !== this.prevLoaded) {\n          this.props.onProgress(progress)\n        }\n        this.prevPlayed = progress.playedSeconds\n        this.prevLoaded = progress.loadedSeconds\n      }\n    }\n    this.progressTimeout = setTimeout(this.progress, this.props.progressFrequency || this.props.progressInterval)\n  }\n\n  seekTo (amount, type, keepPlaying) {\n    // When seeking before player is ready, store value and seek later\n    if (!this.isReady) {\n      if (amount !== 0) {\n        this.seekOnPlay = amount\n        setTimeout(() => { this.seekOnPlay = null }, SEEK_ON_PLAY_EXPIRY)\n      }\n      return\n    }\n    const isFraction = !type ? (amount > 0 && amount < 1) : type === 'fraction'\n    if (isFraction) {\n      // Convert fraction to seconds based on duration\n      const duration = this.player.getDuration()\n      if (!duration) {\n        console.warn('ReactPlayer: could not seek using fraction – duration not yet available')\n        return\n      }\n      this.player.seekTo(duration * amount, keepPlaying)\n      return\n    }\n    this.player.seekTo(amount, keepPlaying)\n  }\n\n  handleReady = () => {\n    if (!this.mounted) return\n    this.isReady = true\n    this.isLoading = false\n    const { onReady, playing, volume, muted } = this.props\n    onReady()\n    if (!muted && volume !== null) {\n      this.player.setVolume(volume)\n    }\n    if (this.loadOnReady) {\n      this.player.load(this.loadOnReady, true)\n      this.loadOnReady = null\n    } else if (playing) {\n      this.player.play()\n    }\n    this.handleDurationCheck()\n  }\n\n  handlePlay = () => {\n    this.isPlaying = true\n    this.isLoading = false\n    const { onStart, onPlay, playbackRate } = this.props\n    if (this.startOnPlay) {\n      if (this.player.setPlaybackRate && playbackRate !== 1) {\n        this.player.setPlaybackRate(playbackRate)\n      }\n      onStart()\n      this.startOnPlay = false\n    }\n    onPlay()\n    if (this.seekOnPlay) {\n      this.seekTo(this.seekOnPlay)\n      this.seekOnPlay = null\n    }\n    this.handleDurationCheck()\n  }\n\n  handlePause = (e) => {\n    this.isPlaying = false\n    if (!this.isLoading) {\n      this.props.onPause(e)\n    }\n  }\n\n  handleEnded = () => {\n    const { activePlayer, loop, onEnded } = this.props\n    if (activePlayer.loopOnEnded && loop) {\n      this.seekTo(0)\n    }\n    if (!loop) {\n      this.isPlaying = false\n      onEnded()\n    }\n  }\n\n  handleError = (...args) => {\n    this.isLoading = false\n    this.props.onError(...args)\n  }\n\n  handleDurationCheck = () => {\n    clearTimeout(this.durationCheckTimeout)\n    const duration = this.getDuration()\n    if (duration) {\n      if (!this.onDurationCalled) {\n        this.props.onDuration(duration)\n        this.onDurationCalled = true\n      }\n    } else {\n      this.durationCheckTimeout = setTimeout(this.handleDurationCheck, 100)\n    }\n  }\n\n  handleLoaded = () => {\n    // Sometimes we know loading has stopped but onReady/onPlay are never called\n    // so this provides a way for players to avoid getting stuck\n    this.isLoading = false\n  }\n\n  render () {\n    const Player = this.props.activePlayer\n    if (!Player) {\n      return null\n    }\n    return (\n      <Player\n        {...this.props}\n        onMount={this.handlePlayerMount}\n        onReady={this.handleReady}\n        onPlay={this.handlePlay}\n        onPause={this.handlePause}\n        onEnded={this.handleEnded}\n        onLoaded={this.handleLoaded}\n        onError={this.handleError}\n      />\n    )\n  }\n}\n"], "mappings": "k0BAAA,IAAAA,EAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAO,QAAU,WAAW,QCA5B,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CACAA,GAAO,QAAU,SAAeC,EAAKC,EAAMC,EAAI,CAC7C,IAAIC,EAAO,SAAS,MAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC,EAC/DC,EAAS,SAAS,cAAc,QAAQ,EAExC,OAAOH,GAAS,aAClBC,EAAKD,EACLA,EAAO,CAAC,GAGVA,EAAOA,GAAQ,CAAC,EAChBC,EAAKA,GAAM,UAAW,CAAC,EAEvBE,EAAO,KAAOH,EAAK,MAAQ,kBAC3BG,EAAO,QAAUH,EAAK,SAAW,OACjCG,EAAO,MAAQ,UAAWH,EAAO,CAAC,CAACA,EAAK,MAAQ,GAChDG,EAAO,IAAMJ,EAETC,EAAK,OACPI,GAAcD,EAAQH,EAAK,KAAK,EAG9BA,EAAK,OACPG,EAAO,KAAO,GAAKH,EAAK,MAG1B,IAAIK,EAAQ,WAAYF,EAASG,GAAWC,GAC5CF,EAAMF,EAAQF,CAAE,EAKXE,EAAO,QACVG,GAASH,EAAQF,CAAE,EAGrBC,EAAK,YAAYC,CAAM,CACzB,EAEA,SAASC,GAAcD,EAAQK,EAAO,CACpC,QAASC,KAAQD,EACfL,EAAO,aAAaM,EAAMD,EAAMC,CAAI,CAAC,CAEzC,CAEA,SAASH,GAAUH,EAAQF,EAAI,CAC7BE,EAAO,OAAS,UAAY,CAC1B,KAAK,QAAU,KAAK,OAAS,KAC7BF,EAAG,KAAME,CAAM,CACjB,EACAA,EAAO,QAAU,UAAY,CAG3B,KAAK,QAAU,KAAK,OAAS,KAC7BF,EAAG,IAAI,MAAM,kBAAoB,KAAK,GAAG,EAAGE,CAAM,CACpD,CACF,CAEA,SAASI,GAASJ,EAAQF,EAAI,CAC5BE,EAAO,mBAAqB,UAAY,CAClC,KAAK,YAAc,YAAc,KAAK,YAAc,WACxD,KAAK,mBAAqB,KAC1BF,EAAG,KAAME,CAAM,EACjB,CACF,IChEA,IAAAO,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAoB,SAA2BC,EAAO,CACzD,OAAOC,GAAgBD,CAAK,GACxB,CAACE,GAAUF,CAAK,CACrB,EAEA,SAASC,GAAgBD,EAAO,CAC/B,MAAO,CAAC,CAACA,GAAS,OAAOA,GAAU,QACpC,CAEA,SAASE,GAAUF,EAAO,CACzB,IAAIG,EAAc,OAAO,UAAU,SAAS,KAAKH,CAAK,EAEtD,OAAOG,IAAgB,mBACnBA,IAAgB,iBAChBC,GAAeJ,CAAK,CACzB,CAGA,IAAIK,GAAe,OAAO,QAAW,YAAc,OAAO,IACtDC,GAAqBD,GAAe,OAAO,IAAI,eAAe,EAAI,MAEtE,SAASD,GAAeJ,EAAO,CAC9B,OAAOA,EAAM,WAAaM,EAC3B,CAEA,SAASC,GAAYC,EAAK,CACzB,OAAO,MAAM,QAAQA,CAAG,EAAI,CAAC,EAAI,CAAC,CACnC,CAEA,SAASC,GAA8BT,EAAOU,EAAS,CACtD,OAAQA,EAAQ,QAAU,IAASA,EAAQ,kBAAkBV,CAAK,EAC/DW,EAAUJ,GAAYP,CAAK,EAAGA,EAAOU,CAAO,EAC5CV,CACJ,CAEA,SAASY,GAAkBC,EAAQC,EAAQJ,EAAS,CACnD,OAAOG,EAAO,OAAOC,CAAM,EAAE,IAAI,SAASC,EAAS,CAClD,OAAON,GAA8BM,EAASL,CAAO,CACtD,CAAC,CACF,CAEA,SAASM,GAAiBC,EAAKP,EAAS,CACvC,GAAI,CAACA,EAAQ,YACZ,OAAOC,EAER,IAAIO,EAAcR,EAAQ,YAAYO,CAAG,EACzC,OAAO,OAAOC,GAAgB,WAAaA,EAAcP,CAC1D,CAEA,SAASQ,GAAgCN,EAAQ,CAChD,OAAO,OAAO,sBACX,OAAO,sBAAsBA,CAAM,EAAE,OAAO,SAASO,EAAQ,CAC9D,OAAO,OAAO,qBAAqB,KAAKP,EAAQO,CAAM,CACvD,CAAC,EACC,CAAC,CACL,CAEA,SAASC,GAAQR,EAAQ,CACxB,OAAO,OAAO,KAAKA,CAAM,EAAE,OAAOM,GAAgCN,CAAM,CAAC,CAC1E,CAEA,SAASS,GAAmBC,EAAQC,EAAU,CAC7C,GAAI,CACH,OAAOA,KAAYD,CACpB,MAAW,CACV,MAAO,EACR,CACD,CAGA,SAASE,GAAiBZ,EAAQI,EAAK,CACtC,OAAOK,GAAmBT,EAAQI,CAAG,GACjC,EAAE,OAAO,eAAe,KAAKJ,EAAQI,CAAG,GACvC,OAAO,qBAAqB,KAAKJ,EAAQI,CAAG,EAClD,CAEA,SAASS,GAAYb,EAAQC,EAAQJ,EAAS,CAC7C,IAAIiB,EAAc,CAAC,EACnB,OAAIjB,EAAQ,kBAAkBG,CAAM,GACnCQ,GAAQR,CAAM,EAAE,QAAQ,SAASI,EAAK,CACrCU,EAAYV,CAAG,EAAIR,GAA8BI,EAAOI,CAAG,EAAGP,CAAO,CACtE,CAAC,EAEFW,GAAQP,CAAM,EAAE,QAAQ,SAASG,EAAK,CACjCQ,GAAiBZ,EAAQI,CAAG,IAI5BK,GAAmBT,EAAQI,CAAG,GAAKP,EAAQ,kBAAkBI,EAAOG,CAAG,CAAC,EAC3EU,EAAYV,CAAG,EAAID,GAAiBC,EAAKP,CAAO,EAAEG,EAAOI,CAAG,EAAGH,EAAOG,CAAG,EAAGP,CAAO,EAEnFiB,EAAYV,CAAG,EAAIR,GAA8BK,EAAOG,CAAG,EAAGP,CAAO,EAEvE,CAAC,EACMiB,CACR,CAEA,SAAShB,EAAUE,EAAQC,EAAQJ,EAAS,CAC3CA,EAAUA,GAAW,CAAC,EACtBA,EAAQ,WAAaA,EAAQ,YAAcE,GAC3CF,EAAQ,kBAAoBA,EAAQ,mBAAqBX,GAGzDW,EAAQ,8BAAgCD,GAExC,IAAImB,EAAgB,MAAM,QAAQd,CAAM,EACpCe,EAAgB,MAAM,QAAQhB,CAAM,EACpCiB,EAA4BF,IAAkBC,EAElD,OAAKC,EAEMF,EACHlB,EAAQ,WAAWG,EAAQC,EAAQJ,CAAO,EAE1CgB,GAAYb,EAAQC,EAAQJ,CAAO,EAJnCD,GAA8BK,EAAQJ,CAAO,CAMtD,CAEAC,EAAU,IAAM,SAAsBoB,EAAOrB,EAAS,CACrD,GAAI,CAAC,MAAM,QAAQqB,CAAK,EACvB,MAAM,IAAI,MAAM,mCAAmC,EAGpD,OAAOA,EAAM,OAAO,SAASC,EAAMC,EAAM,CACxC,OAAOtB,EAAUqB,EAAMC,EAAMvB,CAAO,CACrC,EAAG,CAAC,CAAC,CACN,EAEA,IAAIwB,GAAcvB,EAElBb,GAAO,QAAUoC,KCjHjB,SAASC,GAAgBC,EAAKC,EAAS,CACrC,GAAID,aAAe,MACjB,OAEF,IAAME,EAAQF,EAAI,MAAMC,CAAO,EAC/B,GAAIC,EAAO,CACT,IAAMC,EAAQD,EAAM,CAAC,EACrB,GAAIC,EAAM,MAAMC,EAAiB,EAC/B,OAAOC,GAAgBF,CAAK,EAE9B,GAAIG,GAAc,KAAKH,CAAK,EAC1B,OAAO,SAASA,CAAK,CAEzB,CAEF,CAEA,SAASE,GAAiBF,EAAO,CAC/B,IAAII,EAAU,EACVC,EAAQJ,GAAkB,KAAKD,CAAK,EACxC,KAAOK,IAAU,MAAM,CACrB,GAAM,CAAC,CAAEC,EAAOC,CAAM,EAAIF,EACtBE,IAAW,MAAKH,GAAW,SAASE,EAAO,EAAE,EAAI,GAAK,IACtDC,IAAW,MAAKH,GAAW,SAASE,EAAO,EAAE,EAAI,IACjDC,IAAW,MAAKH,GAAW,SAASE,EAAO,EAAE,GACjDD,EAAQJ,GAAkB,KAAKD,CAAK,CACtC,CACA,OAAOI,CACT,CAEO,SAASI,EAAgBX,EAAK,CACnC,OAAOD,GAAeC,EAAKY,EAAiB,CAC9C,CAEO,SAASC,GAAcb,EAAK,CACjC,OAAOD,GAAeC,EAAKc,EAAe,CAC5C,CAGO,SAASC,GAAgB,CAC9B,OAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAC/C,CAEO,SAASC,GAAaC,EAAQ,CACnC,OAAO,OACJ,KAAKA,CAAM,EACX,IAAIC,GAAO,GAAGA,CAAG,IAAID,EAAOC,CAAG,CAAC,EAAE,EAClC,KAAK,GAAG,CACb,CAEA,SAASC,GAAWD,EAAK,CACvB,OAAI,OAAOA,CAAG,EACL,OAAOA,CAAG,EAEf,OAAO,SAAW,OAAO,QAAQA,CAAG,EAC/B,OAAO,QAAQA,CAAG,EAEvB,OAAO,QAAU,OAAO,OAAO,SAAW,OAAO,OAAO,QAAQA,CAAG,EAC9D,OAAO,OAAO,QAAQA,CAAG,EAE3B,IACT,CA8CO,SAASE,GAAMH,KAAWI,EAAQ,CACvC,IAAMC,EAAW,CAAC,EAAE,OAAO,GAAGD,CAAM,EAC9BE,EAAS,CAAC,EACVC,EAAO,OAAO,KAAKP,CAAM,EAC/B,QAAWC,KAAOM,EACZF,EAAS,QAAQJ,CAAG,IAAM,KAC5BK,EAAOL,CAAG,EAAID,EAAOC,CAAG,GAG5B,OAAOK,CACT,CAEO,SAASE,EAAYC,KAAWC,EAAM,CAG3C,GAAI,CAAC,KAAK,QAAU,CAAC,KAAK,OAAOD,CAAM,EAAG,CACxC,IAAIE,EAAU,gBAAgB,KAAK,YAAY,WAAW,4BAA4BF,CAAM,aAC5F,OAAK,KAAK,OAEE,KAAK,OAAOA,CAAM,IAC5BE,GAAW,gCAFXA,GAAW,+BAIb,QAAQ,KAAKA,EAAS,oBAAqB,EAAE,EACtC,IACT,CACA,OAAO,KAAK,OAAOF,CAAM,EAAE,GAAGC,CAAI,CACpC,CAEO,SAASE,EAAe7B,EAAK,CAClC,OACE,OAAO,QAAW,aAClB,OAAO,OAAO,aAAgB,aAC9BA,aAAe,OAAO,WAE1B,CAEO,SAAS8B,GAAW9B,EAAK,CAC9B,MAAO,SAAS,KAAKA,CAAG,CAC1B,CAEO,SAAS+B,EAAgCC,EAAQ,SAAS,cAAc,OAAO,EAAG,CAGvF,IAAMC,EAAY,cAAc,KAAK,UAAU,SAAS,IAAM,GAC9D,OAAOD,EAAM,gCAAkC,OAAOA,EAAM,2BAA8B,YAAcC,CAC1G,CA3KA,IAAAC,GACAC,GACAC,GAKaC,EAKPzB,GACAE,GACAV,GACAE,GAqEAgC,EACOC,EArFbC,EAAAC,EAAA,KAAAP,GAAkB,OAClBC,GAAuB,QACvBC,GAAkB,QAKLC,EAAQK,GAAsB,GAAAC,QAAM,KAAK,SAAY,CAChE,IAAMC,EAAM,MAAMF,EAAkB,EACpC,OAAO,OAAOE,EAAI,SAAY,WAAaA,EAAMA,EAAI,OACvD,CAAC,EAEKhC,GAAoB,+BACpBE,GAAkB,uBAClBV,GAAoB,gBACpBE,GAAgB,QAqEhBgC,EAAW,CAAC,EACLC,EAAsB,SAAiBvC,EAAK6C,EAAWC,EAAW,KAAMC,EAAW,IAAM,GAAMC,EAAc,GAAAC,QAAY,CACpI,IAAMC,EAAiB/B,GAAU0B,CAAS,EAC1C,OAAIK,GAAkBH,EAASG,CAAc,EACpC,QAAQ,QAAQA,CAAc,EAEhC,IAAI,QAAQ,CAACC,EAASC,IAAW,CAGtC,GAAId,EAAStC,CAAG,EAAG,CACjBsC,EAAStC,CAAG,EAAE,KAAK,CAAE,QAAAmD,EAAS,OAAAC,CAAO,CAAC,EACtC,MACF,CACAd,EAAStC,CAAG,EAAI,CAAC,CAAE,QAAAmD,EAAS,OAAAC,CAAO,CAAC,EACpC,IAAMC,EAAWC,GAAO,CAEtBhB,EAAStC,CAAG,EAAE,QAAQuD,GAAWA,EAAQ,QAAQD,CAAG,CAAC,CACvD,EACA,GAAIR,EAAU,CACZ,IAAMU,EAAkB,OAAOV,CAAQ,EACvC,OAAOA,CAAQ,EAAI,UAAY,CACzBU,GAAiBA,EAAgB,EACrCH,EAASlC,GAAU0B,CAAS,CAAC,CAC/B,CACF,CACAG,EAAYhD,EAAKyD,GAAO,CAClBA,GAGFnB,EAAStC,CAAG,EAAE,QAAQuD,GAAWA,EAAQ,OAAOE,CAAG,CAAC,EACpDnB,EAAStC,CAAG,EAAI,MACN8C,GACVO,EAASlC,GAAU0B,CAAS,CAAC,CAEjC,CAAC,CACH,CAAC,CACH,ICxHA,IAEaa,GACAC,GACAC,GAEAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,EACAC,GACAC,GACAC,GACAC,GAEPC,GAwBOC,EA/CbC,EAAAC,EAAA,KAAAC,IAEavB,GAAoB,sLACpBC,GAAuB,sCACvBC,GAAkB,yCAElBC,GAAgB,uCAChBC,GAAqB,6EACrBC,GAA2B,6BAC3BC,GAAuB,gCACvBC,GAAmB,yEACnBC,GAAyB,iDACzBC,GAA2B,mDAC3BC,GAAwB,qIACxBC,GAAqB,gCACrBC,GAAoB,4CACpBC,GAAoB,iKACpBC,EAAmB,yEACnBC,GAAmB,kDACnBC,GAAiB,kBACjBC,GAAkB,iBAClBC,GAAiB,iBAExBC,GAAcK,GAAO,CACzB,GAAIA,aAAe,MAAO,CACxB,QAAWC,KAAQD,EAIjB,GAHI,OAAOC,GAAS,UAAYN,GAAYM,CAAI,GAG5CN,GAAYM,EAAK,GAAG,EACtB,MAAO,GAGX,MAAO,EACT,CACA,OAAIC,EAAcF,CAAG,GAAKG,GAAUH,CAAG,EAC9B,GAGPV,EAAiB,KAAKU,CAAG,GACzBT,GAAiB,KAAKS,CAAG,GACzBR,GAAe,KAAKQ,CAAG,GACvBP,GAAgB,KAAKO,CAAG,GACxBN,GAAe,KAAKM,CAAG,CAE3B,EAEaJ,EAAU,CACrB,QAASI,GACHA,aAAe,MACVA,EAAI,MAAMC,GAAQzB,GAAkB,KAAKyB,CAAI,CAAC,EAEhDzB,GAAkB,KAAKwB,CAAG,EAEnC,WAAYA,GAAOvB,GAAqB,KAAKuB,CAAG,GAAK,CAACV,EAAiB,KAAKU,CAAG,EAC/E,MAAOA,GAAOtB,GAAgB,KAAKsB,CAAG,GAAK,CAACT,GAAiB,KAAKS,CAAG,GAAK,CAACR,GAAe,KAAKQ,CAAG,EAClG,IAAKA,GAAOrB,GAAc,KAAKqB,CAAG,EAClC,SAAUA,GAAOpB,GAAmB,KAAKoB,CAAG,GAAKnB,GAAyB,KAAKmB,CAAG,EAClF,WAAYA,GAAOlB,GAAqB,KAAKkB,CAAG,EAChD,OAAQA,GAAOjB,GAAiB,KAAKiB,CAAG,EACxC,OAAQA,GAAOhB,GAAuB,KAAKgB,CAAG,GAAKf,GAAyB,KAAKe,CAAG,EACpF,YAAaA,GAAOd,GAAsB,KAAKc,CAAG,EAClD,SAAUA,GAAOb,GAAmB,KAAKa,CAAG,EAC5C,QAASA,GAAOZ,GAAkB,KAAKY,CAAG,EAC1C,QAASA,GAAOX,GAAkB,KAAKW,CAAG,EAC1C,KAAML,EACR,IClEA,IAAAS,GAAA,GAAAC,EAAAD,GAAA,aAAAE,IAAA,IAAAC,GAKMC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GAEeR,EAbrBS,GAAAC,EAAA,KAAAT,GAAiC,OAEjCU,IACAC,IAEMV,GAAU,qCACVC,GAAa,KACbC,GAAmB,0BACnBC,GAAiB,wCACjBC,GAAqB,4BACrBC,GAAiB,wBACjBC,GAAgB,mCAEDR,EAArB,cAAqC,YAAU,CAA/C,kCAGEa,EAAA,kBAAaC,GAmEbD,EAAA,qBAAiBE,GAAQ,CACvB,GAAIA,aAAe,MACjB,MAAO,CACL,SAAU,WACV,SAAUA,EAAI,IAAI,KAAK,KAAK,EAAE,KAAK,GAAG,CACxC,EAEF,GAAIV,GAAe,KAAKU,CAAG,EAAG,CAC5B,GAAM,CAAC,CAAEC,CAAU,EAAID,EAAI,MAAMV,EAAc,EAC/C,MAAO,CACL,SAAU,WACV,KAAMW,EAAW,QAAQ,MAAO,IAAI,CACtC,CACF,CACA,GAAIV,GAAmB,KAAKS,CAAG,EAAG,CAChC,GAAM,CAAC,CAAEE,CAAQ,EAAIF,EAAI,MAAMT,EAAkB,EACjD,MAAO,CACL,SAAU,eACV,KAAMW,CACR,CACF,CACA,MAAO,CAAC,CACV,GAEAJ,EAAA,qBAAiBK,GAAU,CACzB,GAAM,CAAE,KAAAC,CAAK,EAAID,EACX,CAAE,OAAAE,EAAQ,QAAAC,EAAS,SAAAC,EAAU,YAAAC,EAAa,QAAAC,EAAS,QAAAC,EAAS,KAAAC,EAAM,OAAQ,CAAE,WAAAC,EAAY,YAAAC,CAAY,CAAE,EAAI,KAAK,MAC/G,CAAE,UAAAC,EAAW,QAAAC,EAAS,OAAAC,EAAQ,UAAAC,GAAW,MAAAC,GAAO,KAAAC,EAAK,EAAI,OAAO/B,EAAU,EAAE,YAQlF,GAPIgB,IAASU,GAAWD,EAAY,EAChCT,IAASW,IACXV,EAAO,EACPG,EAAY,GAEVJ,IAASY,GAAQV,EAAQ,EACzBF,IAASa,IAAWV,EAAS,EAC7BH,IAASc,GAAO,CAClB,IAAME,GAAa,CAAC,CAAC,KAAK,WAAW,aAAa,EAE9CT,GAAQ,CAACS,KACPR,EAAW,MACb,KAAK,OAAOA,EAAW,KAAK,EAE5B,KAAK,KAAK,GAGdH,EAAQ,CACV,CACIL,IAASe,IAAMT,EAAQ,CAC7B,GA0BAZ,EAAA,YAAO,IAAM,CACX,KAAK,WAAW,MAAM,CACxB,GAEAA,EAAA,cAAS,IAAM,CACb,KAAK,WAAW,QAAQ,CAC1B,GAsBAA,EAAA,WAAMuB,GAAa,CACjB,KAAK,UAAYA,CACnB,GAzKA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CAC/C,CAEA,MAAOrB,EAAK,CACV,MAAI,CAACA,GAAOA,aAAe,OAASV,GAAe,KAAKU,CAAG,EAClD,KAEFA,EAAI,MAAMsB,EAAiB,EAAE,CAAC,CACvC,CAEA,KAAMtB,EAAKuB,EAAS,CAClB,GAAM,CAAE,QAAAC,EAAS,MAAAC,EAAO,YAAAC,EAAa,SAAAC,EAAU,KAAAhB,EAAM,OAAAiB,EAAQ,QAAAC,CAAQ,EAAI,KAAK,MACxE,CAAE,WAAAjB,EAAY,aAAAkB,CAAa,EAAIF,EAC/BG,EAAK,KAAK,MAAM/B,CAAG,EACzB,GAAIuB,EAAS,CACX,GAAIjC,GAAe,KAAKU,CAAG,GAAKT,GAAmB,KAAKS,CAAG,GAAKA,aAAe,MAAO,CACpF,KAAK,OAAO,aAAa,KAAK,cAAcA,CAAG,CAAC,EAChD,MACF,CACA,KAAK,OAAO,aAAa,CACvB,QAAS+B,EACT,aAAcC,EAAehC,CAAG,GAAKY,EAAW,MAChD,WAAYqB,GAAajC,CAAG,GAAKY,EAAW,GAC9C,CAAC,EACD,MACF,CACAsB,EAAO/C,GAASC,GAAYC,GAAkB8C,GAAMA,EAAG,MAAM,EAAE,KAAKA,GAAM,CACnE,KAAK,YACV,KAAK,OAAS,IAAIA,EAAG,OAAO,KAAK,UAAW,CAC1C,MAAO,OACP,OAAQ,OACR,QAASJ,EACT,WAAY,CACV,SAAUP,EAAU,EAAI,EACxB,KAAMC,EAAQ,EAAI,EAClB,SAAUE,EAAW,EAAI,EACzB,MAAOK,EAAehC,CAAG,EACzB,IAAKiC,GAAajC,CAAG,EACrB,OAAQ,OAAO,SAAS,OACxB,YAAa0B,EAAc,EAAI,EAC/B,GAAG,KAAK,cAAc1B,CAAG,EACzB,GAAGY,CACL,EACA,OAAQ,CACN,QAAS,IAAM,CACTD,GACF,KAAK,OAAO,QAAQ,EAAI,EAE1B,KAAK,MAAM,QAAQ,CACrB,EACA,qBAAsBR,GAAS,KAAK,MAAM,qBAAqBA,EAAM,IAAI,EACzE,wBAAyBA,GAAS,KAAK,MAAM,wBAAwBA,CAAK,EAC1E,cAAe,KAAK,cACpB,QAASA,GAAS0B,EAAQ1B,EAAM,IAAI,CACtC,EACA,KAAMX,GAAe,KAAKQ,CAAG,EAAIP,GAAgB,OACjD,GAAGqC,CACL,CAAC,EACH,EAAGD,CAAO,EACNC,EAAa,QACf,QAAQ,KAAK,kIAA6H,CAE9I,CAoDA,MAAQ,CACN,KAAK,WAAW,WAAW,CAC7B,CAEA,OAAS,CACP,KAAK,WAAW,YAAY,CAC9B,CAEA,MAAQ,CACD,SAAS,KAAK,SAAS,KAAK,WAAW,WAAW,CAAC,GACxD,KAAK,WAAW,WAAW,CAC7B,CAEA,OAAQM,EAAQC,EAAc,GAAO,CACnC,KAAK,WAAW,SAAUD,CAAM,EAC5B,CAACC,GAAe,CAAC,KAAK,MAAM,SAC9B,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,WAAW,YAAaA,EAAW,GAAG,CAC7C,CAUA,gBAAiBC,EAAM,CACrB,KAAK,WAAW,kBAAmBA,CAAI,CACzC,CAEA,QAAS5B,EAAM,CACb,KAAK,WAAW,UAAWA,CAAI,CACjC,CAEA,aAAe,CACb,OAAO,KAAK,WAAW,aAAa,CACtC,CAEA,gBAAkB,CAChB,OAAO,KAAK,WAAW,gBAAgB,CACzC,CAEA,kBAAoB,CAClB,OAAO,KAAK,WAAW,wBAAwB,EAAI,KAAK,YAAY,CACtE,CAMA,QAAU,CACR,GAAM,CAAE,QAAA6B,CAAQ,EAAI,KAAK,MAMzB,OACE,GAAAC,QAAA,cAAC,OAAI,MANO,CACZ,MAAO,OACP,OAAQ,OACR,QAAAD,CACF,GAGI,GAAAC,QAAA,cAAC,OAAI,IAAK,KAAK,IAAK,CACtB,CAEJ,CACF,EA5LE3C,EADmBb,EACZ,cAAc,WACrBa,EAFmBb,EAEZ,UAAUyD,EAAQ,WCf3B,IAAAC,GAAA,GAAAC,EAAAD,GAAA,aAAAE,IAAA,IAAAC,GAKMC,GACAC,GAEeH,EARrBI,GAAAC,EAAA,KAAAJ,GAAiC,OAEjCK,IACAC,IAEML,GAAU,yCACVC,GAAa,KAEEH,EAArB,cAAwC,YAAU,CAAlD,kCAIEQ,EAAA,kBAAaC,GACbD,EAAA,gBAAW,MACXA,EAAA,mBAAc,MACdA,EAAA,sBAAiB,MA+DjBA,EAAA,YAAO,IAAM,CACX,KAAK,UAAU,CAAC,CAClB,GAEAA,EAAA,cAAS,IAAM,CACT,KAAK,MAAM,SAAW,MACxB,KAAK,UAAU,KAAK,MAAM,MAAM,CAEpC,GAcAA,EAAA,WAAME,GAAU,CACd,KAAK,OAASA,CAChB,GArFA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CAC/C,CAEA,KAAMC,EAAKC,EAAS,CAClBC,EAAOX,GAASC,EAAU,EAAE,KAAKW,GAAM,CACrC,GAAI,CAAC,KAAK,OAAQ,OAClB,GAAM,CAAE,KAAAC,EAAM,cAAAC,EAAe,MAAAC,EAAO,OAAAC,EAAQ,MAAAC,CAAM,EAAIL,EAAG,OAAO,OAC3DF,IACH,KAAK,OAASE,EAAG,OAAO,KAAK,MAAM,EACnC,KAAK,OAAO,KAAKC,EAAM,KAAK,MAAM,MAAM,EACxC,KAAK,OAAO,KAAKE,EAAO,IAAM,CACV,KAAK,SAAW,KAAK,YACvB,KAIhB,KAAK,MAAM,QAAQ,CACrB,CAAC,EACD,KAAK,OAAO,KAAKD,EAAeI,GAAK,CACnC,KAAK,YAAcA,EAAE,gBAAkB,IACvC,KAAK,eAAiBA,EAAE,cAC1B,CAAC,EACD,KAAK,OAAO,KAAKF,EAAQ,IAAM,KAAK,MAAM,QAAQ,CAAC,EACnD,KAAK,OAAO,KAAKC,EAAOC,GAAK,KAAK,MAAM,QAAQA,CAAC,CAAC,GAEpD,KAAK,OAAO,KAAKT,EAAK,CACpB,GAAG,KAAK,MAAM,OAAO,QACrB,SAAU,IAAM,CACd,KAAK,OAAO,YAAYU,GAAY,CAClC,KAAK,SAAWA,EAAW,IAC3B,KAAK,MAAM,QAAQ,CACrB,CAAC,CACH,CACF,CAAC,CACH,CAAC,CACH,CAEA,MAAQ,CACN,KAAK,WAAW,MAAM,CACxB,CAEA,OAAS,CACP,KAAK,WAAW,OAAO,CACzB,CAEA,MAAQ,CAER,CAEA,OAAQC,EAASC,EAAc,GAAM,CACnC,KAAK,WAAW,SAAUD,EAAU,GAAI,EACnCC,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,WAAW,YAAaA,EAAW,GAAG,CAC7C,CAYA,aAAe,CACb,OAAO,KAAK,QACd,CAEA,gBAAkB,CAChB,OAAO,KAAK,WACd,CAEA,kBAAoB,CAClB,OAAO,KAAK,eAAiB,KAAK,QACpC,CAMA,QAAU,CACR,GAAM,CAAE,QAAAC,CAAQ,EAAI,KAAK,MACnBC,EAAQ,CACZ,MAAO,OACP,OAAQ,OACR,QAAAD,CACF,EACA,OACE,GAAAE,QAAA,cAAC,UACC,IAAK,KAAK,IACV,IAAK,wCAAwC,mBAAmB,KAAK,MAAM,GAAG,CAAC,GAC/E,MAAOD,EACP,YAAa,EACb,MAAM,WACR,CAEJ,CACF,EAhHElB,EADmBR,EACZ,cAAc,cACrBQ,EAFmBR,EAEZ,UAAU4B,EAAQ,YACzBpB,EAHmBR,EAGZ,cAAc,MCXvB,IAAA6B,GAAA,GAAAC,EAAAD,GAAA,aAAAE,IAAA,IAAAC,GAKMC,GACAC,GAEAC,GAIeJ,EAZrBK,GAAAC,EAAA,KAAAL,GAAiC,OAEjCM,IACAC,IAEMN,GAAU,yCACVC,GAAa,QAEbC,GAAWK,GACRA,EAAI,QAAQ,iBAAkB,EAAE,EAGpBT,EAArB,cAAmC,YAAU,CAA7C,kCAIEU,EAAA,kBAAaC,GACbD,EAAA,gBAAW,MACXA,EAAA,mBAAc,MACdA,EAAA,qBAAgB,MAgGhBA,EAAA,YAAO,IAAM,CACX,KAAK,SAAS,EAAI,CACpB,GAEAA,EAAA,cAAS,IAAM,CACb,KAAK,SAAS,EAAK,CACrB,GAcAA,EAAA,WAAME,GAAa,CACjB,KAAK,UAAYA,CACnB,GApHA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CAC/C,CAEA,KAAMH,EAAK,CACT,KAAK,SAAW,KAChBI,EAAOX,GAASC,EAAU,EAAE,KAAKH,GAAS,CACxC,GAAI,CAAC,KAAK,UAAW,OACrB,GAAM,CAAE,cAAAc,EAAe,MAAAC,CAAM,EAAI,KAAK,MAAM,OAC5C,KAAK,OAAS,IAAIf,EAAM,OAAO,KAAK,UAAW,CAC7C,IAAKI,GAASK,CAAG,EACjB,SAAU,KAAK,MAAM,QACrB,MAAO,KAAK,MAAM,MAClB,KAAM,KAAK,MAAM,KACjB,YAAa,KAAK,MAAM,YACxB,SAAU,KAAK,MAAM,SACrB,GAAGK,CACL,CAAC,EACD,KAAK,OAAO,MAAM,EAAE,KAAK,IAAM,CAC7B,IAAME,EAAS,KAAK,UAAU,cAAc,QAAQ,EACpDA,EAAO,MAAM,MAAQ,OACrBA,EAAO,MAAM,OAAS,OAClBD,IACFC,EAAO,MAAQD,EAEnB,CAAC,EAAE,MAAM,KAAK,MAAM,OAAO,EAC3B,KAAK,OAAO,GAAG,SAAU,IAAM,CAC7B,KAAK,MAAM,QAAQ,EACnB,KAAK,gBAAgB,CACvB,CAAC,EACD,KAAK,OAAO,GAAG,OAAQ,IAAM,CAC3B,KAAK,MAAM,OAAO,EAClB,KAAK,gBAAgB,CACvB,CAAC,EACD,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,SAAUE,GAAK,KAAK,MAAM,OAAOA,EAAE,OAAO,CAAC,EAC1D,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,aAAc,CAAC,CAAE,QAAAC,CAAQ,IAAM,CAC5C,KAAK,YAAcA,CACrB,CAAC,EACD,KAAK,OAAO,GAAG,WAAY,CAAC,CAAE,QAAAA,CAAQ,IAAM,CAC1C,KAAK,cAAgBA,CACvB,CAAC,EACD,KAAK,OAAO,GAAG,cAAe,KAAK,MAAM,QAAQ,EACjD,KAAK,OAAO,GAAG,YAAa,KAAK,MAAM,WAAW,EAClD,KAAK,OAAO,GAAG,qBAAsBD,GAAK,KAAK,MAAM,qBAAqBA,EAAE,YAAY,CAAC,CAC3F,EAAG,KAAK,MAAM,OAAO,CACvB,CAEA,iBAAmB,CACjB,KAAK,OAAO,YAAY,EAAE,KAAKE,GAAY,CACzC,KAAK,SAAWA,CAClB,CAAC,CACH,CAEA,MAAQ,CACN,IAAMC,EAAU,KAAK,WAAW,MAAM,EAClCA,GACFA,EAAQ,MAAM,KAAK,MAAM,OAAO,CAEpC,CAEA,OAAS,CACP,KAAK,WAAW,OAAO,CACzB,CAEA,MAAQ,CACN,KAAK,WAAW,QAAQ,CAC1B,CAEA,OAAQF,EAASG,EAAc,GAAM,CACnC,KAAK,WAAW,iBAAkBH,CAAO,EACpCG,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,WAAW,YAAaA,CAAQ,CACvC,CAEA,SAAUC,EAAO,CACf,KAAK,WAAW,WAAYA,CAAK,CACnC,CAEA,QAASC,EAAM,CACb,KAAK,WAAW,UAAWA,CAAI,CACjC,CAEA,gBAAiBC,EAAM,CACrB,KAAK,WAAW,kBAAmBA,CAAI,CACzC,CAUA,aAAe,CACb,OAAO,KAAK,QACd,CAEA,gBAAkB,CAChB,OAAO,KAAK,WACd,CAEA,kBAAoB,CAClB,OAAO,KAAK,aACd,CAMA,QAAU,CACR,GAAM,CAAE,QAAAC,CAAQ,EAAI,KAAK,MACnBC,EAAQ,CACZ,MAAO,OACP,OAAQ,OACR,SAAU,SACV,QAAAD,CACF,EACA,OACE,GAAAE,QAAA,cAAC,OACC,IAAK,KAAK,MAAM,IAChB,IAAK,KAAK,IACV,MAAOD,EACT,CAEJ,CACF,EA9IEjB,EADmBV,EACZ,cAAc,SACrBU,EAFmBV,EAEZ,UAAU6B,EAAQ,OACzBnB,EAHmBV,EAGZ,YAAY,MCfrB,IAAA8B,GAAA,GAAAC,EAAAD,GAAA,aAAAE,IAAA,IAAAC,GAIMC,GAEeF,EANrBG,GAAAC,EAAA,KAAAH,GAAiC,OAEjCI,IAEMH,GAAU,2EAEKF,EAArB,cAAiC,YAAU,CAA3C,kCAqDEM,EAAA,eAAU,IAAIC,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,GACjDD,EAAA,cAAS,IAAIC,IAAS,KAAK,MAAM,OAAO,GAAGA,CAAI,GAC/CD,EAAA,gBAAW,IAAIC,IAAS,KAAK,MAAM,SAAS,GAAGA,CAAI,GACnDD,EAAA,mBAAc,IAAIC,IAAS,KAAK,MAAM,YAAY,GAAGA,CAAI,GACzDD,EAAA,eAAU,IAAIC,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,GACjDD,EAAA,eAAU,IAAIC,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,GACjDD,EAAA,eAAU,IAAIC,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,GACjDD,EAAA,4BAAwBE,GAAU,KAAK,MAAM,qBAAqBA,EAAM,OAAO,YAAY,GAC3FF,EAAA,mBAAc,IAAIC,IAAS,KAAK,MAAM,YAAY,GAAGA,CAAI,GAEzDD,EAAA,cAAS,GAAK,CACZ,KAAK,MAAM,OAAO,EAAE,OAAO,WAAW,CACxC,GAmBAA,EAAA,wBAAmB,IAAM,CACvB,IAAMG,EAAW,KAAK,YAAY,EAClC,KAAK,MAAM,WAAWA,CAAQ,CAChC,GA4BAH,EAAA,YAAO,IAAM,CACX,KAAK,OAAO,MAAQ,EACtB,GAEAA,EAAA,cAAS,IAAM,CACb,KAAK,OAAO,MAAQ,EACtB,GAyDAA,EAAA,WAAMI,GAAU,CACd,KAAK,OAASA,CAChB,GAhLA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,EAC7C,KAAK,aAAa,KAAK,MAAM,EAC7B,IAAMC,EAAa,KAAK,cAAc,KAAK,MAAM,GAAG,EAChDA,IACF,KAAK,OAAO,WAAaA,EAE7B,CAEA,sBAAwB,CACtB,KAAK,OAAO,WAAa,KACzB,KAAK,gBAAgB,KAAK,MAAM,CAClC,CAEA,aAAcD,EAAQ,CACpB,GAAM,CAAE,YAAAE,CAAY,EAAI,KAAK,MAC7BF,EAAO,iBAAiB,OAAQ,KAAK,MAAM,EAC3CA,EAAO,iBAAiB,UAAW,KAAK,QAAQ,EAChDA,EAAO,iBAAiB,UAAW,KAAK,WAAW,EACnDA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,SAAU,KAAK,MAAM,EAC7CA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,aAAc,KAAK,oBAAoB,EAC/DA,EAAO,iBAAiB,wBAAyB,KAAK,WAAW,EACjEA,EAAO,iBAAiB,wBAAyB,KAAK,YAAY,EAClEA,EAAO,iBAAiB,gCAAiC,KAAK,wBAAwB,EACtFA,EAAO,iBAAiB,UAAW,KAAK,OAAO,EAC3CE,GACFF,EAAO,aAAa,cAAe,EAAE,CAEzC,CAEA,gBAAiBA,EAAQ,CACvBA,EAAO,oBAAoB,UAAW,KAAK,OAAO,EAClDA,EAAO,oBAAoB,OAAQ,KAAK,MAAM,EAC9CA,EAAO,oBAAoB,UAAW,KAAK,QAAQ,EACnDA,EAAO,oBAAoB,UAAW,KAAK,WAAW,EACtDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,SAAU,KAAK,MAAM,EAChDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,aAAc,KAAK,oBAAoB,EAClEA,EAAO,oBAAoB,wBAAyB,KAAK,WAAW,EACpEA,EAAO,oBAAoB,wBAAyB,KAAK,YAAY,EACrEA,EAAO,oBAAoB,UAAW,KAAK,OAAO,CACpD,CAiBA,MAAM,KAAMG,EAAK,CAzEnB,IAAAC,EA0EI,GAAM,CAAE,QAAAC,EAAS,OAAAC,CAAO,EAAI,KAAK,MAEjC,GAAI,GAACF,EAAA,WAAW,iBAAX,MAAAA,EAA2B,IAAI,eAClC,GAAI,CAEF,MAAM,OAAiC,GADxBZ,GAAQ,QAAQ,UAAWc,EAAO,OAAO,CACR,IAChD,KAAK,MAAM,SAAS,CACtB,OAASC,EAAO,CACdF,EAAQE,CAAK,CACf,CAGF,GAAM,CAAC,CAAEC,CAAE,EAAIL,EAAI,MAAMM,EAAa,EACtC,KAAK,OAAO,WAAaD,CAC3B,CAOA,MAAQ,CACN,IAAME,EAAU,KAAK,OAAO,KAAK,EAC7BA,GACFA,EAAQ,MAAM,KAAK,MAAM,OAAO,CAEpC,CAEA,OAAS,CACP,KAAK,OAAO,MAAM,CACpB,CAEA,MAAQ,CACN,KAAK,OAAO,WAAa,IAC3B,CAEA,OAAQC,EAASC,EAAc,GAAM,CACnC,KAAK,OAAO,YAAcD,EACrBC,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,OAAO,OAASA,CACvB,CAUA,WAAa,CACP,KAAK,OAAO,yBAA2B,SAAS,0BAA4B,KAAK,QACnF,KAAK,OAAO,wBAAwB,CAExC,CAEA,YAAc,CACR,SAAS,sBAAwB,SAAS,0BAA4B,KAAK,QAC7E,SAAS,qBAAqB,CAElC,CAEA,gBAAiBC,EAAM,CACrB,GAAI,CACF,KAAK,OAAO,aAAeA,CAC7B,OAASP,EAAO,CACd,KAAK,MAAM,QAAQA,CAAK,CAC1B,CACF,CAEA,aAAe,CACb,GAAI,CAAC,KAAK,OAAQ,OAAO,KACzB,GAAM,CAAE,SAAAR,EAAU,SAAAgB,CAAS,EAAI,KAAK,OAGpC,OAAIhB,IAAa,KAAYgB,EAAS,OAAS,EACtCA,EAAS,IAAIA,EAAS,OAAS,CAAC,EAElChB,CACT,CAEA,gBAAkB,CAChB,OAAK,KAAK,OACH,KAAK,OAAO,YADM,IAE3B,CAEA,kBAAoB,CAClB,GAAI,CAAC,KAAK,OAAQ,OAAO,KACzB,GAAM,CAAE,SAAAiB,CAAS,EAAI,KAAK,OAC1B,GAAIA,EAAS,SAAW,EACtB,MAAO,GAET,IAAMC,EAAMD,EAAS,IAAIA,EAAS,OAAS,CAAC,EACtCjB,EAAW,KAAK,YAAY,EAClC,OAAIkB,EAAMlB,EACDA,EAEFkB,CACT,CAEA,cAAed,EAAK,CAClB,GAAM,CAAC,CAAEK,CAAE,EAAIL,EAAI,MAAMM,EAAa,EACtC,OAAOD,CACT,CAMA,QAAU,CACR,GAAM,CAAE,IAAAL,EAAK,QAAAe,EAAS,KAAAC,EAAM,SAAAC,EAAU,MAAAC,EAAO,OAAAf,EAAQ,MAAAgB,EAAO,OAAAC,CAAO,EAAI,KAAK,MACtEC,EAAQ,CACZ,MAAOF,IAAU,OAASA,EAAQ,OAClC,OAAQC,IAAW,OAASA,EAAS,MACvC,EACA,OAAIH,IAAa,KACfI,EAAM,YAAY,EAAI,QAGtB,GAAAC,QAAA,cAAC,cACC,IAAK,KAAK,IACV,cAAa,KAAK,cAActB,CAAG,EACnC,MAAOqB,EACP,QAAQ,OACR,SAAUN,GAAW,OACrB,MAAOG,EAAQ,GAAK,OACpB,KAAMF,EAAO,GAAK,OACjB,GAAGb,EAAO,WACb,CAEJ,CACF,EA3MEV,EADmBN,EACZ,cAAc,OACrBM,EAFmBN,EAEZ,UAAUoC,EAAQ,OCR3B,IAAAC,GAAA,GAAAC,EAAAD,GAAA,aAAAE,IAAA,IAAAC,GAKMC,GACAC,GACAC,GACAC,GAEeL,EAVrBM,GAAAC,EAAA,KAAAN,GAAiC,OAEjCO,IACAC,IAEMP,GAAU,4CACVC,GAAa,KACbC,GAAmB,cACnBC,GAAmB,mBAEJL,EAArB,cAAsC,YAAU,CAAhD,kCAIEU,EAAA,kBAAaC,GACbD,EAAA,gBAAW,KAAK,MAAM,OAAO,UAAY,GAAGL,EAAgB,GAAGO,EAAa,CAAC,IAqE7EF,EAAA,YAAO,IAAM,CACX,KAAK,WAAW,MAAM,CACxB,GAEAA,EAAA,cAAS,IAAM,CACb,KAAK,WAAW,QAAQ,CAC1B,GAzEA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CAC/C,CAEA,KAAMG,EAAKC,EAAS,CAClB,GAAIA,EAAS,CACXC,EAAOb,GAASC,GAAYC,EAAgB,EAAE,KAAKY,GAAMA,EAAG,MAAM,MAAM,CAAC,EACzE,MACF,CACAD,EAAOb,GAASC,GAAYC,EAAgB,EAAE,KAAKY,GAAM,CACvDA,EAAG,KAAK,CACN,MAAO,KAAK,MAAM,OAAO,MACzB,MAAO,GACP,QAAS,KAAK,MAAM,OAAO,OAC7B,CAAC,EACDA,EAAG,MAAM,UAAU,eAAgBC,GAAO,CAGxC,KAAK,MAAM,SAAS,CACtB,CAAC,EACDD,EAAG,MAAM,UAAU,cAAeC,GAAO,CACnCA,EAAI,OAAS,SAAWA,EAAI,KAAO,KAAK,WAC1C,KAAK,OAASA,EAAI,SAClB,KAAK,OAAO,UAAU,iBAAkB,KAAK,MAAM,MAAM,EACzD,KAAK,OAAO,UAAU,SAAU,KAAK,MAAM,OAAO,EAClD,KAAK,OAAO,UAAU,kBAAmB,KAAK,MAAM,OAAO,EAC3D,KAAK,OAAO,UAAU,mBAAoB,KAAK,MAAM,QAAQ,EAC7D,KAAK,OAAO,UAAU,oBAAqB,KAAK,MAAM,WAAW,EACjE,KAAK,OAAO,UAAU,QAAS,KAAK,MAAM,OAAO,EAC7C,KAAK,MAAM,MACb,KAAK,WAAW,MAAM,EAEtB,KAAK,WAAW,QAAQ,EAE1B,KAAK,MAAM,QAAQ,EAInB,SAAS,eAAe,KAAK,QAAQ,EAAE,cAAc,QAAQ,EAAE,MAAM,WAAa,UAEtF,CAAC,CACH,CAAC,CACH,CAEA,MAAQ,CACN,KAAK,WAAW,MAAM,CACxB,CAEA,OAAS,CACP,KAAK,WAAW,OAAO,CACzB,CAEA,MAAQ,CAER,CAEA,OAAQC,EAASC,EAAc,GAAM,CACnC,KAAK,WAAW,OAAQD,CAAO,EAC1BC,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,WAAW,YAAaA,CAAQ,CACvC,CAUA,aAAe,CACb,OAAO,KAAK,WAAW,aAAa,CACtC,CAEA,gBAAkB,CAChB,OAAO,KAAK,WAAW,oBAAoB,CAC7C,CAEA,kBAAoB,CAClB,OAAO,IACT,CAEA,QAAU,CACR,GAAM,CAAE,WAAAC,CAAW,EAAI,KAAK,MAAM,OAKlC,OACE,GAAAC,QAAA,cAAC,OACC,MANU,CACZ,MAAO,OACP,OAAQ,MACV,EAII,GAAI,KAAK,SACT,UAAU,WACV,YAAW,KAAK,MAAM,IACtB,gBAAe,KAAK,MAAM,QAAU,OAAS,QAC7C,uBAAqB,OACrB,gBAAe,KAAK,MAAM,SAAW,OAAS,QAC7C,GAAGD,EACN,CAEJ,CACF,EAhHEX,EADmBV,EACZ,cAAc,YACrBU,EAFmBV,EAEZ,UAAUuB,EAAQ,UACzBb,EAHmBV,EAGZ,cAAc,MCbvB,IAAAwB,GAAA,GAAAC,EAAAD,GAAA,aAAAE,KAAA,IAAAC,GAKMC,GACAC,GAEeH,GARrBI,GAAAC,EAAA,KAAAJ,GAAiC,OAEjCK,IACAC,IAEML,GAAU,2CACVC,GAAa,WAEEH,GAArB,cAAwC,YAAU,CAAlD,kCAGEQ,EAAA,kBAAaC,GACbD,EAAA,gBAAW,MACXA,EAAA,mBAAc,MACdA,EAAA,qBAAgB,MA2DhBA,EAAA,YAAO,IAAM,CACX,KAAK,WAAW,MAAM,CACxB,GAEAA,EAAA,cAAS,IAAM,CACb,KAAK,WAAW,QAAQ,CAC1B,GAcAA,EAAA,WAAME,GAAU,CACd,KAAK,OAASA,CAChB,GA/EA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CAC/C,CAEA,KAAMC,EAAK,CACTC,EAAOV,GAASC,EAAU,EAAE,KAAKU,GAAY,CACtC,KAAK,SACV,KAAK,OAAS,IAAIA,EAAS,OAAO,KAAK,MAAM,EAC7C,KAAK,OAAO,QAAQ,KAAK,MAAM,IAAI,EACnC,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,OAAQ,KAAK,MAAM,MAAM,EACxC,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,SAAU,KAAK,MAAM,MAAM,EAC1C,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,aAAc,CAAC,CAAE,SAAAC,EAAU,QAAAC,CAAQ,IAAM,CACtD,KAAK,SAAWD,EAChB,KAAK,YAAcC,CACrB,CAAC,EACD,KAAK,OAAO,GAAG,WAAY,CAAC,CAAE,QAAAC,CAAQ,IAAM,CACtC,KAAK,WACP,KAAK,cAAgB,KAAK,SAAWA,EAEzC,CAAC,EACG,KAAK,MAAM,OACb,KAAK,OAAO,KAAK,EAErB,EAAG,KAAK,MAAM,OAAO,CACvB,CAEA,MAAQ,CACN,KAAK,WAAW,MAAM,CACxB,CAEA,OAAS,CACP,KAAK,WAAW,OAAO,CACzB,CAEA,MAAQ,CAER,CAEA,OAAQD,EAASE,EAAc,GAAM,CACnC,KAAK,WAAW,iBAAkBF,CAAO,EACpCE,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,WAAW,YAAaA,EAAW,GAAG,CAC7C,CAEA,QAASC,EAAM,CACb,KAAK,WAAW,UAAWA,CAAI,CACjC,CAUA,aAAe,CACb,OAAO,KAAK,QACd,CAEA,gBAAkB,CAChB,OAAO,KAAK,WACd,CAEA,kBAAoB,CAClB,OAAO,KAAK,aACd,CAMA,QAAU,CACR,IAAMC,EAAK,KAAK,MAAM,IAAI,MAAMC,EAAoB,EAAE,CAAC,EACjDC,EAAQ,CACZ,MAAO,OACP,OAAQ,MACV,EACA,OACE,GAAAC,QAAA,cAAC,UACC,IAAK,KAAK,IACV,IAAK,4BAA4BH,CAAE,GACnC,YAAY,IACZ,UAAU,KACV,MAAOE,EACP,MAAM,yCACR,CAEJ,CACF,EAzGEd,EADmBR,GACZ,cAAc,cACrBQ,EAFmBR,GAEZ,UAAUwB,EAAQ,cCV3B,IAAAC,GAAA,GAAAC,EAAAD,GAAA,aAAAE,IAAA,IAAAC,GAKMC,GACAC,GACAC,GAEeJ,EATrBK,GAAAC,EAAA,KAAAL,GAAiC,OAEjCM,IACAC,IAEMN,GAAU,kDACVC,GAAa,SACbC,GAAmB,iBAEJJ,EAArB,cAAoC,YAAU,CAA9C,kCAIES,EAAA,kBAAaC,GACbD,EAAA,gBAAW,KAAK,MAAM,OAAO,UAAY,GAAGL,EAAgB,GAAGO,EAAa,CAAC,IAoD7EF,EAAA,cAAS,IAAIG,IAAS,KAAK,MAAM,OAAO,GAAGA,CAAI,GAC/CH,EAAA,eAAU,IAAIG,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,GACjDH,EAAA,cAAS,IAAIG,IAAS,KAAK,MAAM,OAAO,GAAGA,CAAI,GAC/CH,EAAA,eAAU,IAAIG,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,GACjDH,EAAA,4BAAuB,IAAIG,IAAS,KAAK,MAAM,qBAAqB,GAAGA,CAAI,GA0B3EH,EAAA,YAAO,IAAM,CACX,KAAK,WAAW,MAAM,CACxB,GAEAA,EAAA,cAAS,IAAM,CACb,KAAK,WAAW,QAAQ,CAC1B,GAtFA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CAC/C,CAEA,KAAMI,EAAK,CACT,GAAM,CAAE,QAAAC,EAAS,MAAAC,EAAO,SAAAC,EAAU,QAAAC,EAAS,OAAAC,EAAQ,QAAAC,CAAQ,EAAI,KAAK,MACpEC,EAAOlB,GAASC,EAAU,EAAE,KAAKH,GAAU,CACrCkB,EAAO,gBACTA,EAAO,eAAe,QAAQG,GAAWrB,EAAO,cAAcqB,CAAO,CAAC,EAExE,OAAO,IAAM,OAAO,KAAO,CAAC,EAC5B,OAAO,IAAI,KAAK,CACd,GAAI,KAAK,SACT,QAAS,CACP,SAAUP,EACV,eAAgB,QAChB,MAAAC,EACA,sBAAuBC,EACvB,iBAAkBA,EAClB,QAASA,EACT,oBAAqBA,EACrB,eAAgBA,EAChB,cAAeA,EACf,gBAAiBA,EACjB,gBAAiBA,EACjB,GAAGE,EAAO,OACZ,EACA,QAASI,GAAU,CACjB,KAAK,OAASA,EACd,KAAK,OAAO,EACZ,KAAK,OAAO,KAAK,OAAQ,KAAK,MAAM,EACpC,KAAK,OAAO,KAAK,QAAS,KAAK,OAAO,EACtC,KAAK,OAAO,KAAK,OAAQ,KAAK,MAAM,EACpC,KAAK,OAAO,KAAK,MAAO,KAAK,OAAO,EACpC,KAAK,OAAO,KAAK,qBAAsB,KAAK,oBAAoB,EAChEL,EAAQ,CACV,CACF,CAAC,CACH,EAAGE,CAAO,CACZ,CAEA,QAAU,CACR,KAAK,OAAO,OAAO,OAAQ,KAAK,MAAM,EACtC,KAAK,OAAO,OAAO,QAAS,KAAK,OAAO,EACxC,KAAK,OAAO,OAAO,OAAQ,KAAK,MAAM,EACtC,KAAK,OAAO,OAAO,MAAO,KAAK,OAAO,EACtC,KAAK,OAAO,OAAO,qBAAsB,KAAK,oBAAoB,CACpE,CASA,MAAQ,CACN,KAAK,WAAW,MAAM,CACxB,CAEA,OAAS,CACP,KAAK,WAAW,OAAO,CACzB,CAEA,MAAQ,CACN,KAAK,OAAO,EACZ,KAAK,WAAW,QAAQ,CAC1B,CAEA,OAAQI,EAASC,EAAc,GAAM,CACnC,KAAK,WAAW,OAAQD,CAAO,EAC1BC,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,WAAW,SAAUA,CAAQ,CACpC,CAUA,gBAAiBC,EAAM,CACrB,KAAK,WAAW,eAAgBA,CAAI,CACtC,CAEA,aAAe,CACb,OAAO,KAAK,WAAW,UAAU,CACnC,CAEA,gBAAkB,CAChB,OAAO,KAAK,WAAW,MAAM,CAC/B,CAEA,kBAAoB,CAClB,OAAO,IACT,CAEA,QAAU,CACR,GAAM,CAAE,IAAAb,CAAI,EAAI,KAAK,MACfc,EAAUd,GAAOA,EAAI,MAAMe,EAAgB,EAAE,CAAC,EAC9CC,EAAY,6BAA6BF,CAAO,GAChDG,EAAQ,CACZ,MAAO,OACP,OAAQ,MACV,EACA,OACE,GAAAC,QAAA,cAAC,OAAI,GAAI,KAAK,SAAU,IAAKJ,EAAS,UAAWE,EAAW,MAAOC,EAAO,CAE9E,CACF,EA1HErB,EADmBT,EACZ,cAAc,UACrBS,EAFmBT,EAEZ,UAAUgC,EAAQ,QACzBvB,EAHmBT,EAGZ,cAAc,MCZvB,IAAAiC,GAAA,GAAAC,EAAAD,GAAA,aAAAE,IAAA,IAAAC,GAKMC,GACAC,GACAC,GAEeJ,EATrBK,GAAAC,EAAA,KAAAL,GAAiC,OAEjCM,IACAC,IAEMN,GAAU,0CACVC,GAAa,SACbC,GAAmB,iBAEJJ,EAArB,cAAoC,YAAU,CAA9C,kCAIES,EAAA,kBAAaC,GACbD,EAAA,gBAAW,KAAK,MAAM,OAAO,UAAY,GAAGL,EAAgB,GAAGO,EAAa,CAAC,IAoE7EF,EAAA,YAAO,IAAM,CACX,KAAK,WAAW,WAAY,EAAI,CAClC,GAEAA,EAAA,cAAS,IAAM,CACb,KAAK,WAAW,WAAY,EAAK,CACnC,GAxEA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CAC/C,CAEA,KAAMG,EAAKC,EAAS,CAClB,GAAM,CAAE,YAAAC,EAAa,QAAAC,EAAS,OAAAC,EAAQ,SAAAC,CAAS,EAAI,KAAK,MAClDC,EAAYC,GAAyB,KAAKP,CAAG,EAC7CQ,EAAKF,EAAYN,EAAI,MAAMO,EAAwB,EAAE,CAAC,EAAIP,EAAI,MAAMS,EAAsB,EAAE,CAAC,EACnG,GAAIR,EAAS,CACPK,EACF,KAAK,OAAO,WAAWE,CAAE,EAEzB,KAAK,OAAO,SAAS,IAAMA,CAAE,EAE/B,MACF,CACAE,EAAOpB,GAASC,EAAU,EAAE,KAAKH,GAAU,CACzC,KAAK,OAAS,IAAIA,EAAO,OAAO,KAAK,SAAU,CAC7C,MAAOkB,EAAY,GAAKE,EACxB,QAASF,EAAYE,EAAK,GAC1B,OAAQ,OACR,MAAO,OACP,YAAAN,EACA,SAAU,KAAK,MAAM,QACrB,MAAO,KAAK,MAAM,MAElB,SAAUI,EAAY,GAAOD,EAC7B,KAAMM,EAAeX,CAAG,EACxB,GAAGI,EAAO,OACZ,CAAC,EACD,GAAM,CAAE,MAAAQ,EAAO,QAAAC,EAAS,MAAAC,EAAO,MAAAC,EAAO,OAAAC,EAAQ,QAAAC,GAAS,KAAAC,EAAK,EAAI9B,EAAO,OACvE,KAAK,OAAO,iBAAiBwB,EAAO,KAAK,MAAM,OAAO,EACtD,KAAK,OAAO,iBAAiBC,EAAS,KAAK,MAAM,MAAM,EACvD,KAAK,OAAO,iBAAiBC,EAAO,KAAK,MAAM,OAAO,EACtD,KAAK,OAAO,iBAAiBC,EAAO,KAAK,MAAM,OAAO,EACtD,KAAK,OAAO,iBAAiBG,GAAM,KAAK,MAAM,MAAM,EAGpD,KAAK,OAAO,iBAAiBF,EAAQ,KAAK,MAAM,QAAQ,EACxD,KAAK,OAAO,iBAAiBC,GAAS,KAAK,MAAM,QAAQ,CAC3D,EAAGd,CAAO,CACZ,CAEA,MAAQ,CACN,KAAK,WAAW,MAAM,CACxB,CAEA,OAAS,CACP,KAAK,WAAW,OAAO,CACzB,CAEA,MAAQ,CACN,KAAK,WAAW,OAAO,CACzB,CAEA,OAAQgB,EAASC,EAAc,GAAM,CACnC,KAAK,WAAW,OAAQD,CAAO,EAC1BC,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,WAAW,YAAaA,CAAQ,CACvC,CAUA,aAAe,CACb,OAAO,KAAK,WAAW,aAAa,CACtC,CAEA,gBAAkB,CAChB,OAAO,KAAK,WAAW,gBAAgB,CACzC,CAEA,kBAAoB,CAClB,OAAO,IACT,CAEA,QAAU,CAKR,OACE,GAAAC,QAAA,cAAC,OAAI,MALO,CACZ,MAAO,OACP,OAAQ,MACV,EAEqB,GAAI,KAAK,SAAU,CAE1C,CACF,EArGEzB,EADmBT,EACZ,cAAc,UACrBS,EAFmBT,EAEZ,UAAUmC,EAAQ,QACzB1B,EAHmBT,EAGZ,cAAc,MCZvB,IAAAoC,GAAA,GAAAC,EAAAD,GAAA,aAAAE,IAAA,IAAAC,GAKMC,GACAC,GACAC,GAEeJ,EATrBK,GAAAC,EAAA,KAAAL,GAAiC,OAEjCM,IACAC,IAEMN,GAAU,+BACVC,GAAa,KACbC,GAAmB,cAEJJ,EAArB,cAAyC,YAAU,CAAnD,kCAIES,EAAA,kBAAaC,GA6CbD,EAAA,wBAAmB,IAAM,CACvB,IAAME,EAAW,KAAK,YAAY,EAClC,KAAK,MAAM,WAAWA,CAAQ,CAChC,GAyBAF,EAAA,YAAO,IAAM,CACX,KAAK,WAAW,WAAY,EAAI,CAClC,GAEAA,EAAA,cAAS,IAAM,CACb,KAAK,WAAW,WAAY,EAAK,CACnC,GAcAA,EAAA,WAAMG,GAAa,CACjB,KAAK,UAAYA,CACnB,GA7FA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CAC/C,CAEA,KAAMC,EAAK,CACT,GAAM,CAAE,SAAAC,EAAU,OAAAC,EAAQ,QAAAC,EAAS,QAAAC,CAAQ,EAAI,KAAK,MAC9C,CAAC,CAAEC,CAAE,EAAIL,EAAI,MAAMM,EAAqB,EAC9C,GAAI,KAAK,OAAQ,CACf,KAAK,OAAO,KAAKD,EAAI,CACnB,MAAOE,EAAeP,CAAG,EACzB,SAAUI,CACZ,CAAC,EACD,MACF,CACAI,EAAOnB,GAASC,GAAYC,GAAkBkB,GAAMA,EAAG,MAAM,EAAE,KAAKA,GAAM,CACxE,GAAI,CAAC,KAAK,UAAW,OACrB,IAAMC,EAASD,EAAG,OAClB,KAAK,OAAS,IAAIC,EAAO,KAAK,UAAW,CACvC,MAAO,OACP,OAAQ,OACR,MAAOL,EACP,OAAQ,CACN,SAAAJ,EACA,SAAU,KAAK,MAAM,QACrB,KAAM,KAAK,MAAM,MACjB,MAAOM,EAAeP,CAAG,EACzB,OAAQ,OAAO,SAAS,OACxB,GAAGE,EAAO,MACZ,EACA,OAAQ,CACN,SAAU,KAAK,MAAM,QACrB,OAAQ,IAAM,KAAK,MAAM,OAAO,KAAK,OAAO,WAAW,EACvD,UAAW,KAAK,MAAM,QACtB,eAAgB,KAAK,iBACrB,MAAO,KAAK,MAAM,QAClB,QAAS,KAAK,MAAM,OACpB,QAAS,KAAK,MAAM,SACpB,MAAOS,GAASR,EAAQQ,CAAK,CAC/B,CACF,CAAC,CACH,EAAGR,CAAO,CACZ,CAOA,MAAQ,CACN,KAAK,WAAW,MAAM,CACxB,CAEA,OAAS,CACP,KAAK,WAAW,OAAO,CACzB,CAEA,MAAQ,CAER,CAEA,OAAQS,EAASC,EAAc,GAAM,CACnC,KAAK,WAAW,OAAQD,CAAO,EAC1BC,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,WAAW,YAAaA,CAAQ,CACvC,CAUA,aAAe,CACb,OAAO,KAAK,OAAO,UAAY,IACjC,CAEA,gBAAkB,CAChB,OAAO,KAAK,OAAO,WACrB,CAEA,kBAAoB,CAClB,OAAO,KAAK,OAAO,YACrB,CAMA,QAAU,CACR,GAAM,CAAE,QAAAC,CAAQ,EAAI,KAAK,MAMzB,OACE,GAAAC,QAAA,cAAC,OAAI,MANO,CACZ,MAAO,OACP,OAAQ,OACR,QAAAD,CACF,GAGI,GAAAC,QAAA,cAAC,OAAI,IAAK,KAAK,IAAK,CACtB,CAEJ,CACF,EAjHEpB,EADmBT,EACZ,cAAc,eACrBS,EAFmBT,EAEZ,UAAU8B,EAAQ,aACzBrB,EAHmBT,EAGZ,cAAc,MCZvB,IAAA+B,GAAA,GAAAC,EAAAD,GAAA,aAAAE,IAAA,IAAAC,GAKMC,GACAC,GAEeH,EARrBI,GAAAC,EAAA,KAAAJ,GAAiC,OAEjCK,IACAC,IAEML,GAAU,oDACVC,GAAa,WAEEH,EAArB,cAAsC,YAAU,CAAhD,kCAIEQ,EAAA,kBAAaC,GACbD,EAAA,gBAAW,MACXA,EAAA,mBAAc,MACdA,EAAA,qBAAgB,MA8ChBA,EAAA,YAAO,IAAM,CAEb,GAEAA,EAAA,cAAS,IAAM,CAEf,GAcAA,EAAA,WAAME,GAAU,CACd,KAAK,OAASA,CAChB,GAlEA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CAC/C,CAEA,KAAMC,EAAK,CACTC,EAAOV,GAASC,EAAU,EAAE,KAAKH,GAAY,CAC3C,KAAK,OAASA,EAAS,aAAa,KAAK,MAAM,EAC/C,KAAK,OAAO,MAAM,KAAK,IAAM,CAC3B,KAAK,OAAO,OAAO,KAAK,GAAG,KAAK,MAAM,MAAM,EAC5C,KAAK,OAAO,OAAO,MAAM,GAAG,KAAK,MAAM,OAAO,EAC9C,KAAK,OAAO,OAAO,MAAM,GAAG,KAAK,MAAM,OAAO,EAC9C,KAAK,OAAO,OAAO,MAAM,GAAG,KAAK,MAAM,KAAK,EAC5C,KAAK,OAAO,OAAO,SAAS,GAAG,CAACa,EAASC,IAAa,CACpD,KAAK,YAAcD,EACnB,KAAK,SAAWC,CAClB,CAAC,EACD,KAAK,MAAM,QAAQ,CACrB,CAAC,CACH,EAAG,KAAK,MAAM,OAAO,CACvB,CAEA,MAAQ,CACN,KAAK,WAAW,MAAM,CACxB,CAEA,OAAS,CACP,KAAK,WAAW,OAAO,CACzB,CAEA,MAAQ,CAER,CAEA,OAAQD,EAASE,EAAc,GAAM,CACnC,KAAK,WAAW,OAAQF,CAAO,EAC1BE,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CAErB,CAUA,aAAe,CACb,OAAO,KAAK,QACd,CAEA,gBAAkB,CAChB,OAAO,KAAK,WACd,CAEA,kBAAoB,CAClB,OAAO,IACT,CAMA,QAAU,CACR,GAAM,CAAE,IAAAL,EAAK,OAAAM,CAAO,EAAI,KAAK,MACvBC,EAAKP,EAAI,MAAMQ,EAAkB,EAAE,CAAC,EACpCC,EAAQ,CACZ,MAAO,OACP,OAAQ,MACV,EACMC,EAAQC,GAAY,CACxB,GAAGL,EAAO,QACV,KAAM,IAAIC,CAAE,GACd,CAAC,EAGD,OACE,GAAAK,QAAA,cAAC,UACC,IAAKL,EACL,IAAK,KAAK,IACV,MAAOE,EACP,IAAK,qDAAqDC,CAAK,GAC/D,YAAY,IACZ,MAAM,WACR,CAEJ,CACF,EApGEb,EADmBR,EACZ,cAAc,YACrBQ,EAFmBR,EAEZ,UAAUwB,EAAQ,UACzBhB,EAHmBR,EAGZ,cAAc,MCXvB,IAAAyB,GAAA,GAAAC,EAAAD,GAAA,aAAAE,KAAA,IAAAC,GAKMC,GACAC,GACAC,GAEeJ,GATrBK,GAAAC,EAAA,KAAAL,GAAiC,OAEjCM,IACAC,IAEMN,GAAU,uCACVC,GAAa,YACbC,GAAmB,eAEJJ,GAArB,cAAqC,YAAU,CAA/C,kCAGES,EAAA,kBAAaC,GA6DbD,EAAA,YAAO,IAAM,CACX,KAAK,UAAU,CAAC,CAClB,GAEAA,EAAA,cAAS,IAAM,CACT,KAAK,MAAM,SAAW,MACxB,KAAK,UAAU,KAAK,MAAM,MAAM,CAEpC,GAkBAA,EAAA,WAAME,GAAa,CACjB,KAAK,UAAYA,CACnB,GAvFA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CAC/C,CAEA,KAAMC,EAAK,CACT,GAAM,CAAE,QAAAC,EAAS,OAAAC,EAAQ,QAAAC,EAAS,WAAAC,CAAW,EAAI,KAAK,MAChDC,EAAKL,GAAOA,EAAI,MAAMM,EAAiB,EAAE,CAAC,EAC5C,KAAK,QACP,KAAK,KAAK,EAEZC,EAAOjB,GAASC,GAAYC,EAAgB,EAAE,KAAKJ,GAAW,CACvD,KAAK,YACVA,EAAQ,IAAI,iBAAiB,CAACoB,EAAMC,IAAW,CACzC,KAAK,SAGT,KAAK,OAASA,EACd,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,OAAQ,KAAK,MAAM,MAAM,EACxC,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,OAAQ,KAAK,MAAM,MAAM,EACxC,KAAK,OAAO,GAAG,iBAAkB,KAAK,MAAM,OAAO,EACrD,EAAGJ,CAAE,EACLjB,EAAQ,IAAI,aAAa,CACvB,KAAMiB,EACN,UAAW,KAAK,UAChB,SAAUJ,EAAU,EAAI,EACxB,GAAGC,EAAO,OACZ,CAAC,EACDd,EAAQ,IAAI,kBAAkBiB,CAAE,EAAE,KAAKK,GAAQ,CAC7C,KAAK,SAAWA,EAAK,kBACrBN,EAAWM,EAAK,iBAAiB,CACnC,CAAC,EACH,EAAGP,CAAO,CACZ,CAEA,MAAQ,CACN,KAAK,WAAW,MAAM,CACxB,CAEA,OAAS,CACP,KAAK,WAAW,OAAO,CACzB,CAEA,MAAQ,CACN,OAAO,UAAU,IAAI,cAAc,KAAK,MAAM,CAChD,CAEA,OAAQQ,EAAQC,EAAc,GAAM,CAClC,KAAK,WAAW,OAAQD,CAAM,EACzBC,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,WAAW,YAAaA,CAAQ,CACvC,CAYA,gBAAiBC,EAAM,CACrB,KAAK,WAAW,mBAAoBA,CAAI,CAC1C,CAEA,aAAe,CACb,OAAO,KAAK,QACd,CAEA,gBAAkB,CAChB,OAAO,KAAK,WAAW,aAAa,CACtC,CAEA,kBAAoB,CAClB,OAAO,IACT,CAMA,QAAU,CACR,GAAM,CAAE,QAAAC,CAAQ,EAAI,KAAK,MAMzB,OACE,GAAAC,QAAA,cAAC,OAAI,MANO,CACZ,MAAO,OACP,OAAQ,OACR,QAAAD,CACF,GAGI,GAAAC,QAAA,cAAC,OAAI,IAAK,KAAK,IAAK,CACtB,CAEJ,CACF,EA1GEnB,EADmBT,GACZ,cAAc,WACrBS,EAFmBT,GAEZ,UAAU6B,EAAQ,WCX3B,IAAAC,GAAA,GAAAC,EAAAD,GAAA,aAAAE,KAAA,IAAAC,GAKMC,GACAC,GAEeH,GARrBI,GAAAC,EAAA,KAAAJ,GAAiC,OAEjCK,IACAC,IAEML,GAAU,2CACVC,GAAa,WAEEH,GAArB,cAAqC,YAAU,CAA/C,kCAGEQ,EAAA,kBAAaC,GACbD,EAAA,gBAAW,MACXA,EAAA,mBAAc,MACdA,EAAA,qBAAgB,MAgEhBA,EAAA,YAAO,IAAM,CACX,KAAK,WAAW,MAAM,CACxB,GAEAA,EAAA,cAAS,IAAM,CACb,KAAK,WAAW,QAAQ,CAC1B,GAcAA,EAAA,WAAME,GAAU,CACd,KAAK,OAASA,CAChB,GApFA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CAC/C,CAEA,KAAMC,EAAK,CACTC,EAAOV,GAASC,EAAU,EAAE,KAAKU,GAAY,CACtC,KAAK,SACV,KAAK,OAAS,IAAIA,EAAS,OAAO,KAAK,MAAM,EAC7C,KAAK,OAAO,GAAG,QAAS,IAAM,CAG5B,WAAW,IAAM,CACf,KAAK,OAAO,QAAU,GACtB,KAAK,OAAO,QAAQ,KAAK,MAAM,IAAI,EAC/B,KAAK,MAAM,OACb,KAAK,OAAO,KAAK,EAEnB,KAAK,aAAa,KAAK,OAAQ,KAAK,KAAK,EACzC,KAAK,MAAM,QAAQ,CACrB,EAAG,GAAG,CACR,CAAC,EACH,EAAG,KAAK,MAAM,OAAO,CACvB,CAEA,aAAcC,EAAQC,EAAO,CAC3BD,EAAO,GAAG,OAAQC,EAAM,MAAM,EAC9BD,EAAO,GAAG,QAASC,EAAM,OAAO,EAChCD,EAAO,GAAG,QAASC,EAAM,OAAO,EAChCD,EAAO,GAAG,QAASC,EAAM,OAAO,EAChCD,EAAO,GAAG,aAAc,CAAC,CAAE,SAAAE,EAAU,QAAAC,CAAQ,IAAM,CACjD,KAAK,SAAWD,EAChB,KAAK,YAAcC,CACrB,CAAC,CACH,CAEA,MAAQ,CACN,KAAK,WAAW,MAAM,CACxB,CAEA,OAAS,CACP,KAAK,WAAW,OAAO,CACzB,CAEA,MAAQ,CAER,CAEA,OAAQA,EAASC,EAAc,GAAM,CACnC,KAAK,WAAW,iBAAkBD,CAAO,EACpCC,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,WAAW,YAAaA,CAAQ,CACvC,CAEA,QAASC,EAAM,CACb,KAAK,WAAW,UAAWA,CAAI,CACjC,CAUA,aAAe,CACb,OAAO,KAAK,QACd,CAEA,gBAAkB,CAChB,OAAO,KAAK,WACd,CAEA,kBAAoB,CAClB,OAAO,KAAK,aACd,CAMA,QAAU,CACR,IAAMC,EAAQ,CACZ,MAAO,OACP,OAAQ,MACV,EACA,OACE,GAAAC,QAAA,cAAC,UACC,IAAK,KAAK,IACV,IAAK,KAAK,MAAM,IAChB,YAAY,IACZ,UAAU,KACV,MAAOD,EACP,MAAM,yCACN,eAAe,6BACjB,CAEJ,CACF,EA9GEb,EADmBR,GACZ,cAAc,WACrBQ,EAFmBR,GAEZ,UAAUuB,EAAQ,WCV3B,IAAAC,GAAA,GAAAC,EAAAD,GAAA,aAAAE,KAAA,IAAAC,EAKMC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GAEed,GAnBrBe,GAAAC,EAAA,KAAAf,EAAiC,OAEjCgB,IACAC,IAEMhB,GAAgB,OAAO,WAAc,YACrCC,GAAcD,IAAiB,UAAU,WAAa,YAAc,UAAU,eAAiB,EAC/FE,GAASF,KAAkB,mBAAmB,KAAK,UAAU,SAAS,GAAKC,KAAgB,CAAC,OAAO,SACnGE,GAAYH,IAAkB,iCAAiC,KAAK,UAAU,SAAS,GAAM,CAAC,OAAO,SACrGI,GAAc,8DACdC,GAAa,MACbC,GAAe,wEACfC,GAAc,SACdC,GAAc,8DACdC,GAAa,QACbC,GAAoB,wBACpBC,GAA0B,sDAC1BC,GAA4B,qDAEbd,GAArB,cAAwC,WAAU,CAAlD,kCAiFEmB,EAAA,eAAU,IAAIC,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,GACjDD,EAAA,cAAS,IAAIC,IAAS,KAAK,MAAM,OAAO,GAAGA,CAAI,GAC/CD,EAAA,gBAAW,IAAIC,IAAS,KAAK,MAAM,SAAS,GAAGA,CAAI,GACnDD,EAAA,mBAAc,IAAIC,IAAS,KAAK,MAAM,YAAY,GAAGA,CAAI,GACzDD,EAAA,eAAU,IAAIC,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,GACjDD,EAAA,eAAU,IAAIC,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,GACjDD,EAAA,eAAU,IAAIC,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,GACjDD,EAAA,4BAAwBE,GAAU,KAAK,MAAM,qBAAqBA,EAAM,OAAO,YAAY,GAC3FF,EAAA,mBAAc,IAAIC,IAAS,KAAK,MAAM,YAAY,GAAGA,CAAI,GAEzDD,EAAA,oBAAe,GAAK,CAClB,GAAM,CAAE,aAAAG,EAAc,QAAAC,CAAQ,EAAI,KAAK,MACvCD,EAAa,CAAC,EACVC,GACF,KAAK,KAAK,CAEd,GAEAJ,EAAA,gCAA2B,GAAK,CAC9B,GAAI,KAAK,QAAUK,EAA+B,KAAK,MAAM,EAAG,CAC9D,GAAM,CAAE,uBAAAC,CAAuB,EAAI,KAAK,OACpCA,IAA2B,qBAC7B,KAAK,YAAY,CAAC,EACTA,IAA2B,UACpC,KAAK,aAAa,CAAC,CAEvB,CACF,GAEAN,EAAA,cAAS,GAAK,CACZ,KAAK,MAAM,OAAO,EAAE,OAAO,WAAW,CACxC,GA8HAA,EAAA,YAAO,IAAM,CACX,KAAK,OAAO,MAAQ,EACtB,GAEAA,EAAA,cAAS,IAAM,CACb,KAAK,OAAO,MAAQ,EACtB,GAqEAA,EAAA,2BAAsB,CAACO,EAAQC,IACzB,OAAOD,GAAW,SACb,EAAAE,QAAA,cAAC,UAAO,IAAKD,EAAO,IAAKD,EAAQ,EAEnC,EAAAE,QAAA,cAAC,UAAO,IAAKD,EAAQ,GAAGD,EAAQ,GAGzCP,EAAA,mBAAc,CAACU,EAAOF,IACb,EAAAC,QAAA,cAAC,SAAM,IAAKD,EAAQ,GAAGE,EAAO,GAGvCV,EAAA,WAAMW,GAAU,CACV,KAAK,SAEP,KAAK,WAAa,KAAK,QAEzB,KAAK,OAASA,CAChB,GAtUA,mBAAqB,CACnB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,EAC7C,KAAK,aAAa,KAAK,MAAM,EAC7B,IAAMC,EAAM,KAAK,UAAU,KAAK,MAAM,GAAG,EACrCA,IACF,KAAK,OAAO,IAAMA,IAEhB3B,IAAU,KAAK,MAAM,OAAO,kBAC9B,KAAK,OAAO,KAAK,CAErB,CAEA,mBAAoB4B,EAAW,CACzB,KAAK,eAAe,KAAK,KAAK,IAAM,KAAK,eAAeA,CAAS,IACnE,KAAK,gBAAgB,KAAK,WAAYA,EAAU,GAAG,EACnD,KAAK,aAAa,KAAK,MAAM,GAI7B,KAAK,MAAM,MAAQA,EAAU,KAC7B,CAACC,EAAc,KAAK,MAAM,GAAG,GAC7B,EAAE,KAAK,MAAM,eAAe,SAE5B,KAAK,OAAO,UAAY,KAE5B,CAEA,sBAAwB,CACtB,KAAK,OAAO,gBAAgB,KAAK,EACjC,KAAK,gBAAgB,KAAK,MAAM,EAC5B,KAAK,KACP,KAAK,IAAI,QAAQ,CAErB,CAEA,aAAcH,EAAQ,CACpB,GAAM,CAAE,IAAAI,EAAK,YAAAC,CAAY,EAAI,KAAK,MAClCL,EAAO,iBAAiB,OAAQ,KAAK,MAAM,EAC3CA,EAAO,iBAAiB,UAAW,KAAK,QAAQ,EAChDA,EAAO,iBAAiB,UAAW,KAAK,WAAW,EACnDA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,SAAU,KAAK,MAAM,EAC7CA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,aAAc,KAAK,oBAAoB,EAC/DA,EAAO,iBAAiB,wBAAyB,KAAK,WAAW,EACjEA,EAAO,iBAAiB,wBAAyB,KAAK,YAAY,EAClEA,EAAO,iBAAiB,gCAAiC,KAAK,wBAAwB,EACjF,KAAK,aAAaI,CAAG,GACxBJ,EAAO,iBAAiB,UAAW,KAAK,OAAO,EAE7CK,IACFL,EAAO,aAAa,cAAe,EAAE,EACrCA,EAAO,aAAa,qBAAsB,EAAE,EAC5CA,EAAO,aAAa,iBAAkB,EAAE,EAE5C,CAEA,gBAAiBA,EAAQI,EAAK,CAC5BJ,EAAO,oBAAoB,UAAW,KAAK,OAAO,EAClDA,EAAO,oBAAoB,OAAQ,KAAK,MAAM,EAC9CA,EAAO,oBAAoB,UAAW,KAAK,QAAQ,EACnDA,EAAO,oBAAoB,UAAW,KAAK,WAAW,EACtDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,SAAU,KAAK,MAAM,EAChDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,aAAc,KAAK,oBAAoB,EAClEA,EAAO,oBAAoB,wBAAyB,KAAK,WAAW,EACpEA,EAAO,oBAAoB,wBAAyB,KAAK,YAAY,EACrEA,EAAO,oBAAoB,gCAAiC,KAAK,wBAAwB,EACpF,KAAK,aAAaI,CAAG,GACxBJ,EAAO,oBAAoB,UAAW,KAAK,OAAO,CAEtD,CAoCA,eAAgBM,EAAO,CAIrB,OAHIA,EAAM,OAAO,YAGbA,EAAM,OAAO,WAAW,OACnB,GAEFC,EAAiB,KAAKD,EAAM,GAAG,GAAKA,EAAM,OAAO,UAC1D,CAEA,aAAcF,EAAK,CACjB,OAAK7B,IAAa,KAAK,MAAM,OAAO,gBAAmB,KAAK,MAAM,OAAO,SAChE,GAELD,IAAU,KAAK,MAAM,OAAO,gBACvB,GAEFkC,GAAe,KAAKJ,CAAG,GAAKrB,GAAwB,KAAKqB,CAAG,CACrE,CAEA,cAAeA,EAAK,CAClB,OAAOK,GAAgB,KAAKL,CAAG,GAAK,KAAK,MAAM,OAAO,SACxD,CAEA,aAAcA,EAAK,CACjB,OAAOM,GAAe,KAAKN,CAAG,GAAK,KAAK,MAAM,OAAO,QACvD,CAEA,KAAMA,EAAK,CACT,GAAM,CAAE,WAAAO,EAAY,WAAAC,EAAY,YAAAC,EAAa,WAAAC,CAAW,EAAI,KAAK,MAAM,OAmDvE,GAlDI,KAAK,KACP,KAAK,IAAI,QAAQ,EAEf,KAAK,MACP,KAAK,KAAK,MAAM,EAEd,KAAK,aAAaV,CAAG,GACvBW,EAAOvC,GAAY,QAAQ,UAAWmC,CAAU,EAAGlC,EAAU,EAAE,KAAKuC,GAAO,CAQzE,GAPA,KAAK,IAAM,IAAIA,EAAIJ,CAAU,EAC7B,KAAK,IAAI,GAAGI,EAAI,OAAO,gBAAiB,IAAM,CAC5C,KAAK,MAAM,QAAQ,CACrB,CAAC,EACD,KAAK,IAAI,GAAGA,EAAI,OAAO,MAAO,CAACC,EAAGC,IAAS,CACzC,KAAK,MAAM,QAAQD,EAAGC,EAAM,KAAK,IAAKF,CAAG,CAC3C,CAAC,EACGjC,GAAwB,KAAKqB,CAAG,EAAG,CACrC,IAAMe,EAAKf,EAAI,MAAMrB,EAAuB,EAAE,CAAC,EAC/C,KAAK,IAAI,WAAWC,GAA0B,QAAQ,OAAQmC,CAAE,CAAC,CACnE,MACE,KAAK,IAAI,WAAWf,CAAG,EAEzB,KAAK,IAAI,YAAY,KAAK,MAAM,EAChC,KAAK,MAAM,SAAS,CACtB,CAAC,EAEC,KAAK,cAAcA,CAAG,GACxBW,EAAOrC,GAAa,QAAQ,UAAWmC,CAAW,EAAGlC,EAAW,EAAE,KAAKyC,GAAU,CAC/E,KAAK,KAAOA,EAAO,YAAY,EAAE,OAAO,EACxC,KAAK,KAAK,WAAW,KAAK,OAAQhB,EAAK,KAAK,MAAM,OAAO,EACzD,KAAK,KAAK,GAAG,QAAS,KAAK,MAAM,OAAO,EACpC,SAASS,CAAW,EAAI,EAC1B,KAAK,KAAK,SAAS,EAAE,uBAAuB,EAAK,EAEjD,KAAK,KAAK,eAAe,CAAE,MAAO,CAAE,SAAUO,EAAO,MAAM,cAAe,CAAE,CAAC,EAE/E,KAAK,MAAM,SAAS,CACtB,CAAC,EAEC,KAAK,aAAahB,CAAG,GACvBW,EAAOnC,GAAY,QAAQ,UAAWkC,CAAU,EAAGjC,EAAU,EAAE,KAAKwC,GAAS,CAC3E,KAAK,IAAMA,EAAM,aAAa,CAAE,KAAM,MAAO,IAAAjB,CAAI,CAAC,EAClD,KAAK,IAAI,mBAAmB,KAAK,MAAM,EACvC,KAAK,IAAI,GAAGiB,EAAM,OAAO,MAAO,CAACJ,EAAGC,IAAS,CAC3C,KAAK,MAAM,QAAQD,EAAGC,EAAM,KAAK,IAAKG,CAAK,CAC7C,CAAC,EACD,KAAK,IAAI,KAAK,EACd,KAAK,MAAM,SAAS,CACtB,CAAC,EAGCjB,aAAe,MAKjB,KAAK,OAAO,KAAK,UACRD,EAAcC,CAAG,EAC1B,GAAI,CACF,KAAK,OAAO,UAAYA,CAC1B,MAAY,CACV,KAAK,OAAO,IAAM,OAAO,IAAI,gBAAgBA,CAAG,CAClD,CAEJ,CAEA,MAAQ,CACN,IAAMkB,EAAU,KAAK,OAAO,KAAK,EAC7BA,GACFA,EAAQ,MAAM,KAAK,MAAM,OAAO,CAEpC,CAEA,OAAS,CACP,KAAK,OAAO,MAAM,CACpB,CAEA,MAAQ,CACN,KAAK,OAAO,gBAAgB,KAAK,EAC7B,KAAK,MACP,KAAK,KAAK,MAAM,CAEpB,CAEA,OAAQC,EAASC,EAAc,GAAM,CACnC,KAAK,OAAO,YAAcD,EACrBC,GACH,KAAK,MAAM,CAEf,CAEA,UAAWC,EAAU,CACnB,KAAK,OAAO,OAASA,CACvB,CAUA,WAAa,CACP,KAAK,OAAO,yBAA2B,SAAS,0BAA4B,KAAK,OACnF,KAAK,OAAO,wBAAwB,EAC3B/B,EAA+B,KAAK,MAAM,GAAK,KAAK,OAAO,yBAA2B,sBAC/F,KAAK,OAAO,0BAA0B,oBAAoB,CAE9D,CAEA,YAAc,CACR,SAAS,sBAAwB,SAAS,0BAA4B,KAAK,OAC7E,SAAS,qBAAqB,EACrBA,EAA+B,KAAK,MAAM,GAAK,KAAK,OAAO,yBAA2B,UAC/F,KAAK,OAAO,0BAA0B,QAAQ,CAElD,CAEA,gBAAiBgC,EAAM,CACrB,GAAI,CACF,KAAK,OAAO,aAAeA,CAC7B,OAASC,EAAO,CACd,KAAK,MAAM,QAAQA,CAAK,CAC1B,CACF,CAEA,aAAe,CACb,GAAI,CAAC,KAAK,OAAQ,OAAO,KACzB,GAAM,CAAE,SAAAC,EAAU,SAAAC,CAAS,EAAI,KAAK,OAGpC,OAAID,IAAa,KAAYC,EAAS,OAAS,EACtCA,EAAS,IAAIA,EAAS,OAAS,CAAC,EAElCD,CACT,CAEA,gBAAkB,CAChB,OAAK,KAAK,OACH,KAAK,OAAO,YADM,IAE3B,CAEA,kBAAoB,CAClB,GAAI,CAAC,KAAK,OAAQ,OAAO,KACzB,GAAM,CAAE,SAAAE,CAAS,EAAI,KAAK,OAC1B,GAAIA,EAAS,SAAW,EACtB,MAAO,GAET,IAAMC,EAAMD,EAAS,IAAIA,EAAS,OAAS,CAAC,EACtCF,EAAW,KAAK,YAAY,EAClC,OAAIG,EAAMH,EACDA,EAEFG,CACT,CAEA,UAAW3B,EAAK,CACd,IAAM4B,EAAS,KAAK,aAAa5B,CAAG,EAC9B6B,EAAU,KAAK,cAAc7B,CAAG,EAChC8B,EAAS,KAAK,aAAa9B,CAAG,EACpC,GAAI,EAAAA,aAAe,OAASD,EAAcC,CAAG,GAAK4B,GAAUC,GAAWC,GAGvE,OAAIpD,GAAkB,KAAKsB,CAAG,EACrBA,EAAI,QAAQ,kBAAmB,2BAA2B,EAE5DA,CACT,CAqBA,QAAU,CACR,GAAM,CAAE,IAAAA,EAAK,QAAAX,EAAS,KAAA0C,EAAM,SAAAC,EAAU,MAAAC,EAAO,OAAAC,EAAQ,MAAAC,EAAO,OAAAC,CAAO,EAAI,KAAK,MAEtEC,EADW,KAAK,eAAe,KAAK,KAAK,EACpB,QAAU,QAC/BC,EAAQ,CACZ,MAAOH,IAAU,OAASA,EAAQ,OAClC,OAAQC,IAAW,OAASA,EAAS,MACvC,EACA,OACE,EAAA1C,QAAA,cAAC2C,EAAA,CACC,IAAK,KAAK,IACV,IAAK,KAAK,UAAUrC,CAAG,EACvB,MAAOsC,EACP,QAAQ,OACR,SAAUjD,GAAW,OACrB,SAAU2C,EACV,MAAOC,EACP,KAAMF,EACL,GAAGG,EAAO,YAEVlC,aAAe,OACdA,EAAI,IAAI,KAAK,mBAAmB,EACjCkC,EAAO,OAAO,IAAI,KAAK,WAAW,CACrC,CAEJ,CACF,EArWEjD,EADmBnB,GACZ,cAAc,cACrBmB,EAFmBnB,GAEZ,UAAUyE,EAAQ,QCrB3B,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAEA,IAAIC,GAAiB,OAAO,SAAY,YACpCC,GAAS,OAAO,KAAQ,WACxBC,GAAS,OAAO,KAAQ,WACxBC,GAAiB,OAAO,aAAgB,YAAc,CAAC,CAAC,YAAY,OAIxE,SAASC,GAAMC,EAAGC,EAAG,CAEnB,GAAID,IAAMC,EAAG,MAAO,GAEpB,GAAID,GAAKC,GAAK,OAAOD,GAAK,UAAY,OAAOC,GAAK,SAAU,CAC1D,GAAID,EAAE,cAAgBC,EAAE,YAAa,MAAO,GAE5C,IAAIC,EAAQC,EAAGC,EACf,GAAI,MAAM,QAAQJ,CAAC,EAAG,CAEpB,GADAE,EAASF,EAAE,OACPE,GAAUD,EAAE,OAAQ,MAAO,GAC/B,IAAKE,EAAID,EAAQC,MAAQ,GACvB,GAAI,CAACJ,GAAMC,EAAEG,CAAC,EAAGF,EAAEE,CAAC,CAAC,EAAG,MAAO,GACjC,MAAO,EACT,CAsBA,IAAIE,EACJ,GAAIT,IAAWI,aAAa,KAASC,aAAa,IAAM,CACtD,GAAID,EAAE,OAASC,EAAE,KAAM,MAAO,GAE9B,IADAI,EAAKL,EAAE,QAAQ,EACR,EAAEG,EAAIE,EAAG,KAAK,GAAG,MACtB,GAAI,CAACJ,EAAE,IAAIE,EAAE,MAAM,CAAC,CAAC,EAAG,MAAO,GAEjC,IADAE,EAAKL,EAAE,QAAQ,EACR,EAAEG,EAAIE,EAAG,KAAK,GAAG,MACtB,GAAI,CAACN,GAAMI,EAAE,MAAM,CAAC,EAAGF,EAAE,IAAIE,EAAE,MAAM,CAAC,CAAC,CAAC,EAAG,MAAO,GACpD,MAAO,EACT,CAEA,GAAIN,IAAWG,aAAa,KAASC,aAAa,IAAM,CACtD,GAAID,EAAE,OAASC,EAAE,KAAM,MAAO,GAE9B,IADAI,EAAKL,EAAE,QAAQ,EACR,EAAEG,EAAIE,EAAG,KAAK,GAAG,MACtB,GAAI,CAACJ,EAAE,IAAIE,EAAE,MAAM,CAAC,CAAC,EAAG,MAAO,GACjC,MAAO,EACT,CAGA,GAAIL,IAAkB,YAAY,OAAOE,CAAC,GAAK,YAAY,OAAOC,CAAC,EAAG,CAEpE,GADAC,EAASF,EAAE,OACPE,GAAUD,EAAE,OAAQ,MAAO,GAC/B,IAAKE,EAAID,EAAQC,MAAQ,GACvB,GAAIH,EAAEG,CAAC,IAAMF,EAAEE,CAAC,EAAG,MAAO,GAC5B,MAAO,EACT,CAEA,GAAIH,EAAE,cAAgB,OAAQ,OAAOA,EAAE,SAAWC,EAAE,QAAUD,EAAE,QAAUC,EAAE,MAK5E,GAAID,EAAE,UAAY,OAAO,UAAU,SAAW,OAAOA,EAAE,SAAY,YAAc,OAAOC,EAAE,SAAY,WAAY,OAAOD,EAAE,QAAQ,IAAMC,EAAE,QAAQ,EACnJ,GAAID,EAAE,WAAa,OAAO,UAAU,UAAY,OAAOA,EAAE,UAAa,YAAc,OAAOC,EAAE,UAAa,WAAY,OAAOD,EAAE,SAAS,IAAMC,EAAE,SAAS,EAKzJ,GAFAG,EAAO,OAAO,KAAKJ,CAAC,EACpBE,EAASE,EAAK,OACVF,IAAW,OAAO,KAAKD,CAAC,EAAE,OAAQ,MAAO,GAE7C,IAAKE,EAAID,EAAQC,MAAQ,GACvB,GAAI,CAAC,OAAO,UAAU,eAAe,KAAKF,EAAGG,EAAKD,CAAC,CAAC,EAAG,MAAO,GAKhE,GAAIR,IAAkBK,aAAa,QAAS,MAAO,GAGnD,IAAKG,EAAID,EAAQC,MAAQ,GACvB,GAAK,GAAAC,EAAKD,CAAC,IAAM,UAAYC,EAAKD,CAAC,IAAM,OAASC,EAAKD,CAAC,IAAM,QAAUH,EAAE,WAatE,CAACD,GAAMC,EAAEI,EAAKD,CAAC,CAAC,EAAGF,EAAEG,EAAKD,CAAC,CAAC,CAAC,EAAG,MAAO,GAK7C,MAAO,EACT,CAEA,OAAOH,IAAMA,GAAKC,IAAMA,CAC1B,CAGAP,GAAO,QAAU,SAAiBM,EAAGC,EAAG,CACtC,GAAI,CACF,OAAOF,GAAMC,EAAGC,CAAC,CACnB,OAASK,EAAO,CACd,IAAMA,EAAM,SAAW,IAAI,MAAM,kBAAkB,EAMjD,eAAQ,KAAK,gDAAgD,EACtD,GAGT,MAAMA,CACR,CACF,IC1IA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cASA,IAAIC,GAAuB,+CAE3BD,GAAO,QAAUC,KCXjB,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cASA,IAAIC,GAAuB,KAE3B,SAASC,IAAgB,CAAC,CAC1B,SAASC,IAAyB,CAAC,CACnCA,GAAuB,kBAAoBD,GAE3CF,GAAO,QAAU,UAAW,CAC1B,SAASI,EAAKC,EAAOC,EAAUC,EAAeC,EAAUC,EAAcC,EAAQ,CAC5E,GAAIA,IAAWT,GAIf,KAAIU,EAAM,IAAI,MACZ,iLAGF,EACA,MAAAA,EAAI,KAAO,sBACLA,EACR,CACAP,EAAK,WAAaA,EAClB,SAASQ,GAAU,CACjB,OAAOR,CACT,CAGA,IAAIS,EAAiB,CACnB,MAAOT,EACP,OAAQA,EACR,KAAMA,EACN,KAAMA,EACN,OAAQA,EACR,OAAQA,EACR,OAAQA,EACR,OAAQA,EAER,IAAKA,EACL,QAASQ,EACT,QAASR,EACT,YAAaA,EACb,WAAYQ,EACZ,KAAMR,EACN,SAAUQ,EACV,MAAOA,EACP,UAAWA,EACX,MAAOA,EACP,MAAOA,EAEP,eAAgBT,GAChB,kBAAmBD,EACrB,EAEA,OAAAW,EAAe,UAAYA,EAEpBA,CACT,IChEA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAiBEA,GAAO,QAAU,KAAsC,EATnD,IAAAC,GAIAC,KCZN,IAAAC,GAAA,GAAAC,EAAAD,GAAA,aAAAE,KAAA,IAAAC,EAEMC,GAEAC,GAEeH,GANrBI,GAAAC,EAAA,KAAAJ,EAAiC,OAE3BC,GAAY,OAEZC,GAAQ,CAAC,EAEMH,GAArB,cAAqC,WAAU,CAA/C,kCACEM,EAAA,eAAU,IACVA,EAAA,aAAQ,CACN,MAAO,IACT,GA0CAA,EAAA,sBAAiB,GAAK,EAChB,EAAE,MAAQ,SAAW,EAAE,MAAQ,MACjC,KAAK,MAAM,QAAQ,CAEvB,GA5CA,mBAAqB,CACnB,KAAK,QAAU,GACf,KAAK,WAAW,KAAK,KAAK,CAC5B,CAEA,mBAAoBC,EAAW,CAC7B,GAAM,CAAE,IAAAC,EAAK,MAAAC,CAAM,EAAI,KAAK,OACxBF,EAAU,MAAQC,GAAOD,EAAU,QAAUE,IAC/C,KAAK,WAAW,KAAK,KAAK,CAE9B,CAEA,sBAAwB,CACtB,KAAK,QAAU,EACjB,CAEA,WAAY,CAAE,IAAAD,EAAK,MAAAC,EAAO,UAAAC,CAAU,EAAG,CACrC,GAAI,GAAAC,QAAM,eAAeF,CAAK,EAG9B,IAAI,OAAOA,GAAU,SAAU,CAC7B,KAAK,SAAS,CAAE,MAAOA,CAAM,CAAC,EAC9B,MACF,CACA,GAAIN,GAAMK,CAAG,EAAG,CACd,KAAK,SAAS,CAAE,MAAOL,GAAMK,CAAG,CAAE,CAAC,EACnC,MACF,CACA,YAAK,SAAS,CAAE,MAAO,IAAK,CAAC,EACtB,OAAO,MAAME,EAAU,QAAQ,QAASF,CAAG,CAAC,EAChD,KAAKI,GAAYA,EAAS,KAAK,CAAC,EAChC,KAAKC,GAAQ,CACZ,GAAIA,EAAK,eAAiB,KAAK,QAAS,CACtC,IAAMC,EAAQD,EAAK,cAAc,QAAQ,aAAc,YAAY,EAAE,QAAQ,aAAc,QAAQ,EACnG,KAAK,SAAS,CAAE,MAAAC,CAAM,CAAC,EACvBX,GAAMK,CAAG,EAAIM,CACf,CACF,CAAC,EACL,CAQA,QAAU,CACR,GAAM,CAAE,MAAAL,EAAO,QAAAM,EAAS,SAAAC,EAAU,gBAAAC,EAAiB,iBAAAC,CAAiB,EAAI,KAAK,MACvE,CAAE,MAAAJ,CAAM,EAAI,KAAK,MACjBK,EAAY,EAAAR,QAAM,eAAeF,CAAK,EACtCW,EAAa,CACjB,QAAS,OACT,WAAY,SACZ,eAAgB,QAClB,EACMC,EAAS,CACb,QAAS,CACP,MAAO,OACP,OAAQ,OACR,gBAAiBP,GAAS,CAACK,EAAY,OAAOL,CAAK,IAAM,OACzD,eAAgB,QAChB,mBAAoB,SACpB,OAAQ,UACR,GAAGM,CACL,EACA,OAAQ,CACN,WAAY,2DACZ,aAAclB,GACd,MAAOA,GACP,OAAQA,GACR,SAAUiB,EAAY,WAAa,OACnC,GAAGC,CACL,EACA,SAAU,CACR,YAAa,QACb,YAAa,mBACb,YAAa,4CACb,WAAY,KACd,CACF,EACME,EACJ,EAAAX,QAAA,cAAC,OAAI,MAAOU,EAAO,OAAQ,UAAU,wBACnC,EAAAV,QAAA,cAAC,OAAI,MAAOU,EAAO,SAAU,UAAU,0BAA0B,CACnE,EAEF,OACE,EAAAV,QAAA,cAAC,OACC,MAAOU,EAAO,QACd,UAAU,wBACV,QAASN,EACT,SAAUE,EACV,WAAY,KAAK,eAChB,GAAIC,EAAmB,CAAE,aAAcA,CAAiB,EAAI,CAAC,GAE7DC,EAAYV,EAAQ,KACpBO,GAAYM,CACf,CAEJ,CACF,IC/GA,IAAAC,GAAA,GAAAC,EAAAD,GAAA,aAAAE,KCAAC,IACAC,IAEA,IAAOC,GAAQ,CACb,CACE,IAAK,UACL,KAAM,UACN,QAASC,EAAQ,QACjB,WAAYC,EAAK,IAAM,qCAA+D,CACxF,EACA,CACE,IAAK,aACL,KAAM,aACN,QAASD,EAAQ,WACjB,WAAYC,EAAK,IAAM,qCAAqE,CAC9F,EACA,CACE,IAAK,QACL,KAAM,QACN,QAASD,EAAQ,MACjB,WAAYC,EAAK,IAAM,qCAA2D,CACpF,EACA,CACE,IAAK,MACL,KAAM,MACN,QAASD,EAAQ,IACjB,WAAYC,EAAK,IAAM,qCAAuD,CAChF,EACA,CACE,IAAK,WACL,KAAM,WACN,QAASD,EAAQ,SACjB,WAAYC,EAAK,IAAM,qCAAiE,CAC1F,EACA,CACE,IAAK,aACL,KAAM,aACN,QAASD,EAAQ,WACjB,WAAYC,EAAK,IAAM,qCAAqE,CAC9F,EACA,CACE,IAAK,SACL,KAAM,SACN,QAASD,EAAQ,OACjB,WAAYC,EAAK,IAAM,qCAA6D,CACtF,EACA,CACE,IAAK,SACL,KAAM,SACN,QAASD,EAAQ,OACjB,WAAYC,EAAK,IAAM,qCAA6D,CACtF,EACA,CACE,IAAK,cACL,KAAM,cACN,QAASD,EAAQ,YACjB,WAAYC,EAAK,IAAM,qCAAuE,CAChG,EACA,CACE,IAAK,WACL,KAAM,WACN,QAASD,EAAQ,SACjB,WAAYC,EAAK,IAAM,qCAAiE,CAC1F,EACA,CACE,IAAK,UACL,KAAM,UACN,QAASD,EAAQ,QACjB,WAAYC,EAAK,IAAM,qCAA+D,CACxF,EACA,CACE,IAAK,UACL,KAAM,UACN,QAASD,EAAQ,QACjB,WAAYC,EAAK,IAAM,qCAA+D,CACxF,EACA,CACE,IAAK,OACL,KAAM,aACN,QAASD,EAAQ,KACjB,aAAcE,GACLF,EAAQ,KAAKE,CAAG,IAAM,SAAS,yBAA2BC,EAA+B,IAAM,CAACC,EAAiB,KAAKF,CAAG,EAElI,WAAYD,EAAK,IAAM,qCAAqE,CAC9F,CACF,ECrFA,IAAAI,EAA2C,OAC3CC,GAAkB,QCDlB,IAAIC,GAAY,OAAO,OACnB,SAAkBC,EAAO,CACrB,OAAO,OAAOA,GAAU,UAAYA,IAAUA,CAClD,EACJ,SAASC,GAAQC,EAAOC,EAAQ,CAI5B,MAHI,GAAAD,IAAUC,GAGVJ,GAAUG,CAAK,GAAKH,GAAUI,CAAM,EAI5C,CACA,SAASC,GAAeC,EAAWC,EAAY,CAC3C,GAAID,EAAU,SAAWC,EAAW,OAChC,MAAO,GAEX,QAASC,EAAI,EAAGA,EAAIF,EAAU,OAAQE,IAClC,GAAI,CAACN,GAAQI,EAAUE,CAAC,EAAGD,EAAWC,CAAC,CAAC,EACpC,MAAO,GAGf,MAAO,EACX,CAEA,SAASC,GAAWC,EAAUR,EAAS,CAC/BA,IAAY,SAAUA,EAAUG,IACpC,IAAIM,EACAC,EAAW,CAAC,EACZC,EACAC,EAAa,GACjB,SAASC,GAAW,CAEhB,QADIC,EAAU,CAAC,EACNC,EAAK,EAAGA,EAAK,UAAU,OAAQA,IACpCD,EAAQC,CAAE,EAAI,UAAUA,CAAE,EAE9B,OAAIH,GAAcH,IAAa,MAAQT,EAAQc,EAASJ,CAAQ,IAGhEC,EAAaH,EAAS,MAAM,KAAMM,CAAO,EACzCF,EAAa,GACbH,EAAW,KACXC,EAAWI,GACJH,CACX,CACA,OAAOE,CACX,CAEA,IAAOG,GAAQT,GD7Cf,IAAAU,GAAoB,QEHpB,IAAAC,GAAsB,QAEhB,CAAE,OAAAC,EAAQ,KAAAC,EAAM,OAAAC,GAAQ,MAAAC,GAAO,UAAAC,GAAW,MAAAC,EAAO,OAAAC,EAAQ,KAAAC,EAAM,KAAAC,EAAK,EAAI,GAAAC,QAEjEC,GAAY,CACvB,IAAKN,GAAU,CAACJ,EAAQG,GAAOG,CAAM,CAAC,EACtC,QAASL,EACT,KAAMA,EACN,SAAUA,EACV,OAAQC,GACR,MAAOD,EACP,aAAcC,GACd,MAAOE,GAAU,CAACJ,EAAQE,EAAM,CAAC,EACjC,OAAQE,GAAU,CAACJ,EAAQE,EAAM,CAAC,EAClC,MAAOI,EACP,iBAAkBJ,GAClB,YAAaD,EACb,IAAKA,EACL,cAAeA,EACf,MAAOG,GAAU,CAACH,EAAMD,EAAQM,CAAM,CAAC,EACvC,SAAUE,GACV,gBAAiBN,GACjB,iBAAkBF,EAClB,SAAUQ,GACV,UAAWR,EACX,QAASI,GAAU,CACjBJ,EACAO,EACAF,EAAM,CAAE,OAAQE,EAAK,UAAW,CAAC,CACnC,CAAC,EACD,OAAQF,EAAM,CACZ,WAAYA,EAAM,CAChB,QAASC,CACX,CAAC,EACD,QAASD,EAAM,CACb,WAAYC,EACZ,aAAcA,EACd,YAAaC,CACf,CAAC,EACD,SAAUF,EAAM,CACd,MAAOL,EACP,QAASA,EACT,SAAUA,EACV,WAAYM,CACd,CAAC,EACD,YAAaD,EAAM,CACjB,OAAQC,CACV,CAAC,EACD,MAAOD,EAAM,CACX,cAAeC,EACf,MAAON,CACT,CAAC,EACD,IAAKK,EAAM,CACT,WAAYC,EACZ,QAASN,CACX,CAAC,EACD,KAAMK,EAAM,CACV,WAAYC,EACZ,OAAQH,GACR,WAAYF,EACZ,WAAYA,EACZ,SAAUA,EACV,eAAgBA,EAChB,gBAAiBA,EACjB,UAAWA,EACX,SAAUA,EACV,WAAYK,EACZ,WAAYN,EACZ,YAAaA,EACb,WAAYA,CACd,CAAC,EACD,OAAQK,EAAM,CACZ,QAASC,EACT,SAAUN,EACV,eAAgBG,EAClB,CAAC,EACD,SAAUE,EAAM,CACd,QAASC,CACX,CAAC,EACD,OAAQD,EAAM,CACZ,QAASC,EACT,SAAUN,CACZ,CAAC,EACD,QAASK,EAAM,CACb,QAASC,CACX,CAAC,CACH,CAAC,EACD,QAASC,EACT,QAASA,EACT,OAAQA,EACR,QAASA,EACT,SAAUA,EACV,YAAaA,EACb,QAASA,EACT,QAASA,EACT,WAAYA,EACZ,OAAQA,EACR,qBAAsBA,EACtB,wBAAyBA,EACzB,WAAYA,EACZ,eAAgBA,EAChB,YAAaA,EACb,aAAcA,CAChB,EAEMI,EAAO,IAAM,CAAC,EAEPC,GAAe,CAC1B,QAAS,GACT,KAAM,GACN,SAAU,GACV,OAAQ,KACR,MAAO,GACP,aAAc,EACd,MAAO,QACP,OAAQ,QACR,MAAO,CAAC,EACR,iBAAkB,IAClB,YAAa,GACb,IAAK,GACL,cAAe,GACf,MAAO,GACP,SAAU,KACV,QAAS,MACT,gBAAiB,EACjB,iBAAkB,GAClB,UAAW,sCACX,OAAQ,CACN,WAAY,CACV,QAAS,CACP,OAAQ,GACR,OAAQ,GACR,OAAQ,GACR,SAAU,GACV,QAAS,GACT,cAAe,GACf,eAAgB,EAClB,CACF,EACA,QAAS,CACP,WAAY,CACV,YAAa,EACb,SAAU,EACV,IAAK,EACL,eAAgB,EAChB,eAAgB,CAClB,EACA,aAAc,CAAC,EACf,YAAaD,CACf,EACA,SAAU,CACR,MAAO,mBACP,QAAS,OACT,SAAU,KACV,WAAY,CAAC,CACf,EACA,YAAa,CACX,OAAQ,CACN,IAAK,EACL,mBAAoB,EACtB,CACF,EACA,MAAO,CACL,cAAe,CACb,UAAW,GACX,OAAQ,GACR,SAAU,GACV,MAAO,EACT,EACA,MAAO,IACT,EACA,IAAK,CACH,WAAY,CAAC,EACb,QAAS,GACX,EACA,KAAM,CACJ,WAAY,CAAC,EACb,OAAQ,CAAC,EACT,WAAY,GACZ,WAAY,GACZ,SAAU,GACV,UAAW,GACX,SAAU,GACV,WAAY,CAAC,EACb,WAAY,QACZ,YAAa,QACb,WAAY,QACZ,gBAAiB,EACnB,EACA,OAAQ,CACN,QAAS,CAAC,EACV,SAAU,KACV,eAAgB,IAClB,EACA,SAAU,CACR,QAAS,CACP,WAAY,CACd,CACF,EACA,OAAQ,CACN,QAAS,CAAC,EACV,SAAU,IACZ,EACA,QAAS,CACP,QAAS,CAAC,CACZ,CACF,EACA,QAASA,EACT,QAASA,EACT,OAAQA,EACR,QAASA,EACT,SAAUA,EACV,YAAaA,EACb,QAASA,EACT,QAASA,EACT,WAAYA,EACZ,OAAQA,EACR,qBAAsBA,EACtB,wBAAyBA,EACzB,WAAYA,EACZ,eAAgBA,EAChB,YAAaA,EACb,aAAcA,CAChB,EFzNAE,IGNA,IAAAC,GAAiC,OACjCC,GAAoB,QAGpBC,IAEA,IAAMC,GAAsB,IAEPC,EAArB,cAAoC,YAAU,CAA9C,kCAKEC,EAAA,eAAU,IACVA,EAAA,eAAU,IACVA,EAAA,iBAAY,IACZA,EAAA,iBAAY,IACZA,EAAA,mBAAc,MACdA,EAAA,mBAAc,IACdA,EAAA,kBAAa,MACbA,EAAA,wBAAmB,IAuEnBA,EAAA,yBAAoBC,GAAU,CAC5B,GAAI,KAAK,OAAQ,CACf,KAAK,SAAS,EACd,MACF,CACA,KAAK,OAASA,EACd,KAAK,OAAO,KAAK,KAAK,MAAM,GAAG,EAC/B,KAAK,SAAS,CAChB,GAiBAD,EAAA,yBAAqBE,GACd,KAAK,OACH,KAAK,OAAOA,CAAG,EADG,MAI3BF,EAAA,gBAAW,IAAM,CACf,GAAI,KAAK,MAAM,KAAO,KAAK,QAAU,KAAK,QAAS,CACjD,IAAMG,EAAgB,KAAK,eAAe,GAAK,EACzCC,EAAgB,KAAK,iBAAiB,EACtCC,EAAW,KAAK,YAAY,EAClC,GAAIA,EAAU,CACZ,IAAMC,EAAW,CACf,cAAAH,EACA,OAAQA,EAAgBE,CAC1B,EACID,IAAkB,OACpBE,EAAS,cAAgBF,EACzBE,EAAS,OAASF,EAAgBC,IAGhCC,EAAS,gBAAkB,KAAK,YAAcA,EAAS,gBAAkB,KAAK,aAChF,KAAK,MAAM,WAAWA,CAAQ,EAEhC,KAAK,WAAaA,EAAS,cAC3B,KAAK,WAAaA,EAAS,aAC7B,CACF,CACA,KAAK,gBAAkB,WAAW,KAAK,SAAU,KAAK,MAAM,mBAAqB,KAAK,MAAM,gBAAgB,CAC9G,GAyBAN,EAAA,mBAAc,IAAM,CAClB,GAAI,CAAC,KAAK,QAAS,OACnB,KAAK,QAAU,GACf,KAAK,UAAY,GACjB,GAAM,CAAE,QAAAO,EAAS,QAAAC,EAAS,OAAAC,EAAQ,MAAAC,CAAM,EAAI,KAAK,MACjDH,EAAQ,EACJ,CAACG,GAASD,IAAW,MACvB,KAAK,OAAO,UAAUA,CAAM,EAE1B,KAAK,aACP,KAAK,OAAO,KAAK,KAAK,YAAa,EAAI,EACvC,KAAK,YAAc,MACVD,GACT,KAAK,OAAO,KAAK,EAEnB,KAAK,oBAAoB,CAC3B,GAEAR,EAAA,kBAAa,IAAM,CACjB,KAAK,UAAY,GACjB,KAAK,UAAY,GACjB,GAAM,CAAE,QAAAW,EAAS,OAAAC,EAAQ,aAAAC,CAAa,EAAI,KAAK,MAC3C,KAAK,cACH,KAAK,OAAO,iBAAmBA,IAAiB,GAClD,KAAK,OAAO,gBAAgBA,CAAY,EAE1CF,EAAQ,EACR,KAAK,YAAc,IAErBC,EAAO,EACH,KAAK,aACP,KAAK,OAAO,KAAK,UAAU,EAC3B,KAAK,WAAa,MAEpB,KAAK,oBAAoB,CAC3B,GAEAZ,EAAA,mBAAe,GAAM,CACnB,KAAK,UAAY,GACZ,KAAK,WACR,KAAK,MAAM,QAAQ,CAAC,CAExB,GAEAA,EAAA,mBAAc,IAAM,CAClB,GAAM,CAAE,aAAAc,EAAc,KAAAC,EAAM,QAAAC,CAAQ,EAAI,KAAK,MACzCF,EAAa,aAAeC,GAC9B,KAAK,OAAO,CAAC,EAEVA,IACH,KAAK,UAAY,GACjBC,EAAQ,EAEZ,GAEAhB,EAAA,mBAAc,IAAIiB,IAAS,CACzB,KAAK,UAAY,GACjB,KAAK,MAAM,QAAQ,GAAGA,CAAI,CAC5B,GAEAjB,EAAA,2BAAsB,IAAM,CAC1B,aAAa,KAAK,oBAAoB,EACtC,IAAMK,EAAW,KAAK,YAAY,EAC9BA,EACG,KAAK,mBACR,KAAK,MAAM,WAAWA,CAAQ,EAC9B,KAAK,iBAAmB,IAG1B,KAAK,qBAAuB,WAAW,KAAK,oBAAqB,GAAG,CAExE,GAEAL,EAAA,oBAAe,IAAM,CAGnB,KAAK,UAAY,EACnB,GAhOA,mBAAqB,CACnB,KAAK,QAAU,EACjB,CAEA,sBAAwB,CACtB,aAAa,KAAK,eAAe,EACjC,aAAa,KAAK,oBAAoB,EAClC,KAAK,SAAW,KAAK,MAAM,gBAC7B,KAAK,OAAO,KAAK,EAEb,KAAK,OAAO,YACd,KAAK,OAAO,WAAW,GAG3B,KAAK,QAAU,EACjB,CAEA,mBAAoBkB,EAAW,CAE7B,GAAI,CAAC,KAAK,OACR,OAGF,GAAM,CAAE,IAAAC,EAAK,QAAAX,EAAS,OAAAC,EAAQ,MAAAC,EAAO,aAAAG,EAAc,IAAAO,EAAK,KAAAL,EAAM,aAAAD,EAAc,uBAAAO,CAAuB,EAAI,KAAK,MAC5G,GAAI,IAAC,GAAAC,SAAQJ,EAAU,IAAKC,CAAG,EAAG,CAChC,GAAI,KAAK,WAAa,CAACL,EAAa,WAAa,CAACO,GAA0B,CAACE,EAAcJ,CAAG,EAAG,CAC/F,QAAQ,KAAK,oCAAoCA,CAAG,gDAAgD,EACpG,KAAK,YAAcA,EACnB,MACF,CACA,KAAK,UAAY,GACjB,KAAK,YAAc,GACnB,KAAK,iBAAmB,GACxB,KAAK,OAAO,KAAKA,EAAK,KAAK,OAAO,CACpC,CACI,CAACD,EAAU,SAAWV,GAAW,CAAC,KAAK,WACzC,KAAK,OAAO,KAAK,EAEfU,EAAU,SAAW,CAACV,GAAW,KAAK,WACxC,KAAK,OAAO,MAAM,EAEhB,CAACU,EAAU,KAAOE,GAAO,KAAK,OAAO,WACvC,KAAK,OAAO,UAAU,EAEpBF,EAAU,KAAO,CAACE,GAAO,KAAK,OAAO,YACvC,KAAK,OAAO,WAAW,EAErBF,EAAU,SAAWT,GAAUA,IAAW,MAC5C,KAAK,OAAO,UAAUA,CAAM,EAE1BS,EAAU,QAAUR,IAClBA,EACF,KAAK,OAAO,KAAK,GAEjB,KAAK,OAAO,OAAO,EACfD,IAAW,MAEb,WAAW,IAAM,KAAK,OAAO,UAAUA,CAAM,CAAC,IAIhDS,EAAU,eAAiBL,GAAgB,KAAK,OAAO,iBACzD,KAAK,OAAO,gBAAgBA,CAAY,EAEtCK,EAAU,OAASH,GAAQ,KAAK,OAAO,SACzC,KAAK,OAAO,QAAQA,CAAI,CAE5B,CAYA,aAAe,CACb,OAAK,KAAK,QACH,KAAK,OAAO,YAAY,EADL,IAE5B,CAEA,gBAAkB,CAChB,OAAK,KAAK,QACH,KAAK,OAAO,eAAe,EADR,IAE5B,CAEA,kBAAoB,CAClB,OAAK,KAAK,QACH,KAAK,OAAO,iBAAiB,EADV,IAE5B,CAgCA,OAAQS,EAAQC,EAAMC,EAAa,CAEjC,GAAI,CAAC,KAAK,QAAS,CACbF,IAAW,IACb,KAAK,WAAaA,EAClB,WAAW,IAAM,CAAE,KAAK,WAAa,IAAK,EAAG1B,EAAmB,GAElE,MACF,CAEA,GADoB2B,EAAoCA,IAAS,WAArCD,EAAS,GAAKA,EAAS,EACnC,CAEd,IAAMnB,EAAW,KAAK,OAAO,YAAY,EACzC,GAAI,CAACA,EAAU,CACb,QAAQ,KAAK,iFAAyE,EACtF,MACF,CACA,KAAK,OAAO,OAAOA,EAAWmB,EAAQE,CAAW,EACjD,MACF,CACA,KAAK,OAAO,OAAOF,EAAQE,CAAW,CACxC,CAiFA,QAAU,CACR,IAAM3B,EAAS,KAAK,MAAM,aAC1B,OAAKA,EAIH,GAAA4B,QAAA,cAAC5B,EAAA,CACE,GAAG,KAAK,MACT,QAAS,KAAK,kBACd,QAAS,KAAK,YACd,OAAQ,KAAK,WACb,QAAS,KAAK,YACd,QAAS,KAAK,YACd,SAAU,KAAK,aACf,QAAS,KAAK,YAChB,EAZO,IAcX,CACF,EAjQEC,EADmBD,EACZ,cAAc,UACrBC,EAFmBD,EAEZ,YAAY6B,IACnB5B,EAHmBD,EAGZ,eAAe8B,IHFxB,IAAMC,GAAUC,EAAK,IAAM,qCAA+D,EAEpFC,GAAa,OAAO,QAAW,aAAe,OAAO,UAAY,OAAO,UAAa,YACrFC,GAAY,OAAO,QAAW,aAAe,OAAO,QAAU,OAAO,OAAO,SAC5EC,GAAkB,OAAO,KAAKC,EAAS,EAIvCC,GAAoBJ,IAAcC,GAAY,WAAW,IAAM,KAE/DI,GAAgB,CAAC,EAEVC,GAAoB,CAACC,EAASC,IAAa,CArBxD,IAAAC,EAsBE,OAAOA,EAAA,cAA0B,WAAU,CAApC,kCAyBLC,EAAA,aAAQ,CACN,YAAa,CAAC,CAAC,KAAK,MAAM,KAC5B,GAGAA,EAAA,kBAAa,CACX,QAASC,GAAW,CAAE,KAAK,QAAUA,CAAQ,EAC7C,OAAQC,GAAU,CAAE,KAAK,OAASA,CAAO,CAC3C,GAgBAF,EAAA,0BAAsBG,GAAM,CAC1B,KAAK,SAAS,CAAE,YAAa,EAAM,CAAC,EACpC,KAAK,MAAM,eAAeA,CAAC,CAC7B,GAEAH,EAAA,mBAAc,IAAM,CAClB,KAAK,SAAS,CAAE,YAAa,EAAK,CAAC,CACrC,GAEAA,EAAA,mBAAc,IACP,KAAK,OACH,KAAK,OAAO,YAAY,EADN,MAI3BA,EAAA,sBAAiB,IACV,KAAK,OACH,KAAK,OAAO,eAAe,EADT,MAI3BA,EAAA,wBAAmB,IACZ,KAAK,OACH,KAAK,OAAO,iBAAiB,EADX,MAI3BA,EAAA,yBAAoB,CAACI,EAAM,WACpB,KAAK,OACH,KAAK,OAAO,kBAAkBA,CAAG,EADf,MAI3BJ,EAAA,cAAS,CAACK,EAAUC,EAAMC,IAAgB,CACxC,GAAI,CAAC,KAAK,OAAQ,OAAO,KACzB,KAAK,OAAO,OAAOF,EAAUC,EAAMC,CAAW,CAChD,GAEAP,EAAA,mBAAc,IAAM,CAClB,KAAK,MAAM,QAAQ,IAAI,CACzB,GAEAA,EAAA,uBAAkBQ,GAAQC,GAAO,CAC/B,QAAWP,IAAU,CAAC,GAAGP,GAAe,GAAGE,CAAO,EAChD,GAAIK,EAAO,QAAQO,CAAG,EACpB,OAAOP,EAGX,OAAIJ,GAGG,IACT,CAAC,GAEDE,EAAA,iBAAYQ,GAAQ,CAACC,EAAKL,IAAQ,CAChC,GAAM,CAAE,OAAAM,CAAO,EAAI,KAAK,MACxB,OAAO,GAAAC,QAAM,IAAI,CACfC,GAAa,OACbA,GAAa,OAAOR,CAAG,GAAK,CAAC,EAC7BM,EACAA,EAAON,CAAG,GAAK,CAAC,CAClB,CAAC,CACH,CAAC,GAEDJ,EAAA,qBAAgBQ,GAAQC,GACfI,GAAK,KAAK,MAAOrB,EAAe,CACxC,GAkBDQ,EAAA,0BAAqBS,GAAO,CAC1B,GAAI,CAACA,EAAK,OAAO,KACjB,IAAMP,EAAS,KAAK,gBAAgBO,CAAG,EACvC,GAAI,CAACP,EAAQ,OAAO,KACpB,IAAMQ,EAAS,KAAK,UAAUD,EAAKP,EAAO,GAAG,EAC7C,OACE,EAAAY,QAAA,cAACC,EAAA,CACE,GAAG,KAAK,MACT,IAAKb,EAAO,IACZ,IAAK,KAAK,WAAW,OACrB,OAAQQ,EACR,aAAcR,EAAO,YAAcA,EACnC,QAAS,KAAK,YAChB,CAEJ,GA7GA,sBAAuBc,EAAWC,EAAW,CAC3C,MAAO,IAAC,GAAAC,SAAQ,KAAK,MAAOF,CAAS,GAAK,IAAC,GAAAE,SAAQ,KAAK,MAAOD,CAAS,CAC1E,CAEA,mBAAoBE,EAAW,CAC7B,GAAM,CAAE,MAAAC,CAAM,EAAI,KAAK,MACnB,CAACD,EAAU,OAASC,GACtB,KAAK,SAAS,CAAE,YAAa,EAAK,CAAC,EAEjCD,EAAU,OAAS,CAACC,GACtB,KAAK,SAAS,CAAE,YAAa,EAAM,CAAC,CAExC,CAkEA,cAAeX,EAAK,CAClB,GAAI,CAACA,EAAK,OAAO,KACjB,GAAM,CAAE,MAAAW,EAAO,SAAAC,EAAU,gBAAAC,EAAiB,UAAAC,EAAW,iBAAAC,CAAiB,EAAI,KAAK,MAC/E,OACE,EAAAV,QAAA,cAAC1B,GAAA,CACC,IAAKqB,EACL,MAAOW,EACP,SAAUC,EACV,gBAAiBC,EACjB,iBAAkBE,EAClB,UAAWD,EACX,QAAS,KAAK,mBAChB,CAEJ,CAmBA,QAAU,CACR,GAAM,CAAE,IAAAd,EAAK,MAAAgB,EAAO,MAAAC,EAAO,OAAAC,EAAQ,SAAA7B,EAAU,QAAS8B,CAAQ,EAAI,KAAK,MACjE,CAAE,YAAAC,CAAY,EAAI,KAAK,MACvBC,EAAa,KAAK,cAAcrB,CAAG,EACnCsB,EAAa,OAAOH,GAAY,SAAW,KAAK,WAAW,QAAU,OAC3E,OACE,EAAAd,QAAA,cAACc,EAAA,CAAQ,IAAKG,EAAY,MAAO,CAAE,GAAGN,EAAO,MAAAC,EAAO,OAAAC,CAAO,EAAI,GAAGG,GAChE,EAAAhB,QAAA,cAACpB,GAAA,CAAkB,SAAUI,GAC1B+B,EACG,KAAK,cAAcpB,CAAG,EACtB,KAAK,mBAAmBA,CAAG,CACjC,CACF,CAEJ,CACF,EAhKET,EADKD,EACE,cAAc,eACrBC,EAFKD,EAEE,YAAYN,IACnBO,EAHKD,EAGE,eAAea,IACtBZ,EAJKD,EAIE,kBAAkBG,GAAU,CAAEP,GAAc,KAAKO,CAAM,CAAE,GAChEF,EALKD,EAKE,sBAAsB,IAAM,CAAEJ,GAAc,OAAS,CAAE,GAE9DK,EAPKD,EAOE,UAAUU,GAAO,CACtB,QAAWM,IAAU,CAAC,GAAGpB,GAAe,GAAGE,CAAO,EAChD,GAAIkB,EAAO,QAAQN,CAAG,EACpB,MAAO,GAGX,MAAO,EACT,GAEAT,EAhBKD,EAgBE,eAAeU,GAAO,CAC3B,QAAWM,IAAU,CAAC,GAAGpB,GAAe,GAAGE,CAAO,EAChD,GAAIkB,EAAO,cAAgBA,EAAO,aAAaN,CAAG,EAChD,MAAO,GAGX,MAAO,EACT,GAvBKV,CAkKT,EFpLA,IAAMiC,GAAWC,GAAQA,GAAQ,OAAS,CAAC,EAEpCC,GAAQC,GAAkBF,GAASD,EAAQ", "names": ["require_react", "__commonJSMin", "exports", "module", "require_load_script", "__commonJSMin", "exports", "module", "src", "opts", "cb", "head", "script", "setAttributes", "onend", "stdOnEnd", "ieOnEnd", "attrs", "attr", "require_cjs", "__commonJSMin", "exports", "module", "isMergeableObject", "value", "isNonNullObject", "isSpecial", "stringValue", "isReactElement", "canUseSymbol", "REACT_ELEMENT_TYPE", "emptyTarget", "val", "cloneUnlessOtherwiseSpecified", "options", "deepmerge", "defaultArrayMerge", "target", "source", "element", "getMergeFunction", "key", "customMerge", "getEnumerableOwnPropertySymbols", "symbol", "get<PERSON><PERSON><PERSON>", "propertyIsOnObject", "object", "property", "propertyIsUnsafe", "mergeObject", "destination", "sourceIsArray", "targetIsArray", "sourceAndTargetTypesMatch", "array", "prev", "next", "deepmerge_1", "parseTimeParam", "url", "pattern", "match", "stamp", "MATCH_START_STAMP", "parseTimeString", "MATCH_NUMERIC", "seconds", "array", "count", "period", "parseStartTime", "MATCH_START_QUERY", "parseEndTime", "MATCH_END_QUERY", "randomString", "queryString", "object", "key", "getGlobal", "omit", "arrays", "omit<PERSON><PERSON><PERSON>", "output", "keys", "callPlayer", "method", "args", "message", "isMediaStream", "isBlobUrl", "supportsWebKitPresentationMode", "video", "notMobile", "import_react", "import_load_script", "import_deepmerge", "lazy", "requests", "getSDK", "init_utils", "__esmMin", "componentImportFn", "React", "obj", "sdkGlobal", "sdkReady", "isLoaded", "fetchScript", "loadScript", "existingGlobal", "resolve", "reject", "onLoaded", "sdk", "request", "previousOnReady", "err", "MATCH_URL_YOUTUBE", "MATCH_URL_SOUNDCLOUD", "MATCH_URL_VIMEO", "MATCH_URL_MUX", "MATCH_URL_FACEBOOK", "MATCH_URL_FACEBOOK_WATCH", "MATCH_URL_STREAMABLE", "MATCH_URL_WISTIA", "MATCH_URL_TWITCH_VIDEO", "MATCH_URL_TWITCH_CHANNEL", "MATCH_URL_DAILYMOTION", "MATCH_URL_MIXCLOUD", "MATCH_URL_VIDYARD", "MATCH_URL_KALTURA", "AUDIO_EXTENSIONS", "VIDEO_EXTENSIONS", "HLS_EXTENSIONS", "DASH_EXTENSIONS", "FLV_EXTENSIONS", "canPlayFile", "canPlay", "init_patterns", "__esmMin", "init_utils", "url", "item", "isMediaStream", "isBlobUrl", "YouTube_exports", "__export", "YouTube", "import_react", "SDK_URL", "SDK_GLOBAL", "SDK_GLOBAL_READY", "MATCH_PLAYLIST", "MATCH_USER_UPLOADS", "MATCH_NOCOOKIE", "NOCOOKIE_HOST", "init_YouTube", "__esmMin", "init_utils", "init_patterns", "__publicField", "callPlayer", "url", "playlistId", "username", "event", "data", "onPlay", "onPause", "onBuffer", "onBufferEnd", "onEnded", "onReady", "loop", "playerVars", "onUnstarted", "UNSTARTED", "PLAYING", "PAUSED", "BUFFERING", "ENDED", "CUED", "isPlaylist", "container", "MATCH_URL_YOUTUBE", "isReady", "playing", "muted", "playsinline", "controls", "config", "onError", "embedOptions", "id", "parseStartTime", "parseEndTime", "getSDK", "YT", "amount", "keepPlaying", "fraction", "rate", "display", "React", "canPlay", "SoundCloud_exports", "__export", "SoundCloud", "import_react", "SDK_URL", "SDK_GLOBAL", "init_SoundCloud", "__esmMin", "init_utils", "init_patterns", "__publicField", "callPlayer", "iframe", "url", "isReady", "getSDK", "SC", "PLAY", "PLAY_PROGRESS", "PAUSE", "FINISH", "ERROR", "e", "duration", "seconds", "keepPlaying", "fraction", "display", "style", "React", "canPlay", "Vimeo_exports", "__export", "Vimeo", "import_react", "SDK_URL", "SDK_GLOBAL", "cleanUrl", "init_Vimeo", "__esmMin", "init_utils", "init_patterns", "url", "__publicField", "callPlayer", "container", "getSDK", "playerOptions", "title", "iframe", "e", "seconds", "duration", "promise", "keepPlaying", "fraction", "muted", "loop", "rate", "display", "style", "React", "canPlay", "Mux_exports", "__export", "<PERSON><PERSON>", "import_react", "SDK_URL", "init_Mux", "__esmMin", "init_patterns", "__publicField", "args", "event", "duration", "player", "playbackId", "playsinline", "url", "_a", "onError", "config", "error", "id", "MATCH_URL_MUX", "promise", "seconds", "keepPlaying", "fraction", "rate", "seekable", "buffered", "end", "playing", "loop", "controls", "muted", "width", "height", "style", "React", "canPlay", "Facebook_exports", "__export", "Facebook", "import_react", "SDK_URL", "SDK_GLOBAL", "SDK_GLOBAL_READY", "PLAYER_ID_PREFIX", "init_Facebook", "__esmMin", "init_utils", "init_patterns", "__publicField", "callPlayer", "randomString", "url", "isReady", "getSDK", "FB", "msg", "seconds", "keepPlaying", "fraction", "attributes", "React", "canPlay", "Streamable_exports", "__export", "Streamable", "import_react", "SDK_URL", "SDK_GLOBAL", "init_Streamable", "__esmMin", "init_utils", "init_patterns", "__publicField", "callPlayer", "iframe", "url", "getSDK", "playerjs", "duration", "seconds", "percent", "keepPlaying", "fraction", "loop", "id", "MATCH_URL_STREAMABLE", "style", "React", "canPlay", "Wistia_exports", "__export", "Wistia", "import_react", "SDK_URL", "SDK_GLOBAL", "PLAYER_ID_PREFIX", "init_Wistia", "__esmMin", "init_utils", "init_patterns", "__publicField", "callPlayer", "randomString", "args", "url", "playing", "muted", "controls", "onReady", "config", "onError", "getSDK", "control", "player", "seconds", "keepPlaying", "fraction", "rate", "videoID", "MATCH_URL_WISTIA", "className", "style", "React", "canPlay", "Twitch_exports", "__export", "Twitch", "import_react", "SDK_URL", "SDK_GLOBAL", "PLAYER_ID_PREFIX", "init_Twitch", "__esmMin", "init_utils", "init_patterns", "__publicField", "callPlayer", "randomString", "url", "isReady", "playsinline", "onError", "config", "controls", "isChannel", "MATCH_URL_TWITCH_CHANNEL", "id", "MATCH_URL_TWITCH_VIDEO", "getSDK", "parseStartTime", "READY", "PLAYING", "PAUSE", "ENDED", "ONLINE", "OFFLINE", "SEEK", "seconds", "keepPlaying", "fraction", "React", "canPlay", "DailyMotion_exports", "__export", "DailyMotion", "import_react", "SDK_URL", "SDK_GLOBAL", "SDK_GLOBAL_READY", "init_DailyMotion", "__esmMin", "init_utils", "init_patterns", "__publicField", "callPlayer", "duration", "container", "url", "controls", "config", "onError", "playing", "id", "MATCH_URL_DAILYMOTION", "parseStartTime", "getSDK", "DM", "Player", "event", "seconds", "keepPlaying", "fraction", "display", "React", "canPlay", "Mixcloud_exports", "__export", "Mixcloud", "import_react", "SDK_URL", "SDK_GLOBAL", "init_Mixcloud", "__esmMin", "init_utils", "init_patterns", "__publicField", "callPlayer", "iframe", "url", "getSDK", "seconds", "duration", "keepPlaying", "fraction", "config", "id", "MATCH_URL_MIXCLOUD", "style", "query", "queryString", "React", "canPlay", "Vidyard_exports", "__export", "<PERSON><PERSON><PERSON>", "import_react", "SDK_URL", "SDK_GLOBAL", "SDK_GLOBAL_READY", "init_Vidyard", "__esmMin", "init_utils", "init_patterns", "__publicField", "callPlayer", "container", "url", "playing", "config", "onError", "onDuration", "id", "MATCH_URL_VIDYARD", "getSDK", "data", "player", "meta", "amount", "keepPlaying", "fraction", "rate", "display", "React", "canPlay", "Kaltura_exports", "__export", "<PERSON><PERSON><PERSON>", "import_react", "SDK_URL", "SDK_GLOBAL", "init_Kaltura", "__esmMin", "init_utils", "init_patterns", "__publicField", "callPlayer", "iframe", "url", "getSDK", "playerjs", "player", "props", "duration", "seconds", "keepPlaying", "fraction", "loop", "style", "React", "canPlay", "FilePlayer_exports", "__export", "FilePlayer", "import_react", "HAS_NAVIGATOR", "IS_IPAD_PRO", "IS_IOS", "IS_SAFARI", "HLS_SDK_URL", "HLS_GLOBAL", "DASH_SDK_URL", "DASH_GLOBAL", "FLV_SDK_URL", "FLV_GLOBAL", "MATCH_DROPBOX_URL", "MATCH_CLOUDFLARE_STREAM", "REPLACE_CLOUDFLARE_STREAM", "init_FilePlayer", "__esmMin", "init_utils", "init_patterns", "__publicField", "args", "event", "onDisablePIP", "playing", "supportsWebKitPresentationMode", "webkitPresentationMode", "source", "index", "React", "track", "player", "src", "prevProps", "isMediaStream", "url", "playsinline", "props", "AUDIO_EXTENSIONS", "HLS_EXTENSIONS", "DASH_EXTENSIONS", "FLV_EXTENSIONS", "hlsVersion", "hlsOptions", "dashVersion", "flvVersion", "getSDK", "Hls", "e", "data", "id", "dashjs", "flvjs", "promise", "seconds", "keepPlaying", "fraction", "rate", "error", "duration", "seekable", "buffered", "end", "useHLS", "useDASH", "useFLV", "loop", "controls", "muted", "config", "width", "height", "Element", "style", "canPlay", "require_react_fast_compare", "__commonJSMin", "exports", "module", "hasElementType", "hasMap", "hasSet", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "equal", "a", "b", "length", "i", "keys", "it", "error", "require_ReactPropTypesSecret", "__commonJSMin", "exports", "module", "ReactPropTypesSecret", "require_factoryWithThrowingShims", "__commonJSMin", "exports", "module", "ReactPropTypesSecret", "emptyFunction", "emptyFunctionWithReset", "shim", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "secret", "err", "getShim", "ReactPropTypes", "require_prop_types", "__commonJSMin", "exports", "module", "ReactIs", "throwOnDirectAccess", "Preview_exports", "__export", "Preview", "import_react", "ICON_SIZE", "cache", "init_Preview", "__esmMin", "__publicField", "prevProps", "url", "light", "oEmbedUrl", "React", "response", "data", "image", "onClick", "playIcon", "previewTabIndex", "previewAriaLabel", "isElement", "flexCenter", "styles", "defaultPlayIcon", "src_exports", "__export", "src_default", "init_utils", "init_patterns", "players_default", "canPlay", "lazy", "url", "supportsWebKitPresentationMode", "AUDIO_EXTENSIONS", "import_react", "import_deepmerge", "safeIsNaN", "value", "isEqual", "first", "second", "areInputsEqual", "newInputs", "lastInputs", "i", "memoizeOne", "resultFn", "lastThis", "lastArgs", "lastResult", "calledOnce", "memoized", "newArgs", "_i", "memoize_one_esm_default", "import_react_fast_compare", "import_prop_types", "string", "bool", "number", "array", "oneOfType", "shape", "object", "func", "node", "PropTypes", "propTypes", "noop", "defaultProps", "init_utils", "import_react", "import_react_fast_compare", "init_utils", "SEEK_ON_PLAY_EXPIRY", "Player", "__publicField", "player", "key", "playedSeconds", "loadedSeconds", "duration", "progress", "onReady", "playing", "volume", "muted", "onStart", "onPlay", "playbackRate", "activePlayer", "loop", "onEnded", "args", "prevProps", "url", "pip", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isEqual", "isMediaStream", "amount", "type", "keepPlaying", "React", "propTypes", "defaultProps", "Preview", "lazy", "IS_BROWSER", "IS_GLOBAL", "SUPPORTED_PROPS", "propTypes", "UniversalSuspense", "customPlayers", "createReactPlayer", "players", "fallback", "_a", "__publicField", "wrapper", "player", "e", "key", "fraction", "type", "keepPlaying", "memoize_one_esm_default", "url", "config", "merge", "defaultProps", "omit", "React", "Player", "nextProps", "nextState", "isEqual", "prevProps", "light", "playIcon", "previewTabIndex", "oEmbedUrl", "previewAriaLabel", "style", "width", "height", "Wrapper", "showPreview", "attributes", "wrapperRef", "fallback", "players_default", "src_default", "createReactPlayer"]}