{"name": "@mui/styled-engine", "version": "5.18.0", "private": false, "author": "MUI Team", "description": "styled() API wrapper package for emotion.", "main": "./node/index.js", "keywords": ["react", "react-component", "mui", "emotion"], "repository": {"type": "git", "url": "https://github.com/mui/material-ui.git", "directory": "packages/mui-styled-engine"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://v5.mui.com/system/styled/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "dependencies": {"@babel/runtime": "^7.23.9", "@emotion/cache": "^11.13.5", "@emotion/serialize": "^1.3.3", "csstype": "^3.1.3", "prop-types": "^15.8.1"}, "peerDependencies": {"@emotion/react": "^11.4.1", "@emotion/styled": "^11.3.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "engines": {"node": ">=12.0.0"}, "module": "./index.js", "types": "./index.d.ts"}