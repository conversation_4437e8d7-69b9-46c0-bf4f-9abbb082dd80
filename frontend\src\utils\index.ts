// Utility functions for Islamic AI Animation Maker

/**
 * Format duration from seconds to readable format
 */
export const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * Format file size to readable format
 */
export const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

/**
 * Validate Islamic story content
 */
export const validateStoryContent = (content: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!content.trim()) {
    errors.push('Story content is required');
  }
  
  if (content.length < 50) {
    errors.push('Story should be at least 50 characters long');
  }
  
  if (content.length > 5000) {
    errors.push('Story should not exceed 5000 characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Get language direction for RTL languages
 */
export const getLanguageDirection = (language: string): 'ltr' | 'rtl' => {
  const rtlLanguages = ['arabic', 'urdu'];
  return rtlLanguages.includes(language) ? 'rtl' : 'ltr';
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

/**
 * Generate unique ID
 */
export const generateId = (): string => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
};

/**
 * Debounce function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Check if URL is valid
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Islamic greeting based on time
 */
export const getIslamicGreeting = (): string => {
  const hour = new Date().getHours();
  
  if (hour < 12) {
    return 'Assalamu Alaikum! Good morning';
  } else if (hour < 17) {
    return 'Assalamu Alaikum! Good afternoon';
  } else {
    return 'Assalamu Alaikum! Good evening';
  }
};
