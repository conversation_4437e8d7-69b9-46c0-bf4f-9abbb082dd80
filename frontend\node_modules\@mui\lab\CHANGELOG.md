# [Versions](https://mui.com/versions/)

## 5.18.0

<!-- generated comparing v5.17.1..v5.x -->

_Jun 26, 2025_

A big thanks to the 2 contributors who made this release possible.

CSS layers make it easier to override styles by splitting a single style sheet into multiple layers.
To learn more, check out the [CSS layers documentation](https://v5.mui.com/material-ui/customization/css-layers/).

### `@mui/system@5.18.0`

- Backport CSS layers from v7 (#46320) @siriwatknp

### `@mui/material-nextjs@5.18.0`

- Backport CSS layers from v7 (#46320) @siriwatknp

### Docs

- Ease migration to v5 (#45991) @oliviertassinari
- Add AccordionSummary to the breaking change migration (@siriwatknp) (#45972) @siriwatknp

All contributors of this release in alphabetical order: @oliviertassinari, @siriwatknp

## v5.17.1

<!-- generated comparing v5.17.0..v5.x -->

_Mar 18, 2025_

This release fixes the `@mui/types` dependencies in all packages to fix a package layout bug (#45612) @DiegoAndai

## v5.17.0

_Mar 18, 2025_

A big thanks to the 2 contributors who made this release possible.

### `@mui/material@5.17.0`

- [TextareaAutosize] Temporarily disconnect ResizeObserver to avoid loop error (#44540) (#45238) @DiegoAndai
- Support nested theme when upper theme is CSS vars theme (#45604) @siriwatknp

All contributors of this release in alphabetical order: @DiegoAndai, @siriwatknp

## v5.16.14

<!-- generated comparing v5.16.13..v5.x -->

_Jan 6, 2025_

A big thanks to the 1 contributor who made this release possible.

### `@mui/material@5.16.14`

- [Autocomplete] Revert: Fix options list rendering in freeSolo mode (#44857) @ZeeshanTamboli

All contributors of this release in alphabetical order: @ZeeshanTamboli

## v5.16.13

<!-- generated comparing v5.16.12..v5.x -->

_Dec 24, 2024_

A big thanks to the 2 contributors who made this release possible.

### `@mui/material-nextjs@5.16.13`

- Support Next 15.0.0 in v5 (#44853) @DiegoAndai

### Docs

- [material-ui] Fix crashing of DraggableDialog demo (#44811) @sai6855

### Core

- Use React 18's JSX runtime for v5.x UMD builds (#44815) @DiegoAndai

All contributors of this release in alphabetical order: @DiegoAndai, @sai6855

## v5.16.12

<!-- generated comparing v5.16.11..v5.x -->

_Dec 16, 2024_

Material UI v5 is now compatible with React 19 (#44720) @DiegoAndai

### Core

- Bump react 19 in v5 (#44720) @DiegoAndai
- Add `latest-v5` tag to v5 releases (#44757) @DiegoAndai

All contributors of this release in alphabetical order: @DiegoAndai

## 5.16.11

<!-- generated comparing v5.16.9..v5.x -->

_Dec 11, 2024_

A big thanks to the contributor who made this release possible.

### Core

- Bump pnpm to 9.14.4 in v5 (#44705) @DiegoAndai
- Fix UMD examples (#44706) @DiegoAndai
- [typescript] Rescue missing backports v5 (#44712) @DiegoAndai

All contributors of this release in alphabetical order: @DiegoAndai

## 5.16.9

<!-- generated comparing v5.16.8..v5.x -->

_Dec 3, 2024_

A big thanks to the 2 contributors who made this release possible.

### `@mui/material@5.16.9`

- [Tabs] Cherry pick `ScrollbarSize` ref being overridden fix (#44595) @DiegoAndai

### Core

- Ignore browserslist and remove tag latest (#44589) @siriwatknp

All contributors of this release in alphabetical order: @DiegoAndai, @siriwatknp

## 5.16.8

<!-- generated comparing v5.16.7..v5.x -->

_Nov 26, 2024_

A big thanks to the 8 contributors who made this release possible.

### `@mui/material@5.16.8`

- Cherry pick ref accessing PRs (#44543) @DiegoAndai

### `@mui/utils@5.16.8`

- Skip deep clone React element (v5.x) (#44494) @jukkatupamaki

### Docs

- Keep sponsors up to date @oliviertassinari
- Fix MUI Treasury Layout broken links (#43753) @oliviertassinari
- Strengthen CSP rule @oliviertassinari
- Give up on restoring search @oliviertassinari
- Normalize next major message @oliviertassinari
- Fix versions URL @oliviertassinari
- Fix use of absolute URLs (#43567) @oliviertassinari
- Restore search on v5 (#43566) @oliviertassinari
- Fix link from v5 to v6 (#43585) @alexfauquette
- Updated mui-x roadmap links with the new project URL (@michelengelen) (#43446) @michelengelen
- Fix broken link to Next.js docs @oliviertassinari

### Core

- [blog] Polish Upcoming changes to MUI X pricing in 2024 (#43438) @oliviertassinari
- [blog] Add video to the Pigment CSS blog post (#42500) @oliviertassinari
- [blog] Announcing pricing changes Sep 2024 (#43272) @cherniavskii
- Prepare for moving to v5.x branch (#43447) @siriwatknp
- [core] Cherry pick #42346 to v5 (#44475) @DiegoAndai
- [core] Fix CI on v5.x branch (#44487) @DiegoAndai
- [core] Fix CI on v5.x branch (#43564) @oliviertassinari
- [examples] Fix v5 clone example instructions (#43755) @oliviertassinari
- [examples] Fix CLI download instructions @oliviertassinari
- [examples] Fix CDN live preview example @oliviertassinari
- [examples] Fix more examples to work with v5 @oliviertassinari
- [examples] Freeze examples dependency range (#43435) @oliviertassinari

All contributors of this release in alphabetical order: @alexfauquette, @cherniavskii, @DiegoAndai, @jukkatupamaki, @michelengelen, @oliviertassinari, @rluzists1, @siriwatknp

## 5.16.7

<!-- generated comparing v5.16.6..master -->

_Aug 9, 2024_

A big thanks to the 3 contributors who made this release possible.

### `@mui/material@5.16.7`

- &#8203;<!-- 3 -->[material-ui][mui-system] Add support for version runtime checks (#43233) @DiegoAndai

### Docs

- &#8203;<!-- 4 -->[docs] Fix 301 @oliviertassinari

### Core

- &#8203;<!-- 2 -->[website] Fix wrong link in pricing table (@zanivan) (#43143) @zanivan
- &#8203;<!-- 1 -->[website] Add blog link to pricing table (@zanivan) (#43140) @zanivan

All contributors of this release in alphabetical order: @DiegoAndai, @oliviertassinari, @zanivan

## 5.16.6

<!-- generated comparing v5.16.5..master -->

_Jul 30, 2024_

A big thanks to the 5 contributors who made this release possible.

### `@mui/material@5.16.6`

- [Divider] Enable borderStyle enhancement in divider with children (#43059) @anuujj

### Docs

- [material-ui][Card] Update CardMedia description (#43121) @shahzaibdev1
- [material-ui] Replace deprecated `<ListItem button/>` with `ListItemButton` component in routing libraries list example (#43114) @aliharis99
- [material-ui][Snackbar] Improve close `reason` type in demos (#43105) @sai6855

### Core

- [code-infra] Use the same CI names on master & next (#43064) @mnajdova

All contributors of this release in alphabetical order: @aliharis99, @anuujj, @mnajdova, @sai6855, @shahzaibdev1

## 5.16.5

<!-- generated comparing v5.16.4..master -->

_Jul 25, 2024_

A big thanks to the 4 contributors who made this release possible.

### `@mui/utils@5.16.5`

- &#8203;<!-- 1 -->[utils] Add dependency to @mui/types (@mnajdova) (#43047) @mnajdova

### Docs

- &#8203;<!-- 4 -->[material-ui][joy-ui][Autocomplete] Fix `Hint` demo (@ManthanGajjar) (#43039) @ManthanGajjar
- &#8203;<!-- 3 -->Fix CHANGELOG convention @oliviertassinari
- &#8203;<!-- 2 -->[material-ui] Fix broken image links in blog template on master branch (#42969) @navedqb

All contributors of this release in alphabetical order: @ManthanGajjar, @mnajdova, @navedqb, @oliviertassinari

## 5.16.4

<!-- generated comparing v5.16.3..master -->

_Jul 16, 2024_

A big thanks to the 1 contributor who made this release possible.

### `@mui/material@5.16.4`

- &#8203;<!-- 1 -->Fix wrong import in Popover types (#42967) @mnajdova

All contributors of this release in alphabetical order: @mnajdova

## 5.16.3

<!-- generated comparing v5.16.2..master -->

_Jul 16, 2024_

A big thanks to the 3 contributors who made this release possible.

### `@mui/material@5.16.3`

- &#8203;<!-- 1 -->[material] Add missing dependency (#42959) @mnajdova

### Docs

- &#8203;<!-- 3 -->[material-ui][Autocomplete] Add instructions about `autosuggest-highlight` dependency (#42953) @HoFa1997
- &#8203;<!-- 2 -->Move feedback from Canny to GitHub @oliviertassinari

All contributors of this release in alphabetical order: @HoFa1997, @mnajdova, @oliviertassinari

## 5.16.2

<!-- generated comparing v5.16.1..master -->

_Jul 16, 2024_

A big thanks to the 2 contributors who made this release possible.

### `@mui/material@5.16.2`

- &#8203;<!-- 2 -->[material] Remove dependency to @mui/base (@mnajdova) (#42917) @mnajdova

### Core

- &#8203;<!-- 1 -->[website] Sync /about page @oliviertassinari

All contributors of this release in alphabetical order: @mnajdova, @oliviertassinari

## 5.16.1

_Jul 11, 2024_

A big thanks to the 4 contributors who made this release possible. Here are some highlights ✨:

- ⚛️ All packages, including Material UI, are now compatible with React 18.3.1

### `@mui/material@5.16.1`

- [AppBar] Fix inherit color is inconsistent between ThemeProvider and CssVarsProvider (#42713) @ZeeshanTamboli

### `@mui/joy@5.0.0-beta.48`

- [Autocomplete] Fix React spread key warning (#42856) @aarongarciah

### Docs

- [material-ui] Fix React 18.3 key spread warnings in Autocomplete demos (#42854) @aarongarciah
- [material-ui] Fix type error in virtualized table demo (#42852) @aarongarciah
- Fix typos (@omahs) (#42888) @omahs
- Fix 301 @oliviertassinari

### Core

- [core] Bump React to 18.3.1 (#42846) @aarongarciah
- [core] Remove react-test-renderer (#42853) @aarongarciah
- [core] Replace enzyme in describeConformance (#42847) @aarongarciah
- [test] Remove enzyme (#42850) @aarongarciah
- [test] Remove createMount test util (#42849) @aarongarciah
- [base-ui] Fix React spread key warning in test (#42855) @aarongarciah
- [styles][withStyles] Expect React defaultProps warning in test (#42752) (#42851) @aarongarciah

All contributors of this release in alphabetical order: @aarongarciah, @oliviertassinari, @omahs, @ZeeshanTamboli

## 5.16.0

<!-- generated comparing v5.15.21..master -->

_Jul 5, 2024_

A big thanks to the 5 contributors who made this release possible. Here are some highlights ✨:

- 🚀 Added `InitColorSchemeScript` for Next.js App Router (#42829) @siriwatknp

### `@mui/material@5.16.0`

- [Alert] Add ability to override slot props (@alexey-kozlenkov) (#42808) @alexey-kozlenkov
- Add `InitColorSchemeScript` for Next.js App Router (#42829) @siriwatknp
- Add `DefaultPropsProvider` (#42820) @siriwatknp
- Support `CssVarsTheme` in `responsiveFontSizes` return type (@jxdp) (#42806) @jxdp
- Remove warning from `getInitColorSchemeScript` (#42838) @siriwatknp

### Docs

- [docs] Fix 301 MDN redirections @oliviertassinari

### Core

- [mui-utils][test] Remove usages of deprecated react-dom APIs (@aarongarciah) (#42813) @aarongarciah

All contributors of this release in alphabetical order: @aarongarciah, @alexey-kozlenkov, @jxdp, @oliviertassinari, @siriwatknp

## 5.15.21

<!-- generated comparing v5.15.20..master -->

_Jun 28, 2024_

A big thanks to the 7 contributors who made this release possible.

### `@mui/material@5.15.21`

- [Autocomplete] Fix renderOption props type (@DiegoAndai) (#42709) @DiegoAndai
- [Stepper] Generate class for `nonLinear` prop (@alexismo) (#42677) @alexismo

### Docs

- Use new email for sponsoring @oliviertassinari
- Fix 301 links (@alexfauquette) (#42700) @alexfauquette
- [material-ui][Select] Fix the `SelectAutoWidth` demo menu item value (@Danielkhakbaz) (#42696) @Danielkhakbaz
- [material-ui][Autocomplete] Fix more React 18.3 key spread warnings in demos (#42766) @wbt
- [material-ui] Fix sign in side image (#42708) @zanivan
- [website] Add Ale to team (#42769) @alelthomas

### Core

- [core] Cherry pick pnpm updates (#42763) @DiegoAndai
- [website] Add Armin to the team members (@arminmeh) (#42681) @arminmeh
- [website] Open Staff Engineer role for Pigment CSS (@mnajdova) (#42669) @mnajdova

All contributors of this release in alphabetical order: @alexfauquette, @alexismo, @arminmeh, @Danielkhakbaz, @DiegoAndai, @mnajdova, @oliviertassinari

## 5.15.20

<!-- generated comparing v5.15.19..master -->

_Jun 12, 2024_

A big thanks to the 9 contributors who made this release possible.

### `@mui/material@5.15.20`

- &#8203;<!-- 17 -->[Autocomplete] Shouldn't resize when hovering (@ZeeshanTamboli) (#42535) @ZeeshanTamboli
- &#8203;<!-- 07 -->[Tab] Fix applying `iconWrapper` styles from theme and update its description (@sai6855) (#42570) @sai6855

### `@mui/utils@5.15.15`

- &#8203;<!-- 06 -->Allow passing `NaN` as `defaultValue` to `useControlled` (@iammminzzy) (#42571) @iammminzzy
- &#8203;<!-- 17 -->Fix GitHub-reported prototype pollution vulnerability in `deepmerge` (#41652) (#42608) @DiegoAndai

### Docs

- &#8203;<!-- 16 -->[docs] Add Pigment CSS and Base UI logos SVGs (#42513) @danilo-leal
- &#8203;<!-- 15 -->[docs] Update twitter.com to x.com @oliviertassinari
- &#8203;<!-- 14 -->[docs] Simplify Example projects page @oliviertassinari
- &#8203;<!-- 13 -->[material-ui] Add docs for complementary stepper components (@anle9650) (#42613) @anle9650
- &#8203;<!-- 12 -->[docs] Add changelog section to the design kits page (@danilo-leal) (#42463) @danilo-leal
- &#8203;<!-- 11 -->[material-ui] Fix sentence in the All components page (@danilo-leal) (#42462) @danilo-leal
- &#8203;<!-- 10 -->[material-ui] Update Figma design kit doc redirect link (@danilo-leal) (#42456) @danilo-leal
- &#8203;<!-- 09 -->[system] Add "dynamic values" section to sx prop page (@aarongarciah) (#42453) @aarongarciah

### Core

- &#8203;<!-- 18 -->[website] Move the `React Engineer - X` role to future roles (#42532) @DanailH
- &#8203;<!-- 08 -->[examples] Remove Pigment CSS examples (#42538) @sai6855
- &#8203;<!-- 05 -->[website] Close Developer Advocate / Content Engineer role @oliviertassinari
- &#8203;<!-- 04 -->[website] Update DoiT description and link in Sponsors section (@erezstmn-doit) (#42511) @erezstmn-doit
- &#8203;<!-- 03 -->[website] Clean up the docs-infra job ad (@danilo-leal) (#42509) @danilo-leal
- &#8203;<!-- 02 -->[website] Open the Docs-infra engineer role (@danilo-leal) (#42496) @danilo-leal
- &#8203;<!-- 01 -->[website] Fix locationCountry in about page @oliviertassinari

All contributors of this release in alphabetical order: @aarongarciah, @anle9650, @DanailH, @danilo-leal, @erezstmn-doit, @iammminzzy, @oliviertassinari, @sai6855, @ZeeshanTamboli

## 5.15.19

<!-- generated comparing v5.15.18..master -->

_May 29, 2024_

A big thanks to the 12 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.15.19`

- &#8203;<!-- 19 -->[AlertTitle] Enable extending Typography props (@lucasgmelo) (#42334) @github-actions[bot]
- &#8203;<!-- 06 -->[responsiveFontSizes] Handled undefined variants (@brijeshb42) (#42419) @github-actions[bot]
- &#8203;<!-- 05 -->[Slider] Fix wrong CSS value (@mnajdova) (#42373) @github-actions[bot]

### Docs

- &#8203;<!-- 13 -->Link to pnpm installation docs (#42420) @aarongarciah
- &#8203;<!-- 12 -->Remove LocalMonero (@oliviertassinari) (#42315) @github-actions[bot]
- &#8203;<!-- 10 -->[material-ui] Fix typo in style interoperability with Tailwind CSS docs (@ZeeshanTamboli) (#42312) @github-actions[bot]
- &#8203;<!-- 09 -->[material-ui][Pagination] Clarify pagination `page` prop API (@Mandar-Pandya) (#42265) @github-actions[bot]
- &#8203;<!-- 08 -->[material-ui][Tabs] Improve the Basic Tabs demo (@MatheusEli) (#42426) @github-actions[bot]
- &#8203;<!-- 07 -->[pigment-css] Fix duplication of content (#42410) @oliviertassinari

### Core

- &#8203;<!-- 18 -->[blog] Add the "Product" tag to the Pigment CSS post (@danilo-leal) (#42366) @github-actions[bot]
- &#8203;<!-- 17 -->[blog] Update blog post OG image (@danilo-leal) (#42306) @github-actions[bot]
- &#8203;<!-- 16 -->[blog] Update Pigment CSS post (@danilo-leal) (#42267) @github-actions[bot]
- &#8203;<!-- 15 -->[core] Fix React 18.3 warnings about spreading keys in the Material UI `Autocomplete` component (#42099) (#42241) @DiegoAndai
- &#8203;<!-- 14 -->[core] Fix a few more key spread issues (@oliviertassinari) (#42318) @github-actions[bot]
- &#8203;<!-- 11 -->[docs-infra] Allow JSDoc tags (#42327) @aarongarciah
- &#8203;<!-- 04 -->[website] Add Nikita to the about page (@nikitaa24) (#42421) @github-actions[bot]
- &#8203;<!-- 03 -->[website] Fix hero spacing changes applying at the wrong breakpoint (@KenanYusuf) (#42357) @github-actions[bot]
- &#8203;<!-- 02 -->[website] Adds Kenan Yusuf to about page (@KenanYusuf) (#42330) @github-actions[bot]
- &#8203;<!-- 01 -->[website] Improve about page @oliviertassinari

All contributors of this release in alphabetical order: @aarongarciah, @brijeshb42, @danilo-leal, @DiegoAndai, @KenanYusuf, @lucasgmelo, @Mandar-Pandya, @MatheusEli, @mnajdova, @nikitaa24, @oliviertassinari, @ZeeshanTamboli

## 5.15.18

<!-- generated comparing v5.15.17..master -->

_May 14, 2024_

A big thanks to the 5 contributors who made this release possible. Here are some highlights ✨:

### `@mui/material@5.15.18`

- &#8203;<!-- 6 -->[Autocomplete] Improve design when there's a start adornment for small autocomplete (@TahaRhidouani) (#42176) @github-actions[bot]
- &#8203;<!-- 3 -->[ToggleButtonGroup] Add missing `selected` class in ToggleButtonGroupClasses type (@tarunrajput) (#42250) @github-actions[bot]

### Docs

- &#8203;<!-- 4 -->[docs] Fix 301 to Figma @oliviertassinari

### Core

- &#8203;<!-- 5 -->[blog] Introducing Pigment CSS blog post (#42198) (#42255) @samuelsycamore
- &#8203;<!-- 2 -->[website] Add redirection for talk @oliviertassinari
- &#8203;<!-- 1 -->[website] Adds Arthur Balduini team info (@arthurbalduini) (#42226) @github-actions[bot]

All contributors of this release in alphabetical order: @arthurbalduini, @oliviertassinari, @samuelsycamore, @TahaRhidouani, @tarunrajput

## 5.15.17

<!-- generated comparing v5.15.16..master -->

_May 8, 2024_

A big thanks to the 4 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.15.17`

- [Slider] Move palette styles to the bottom (#41676) @siriwatknp

### Docs

- Fix SEO redirection issues @oliviertassinari
- [material-ui] Fix broken link (@aarongarciah) (#42143) @github-actions[bot]
- [material-ui] Fix link on the Sync page (@danilo-leal) (#42089) @github-actions[bot]

### Core

- [blog] Shorten title to fit @oliviertassinari
- [blog] Update Sync post OG image (@danilo-leal) (#42117) @github-actions[bot]
- [blog] A few tweaks in introducing-sync-plugin (@oliviertassinari) (#42094) @github-actions[bot]
- [docs-infra] Fix code block layout shift (#41917) @oliviertassinari
- [website] Fix home page slider's track position (@aarongarciah) (#42144) @github-actions[bot]
- [website] Closing the survey @oliviertassinari
- [website] Remove Survey banner from website and Core docs (#42104) @joserodolfofreitas

All contributors of this release in alphabetical order: @github-actions[bot], @joserodolfofreitas, @oliviertassinari, @siriwatknp

## 5.15.16

<!-- generated comparing v5.15.15..master -->

_May 1, 2024_

A big thanks to the 8 contributors who made this release possible. Here are some highlights ✨:
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.15.16`

- [material-ui][Dialog] Prevent onClick on the root element from being overwritten (@ryanburr) (#41914) @github-actions[bot]
- [material-ui][Select] Fix `muiName` property TypeScript error (@EyaOuenniche) (#41786) @github-actions[bot]
- —>[material-ui][l10n] Fix typo in is-IS locale (@magnimarels) (#41815) @github-actions[bot]

### Docs

- Fix small SEO issues @oliviertassinari
- Fix 301 Toolpad links @oliviertassinari
- Fix 301 Toolpad links @oliviertassinari
- Fix 301 image redirections @oliviertassinari
- Fix small SEO issues @oliviertassinari
- Fix 301 redirection @oliviertassinari
- Fix format git diff regression (#41882) @oliviertassinari
- Fix 301 links @oliviertassinari
- [material-ui] Fix import statement in migration guide (@sai6855) (#41864) @github-actions[bot]
- [material-ui] Update Figma plugin name (@danilo-leal) (#42057) @github-actions[bot]
- [material-ui] Fix minor spelling error in the "About the lab" page (@ryanhartwig) (#42075) @github-actions[bot]
- [material-ui] Add missing backticks to HTML tag in the installation page (@Miguelrom) (#42009) @github-actions[bot]
- [material-ui] Add Connect-related content (@danilo-leal) (#41924) @DiegoAndai
- [material-ui] Fix Material 3 message typo (@aarongarciah) (#41822) @github-actions[bot]
- [material-ui] Remove Data Grid v7 beta callout (@cherniavskii) (#41842) @github-actions[bot]
- [material-ui][templates] Fix input props attributes in Landing Page template (@5-tom) (#42034) @github-actions[bot]
- [system] Update typo on the sx prop page (@bricker) (#42078) @github-actions[bot]

### Core

- [blog] Add post to introduce the Connect plugin (@danilo-leal) (#41929) @DiegoAndai
- [core] Automate cherry-pick of PRs from `next` -> `master` (#41742) @aarongarciah
- [docs-infra] Improve Twitter OG:image (#41860) @oliviertassinari
- [docs-infra] Use edge function for card generation (#41188) @alexfauquette
- [docs-infra] Fix drawer performances (#41807) (#41820) @alexfauquette
- [docs-infra] Fix analytics about inline ads (#41474) @alexfauquette
- [website] Sync career roles (@oliviertassinari) (#42059) @github-actions[bot]
- [website] Add content about the Sync plugin in the Material UI page (@danilo-leal) (#42074) @github-actions[bot]
- [website] Add Nadja on the about page (#42054) @mnajdova
- [website] Close the `Design Engineer - X` role (#42014) @DanailH
- [website] Remove customer support agent role from website (@rluzists1) (#41996) @github-actions[bot]
- [website] Add Jose to About Us (#41759) @JCQuintas

All contributors of this release in alphabetical order: @aarongarciah, @alexfauquette, @DanailH, @DiegoAndai, @github-actions[bot], @JCQuintas, @mnajdova, @oliviertassinari

## 5.15.15

<!-- generated comparing v5.15.14..master -->

_Apr 4, 2024_

A big thanks to the 7 contributors who made this release possible. Here are some highlights ✨:
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.15.15`

- [Autocomplete] Display options provided to the `options` prop even if loading is true (#41677) @ZeeshanTamboli
- [RadioGroup] Apply classnames (#41681) @ZeeshanTamboli

### `@mui/system@5.15.15`

- Fix typo to avoid infinite recursion in function call (#41678) @ZeeshanTamboli

### Docs

- [material-ui][Slider] Remove `valueLabelFormat` from restricted values demo so that the tooltip thumb label displays the same as the value text (#41679) @ZeeshanTamboli
- [material-ui] Remove deleted page from the sidenav (#41594) @danilo-leal
- [material-ui] Fix typo in CSS theme variables customization (#41680) @ZeeshanTamboli
- Continue migration of Base UI to sperate repository @oliviertassinari
- Add notification for MUI X v7 blog post (#41587) (#41605) @cherniavskii
- Update the versions dropdown to show v6 (#41557) @mnajdova

### Core

- [blog] Link to Romain's blog post in MUI X v7 announcement post (#41641) @cherniavskii
- [blog] Blog post with MUI X v7.0.0 annoucement (#41563) (#41604) @cherniavskii
- [blog] Add post about remote (#41565) @danilo-leal
- [core] Continue rename of Toolpad @oliviertassinari
- [docs-infra] Add Toolpad product/category IDs to types (#41551) @bharatkashyap
- [website] Add Aarón to About Us (#41747) @aarongarciah
- [website] Add stray design adjustments throughout the site (#41642) @mnajdova
- [website] Update pricing table (#41606) @cherniavskii

All contributors of this release in alphabetical order: @aarongarciah, @bharatkashyap, @cherniavskii, @danilo-leal, @mnajdova, @oliviertassinari, @ZeeshanTamboli

## 5.15.14

<!-- generated comparing v5.15.13..master -->

_Mar 18, 2024_

A big thanks to the 15 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.15.13`

- [Accordion] Convert to support CSS extraction (#41221) @mnajdova
- &#8203;<!-- 24 -->[Autocomplete] Convert to support CSS extraction (#40330) @mnajdova
- &#8203;<!-- 06 -->[Slider] Convert to support CSS extraction (#41201) @mnajdova
- &#8203;<!-- 07 -->[Select] Fix variant type (#41405) @sai6855
- &#8203;<!-- 09 -->[typescript] Use interface instead of type for props (#41500) @siriwatknp

### `@pigment-css/react@0.0.3`

- &#8203;<!-- 03 -->Add Box component (#41451) @brijeshb42

### `pigment-css/nextjs-plugin@0.0.3`

- &#8203;<!-- 04 -->Fix alias resolver (#41494) @brijeshb42
- &#8203;<!-- 05 -->Follow-up to #41494 (#41502) @brijeshb42

### Docs

- &#8203;<!-- 12 -->[joy-ui] Add UI improvements to the side navigation demo (#41461) @cipherlogs
- &#8203;<!-- 11 -->[pigment-css] Add media query guide (#41473) @siriwatknp
- &#8203;<!-- 10 -->[pigment-css] Fixing location of the ExtendTheme type in the docs (#41499) @jherr
- &#8203;<!-- 08 -->[material-ui][Progress] Add Circular progress gradient demo from Github comment (#40559) @DiegoAndai

### Core

- &#8203;<!-- 23 -->[blog] Bringing consistency to Material UI customization APIs (#41040) @DiegoAndai
- &#8203;<!-- 22 -->[code-infra] Rename @mui-internal/docs-utils to @mui/internal-docs-utils (#41498) @michaldudak
- &#8203;<!-- 21 -->[code-infra] Copy translations.json to @mui/docs build folder (#41472) @Janpot
- &#8203;<!-- 20 -->[core] Use Circle CI context (#41532) @oliviertassinari
- &#8203;<!-- 19 -->[core] Fix CHANGELOG format and update date (#41481) @DiegoAndai
- &#8203;<!-- 18 -->[docs] Fix useStorageState regressions (#41223) @Janpot
- &#8203;<!-- 17 -->[docs] Fix some Vale errors (#41516) @oliviertassinari
- &#8203;<!-- 15 -->[material-ui][docs] Fix landing page template's h1 size (#41543) @zanivan
- &#8203;<!-- 14 -->[material-ui][docs] Apply new code header docs feature (#41508) @danilo-leal
- &#8203;<!-- 11 -->[material-next] Drop the package (#41544) @mnajdova
- &#8203;<!-- 16 -->[docs-infra] Fail CI on Vale error (#40944) @oliviertassinari
- &#8203;<!-- 15 -->[docs-infra] Improve Vale config @oliviertassinari
- &#8203;<!-- 14 -->[docs-infra] Add a feature list "component" (#41484) @danilo-leal
- &#8203;<!-- 13 -->[docs-infra] Add code block header classes (#41487) @danilo-leal
- &#8203;<!-- 21 -->[docs-infra] Make the Algolia search input label invisible (#41542) @danilo-leal
- &#8203;<!-- 03 -->[website] Improve navbar's items hover state (#41535) @EyaOuenniche
- &#8203;<!-- 02 -->[website] Split Toolpad documentation (#41316) @bharatkashyap
- &#8203;<!-- 01 -->[website] Use MUI X Data Grid v7-beta (#41276) @cherniavskii

All contributors of this release in alphabetical order: @bharatkashyap, @brijeshb42, @cherniavskii, @cipherlogs, @danilo-leal, @DiegoAndai, @EyaOuenniche, @Janpot, @jherr, @michaldudak, @mnajdova, @oliviertassinari, @sai6855, @siriwatknp, @zanivan

## 5.15.13

<!-- generated comparing v5.15.12..master -->

_Mar 13, 2024_

A big thanks to the 18 contributors who made this release possible. Here are some highights ✨

- The Material UI free Checkout template got a design uplift (#41447) @zanivan

### `@mui/material@5.15.13`

- [Alert] Add `slots` and `slotProps` type to theme (#41324) @sai6855
- [Autocomplete] Fix the options list being added to the DOM in `freeSolo` mode even when there are no options, causing style problems (#41300) @rakeshmusturi
- Add `paperChannel` token (#41447) @siriwatknp
- [Switch] Convert to support CSS extraction (#41367) @alexfauquette
- [Tooltip] Support event handlers with extra parameters (#41320) @LukasTy

### `@mui/system@5.15.13`

- [RtlProvider] Add component & hook (#41241) @mnajdova

### `@mui/utils@5.15.13`

- [utils] Fix visually hidden styles' margin unit (#41477) @michaldudak

### `@mui/codemod@5.15.13`

- Fix merging of slotProps and componentProps (#41323) @sai6855

### `@mui/base@5.0.0-beta.39`

- [material-ui][joy-ui][Autocomplete] Keep in sync highlighted index when the option still exists (#41306) @CGNonofr
- [FormControl] Export `FormControlOwnerState` type from index (#41287) @michaeldfoley
- [material-ui][TextareaAutosize] Fix inline style not getting applied (#41369) @ZeeshanTamboli

### `@pigment-css/react@0.0.2`

- Handle more scenarios while transforming sx prop (#41372) @brijeshb42
- Improve testing of fixtures (#41389) @brijeshb42
- Fix `keyframes` serialize styles error (#41395) @siriwatknp
- Use class selector instead of class value (#41442) @brijeshb42
- [next] Warn about unsupported turbo mode in Next.js (#41445) @brijeshb42

### Docs

- [material-ui] Refine checkout template (#40967) @zanivan
- [material-ui] Add docs for complementary List components (#41329) @anle9650
- [material-ui] Add docs for complementary Dialog components (#41313) @jwithington
- [material-ui] Fix Templates live preview link (#41467) @danilo-leal
- [material-ui] Polish out the templates page (#41468) @zanivan
- [material-ui] Adjust the Templates card design (#41450) @danilo-leal
- [joy-ui] Remove unnecessary styles in color inversion footer demo (#41419) @cipherlogs
- [joy-ui] Update case studies chip background color (#41413) @cipherlogs
- [joy-ui] Remove wrong CSS prop from the Sign-in-side template (#41383) @cipherlogs
- [joy-ui] Fix broken link on the Color Inversion page (#41407) @cipherlogs
- [pigment] Add example and guide section (#41249) @siriwatknp
- [pigment-css] Brand name nonbreaking space (#41438) @oliviertassinari
- [pigment-css] Fix import on the README (#41411) @danilo-leal
- [pigment-css] Edit starter template links on README (#41409) @danilo-leal
- [pigment-css] Tweak the examples and edit READMEs (#41408) @danilo-leal
- [pigment-css] Adjust the bit about CSS vars on the README (#41463) @danilo-leal
- Finish brand name fixes #41438 @oliviertassinari
- Remove noreferrer @oliviertassinari
- Fix v4 docs `<b>` appearing in notifications (#41390) @peterwangsc
- Update GitHub project links (#41370) @danilo-leal

### Core

- [pigment] Make all Pigment CSS packages public (#41404) @brijeshb42
- [pigment] Rename directories to match package names (#41453) @brijeshb42
- [pigment-css] Example fix leading spaces (#41439) @oliviertassinari
- [code-infra] Add short note about e2e-website workflow schedule (#41355) @Janpot
- [code-infra] Add alias for icon types (#41248) @Janpot
- [code-infra] Reduce concurrency of typescript:ci further (#41392) @Janpot
- [code-infra] Reduce concurrency for test_types ci job (#41385) @Janpot
- [code-infra] Adapt API code generator to Base UI repo needs (#41475) @michaldudak
- [docs-infra] Don't generate preview files for the templates (#41379) @mnajdova
- [docs-infra] Fix Pigment CSS apps path in the render mui demos script (#41476) @mnajdova
- [docs-infra] move feedback to ESM (#41381) @alexfauquette
- [docs-infra] Improve color contrast throughout (#41387) @danilo-leal
- [docs-infra] Simplify Algolia crawler config (#41312) @oliviertassinari
- [docs-infra] Adjust the tabs and layout selection design (#41084) @danilo-leal
- [blog] Update the Base UI post with links to dedicated repo (#41358) @danilo-leal
- [website] Update the Careers page role (#41384) @danilo-leal
- [website] Compress about images @oliviertassinari
- [website] Improve color contrast on the homepage (#41465) @danilo-leal
- [examples] Add pigment-css-vite-ts starter example (#41196) @siriwatknp
- [examples] Add pigment-css-nextjs-ts starter project (#41105) @siriwatknp

All contributors of this release in alphabetical order: @alexfauquette, @anle9650, @brijeshb42, @CGNonofr, @cipherlogs, @danilo-leal, @jwithington, @Janpot, @michaeldfoley, @michaldudak, @mnajdova, @oliviertassinari, @peterwangsc, @rakeshmusturi, @sai6855, @siriwatknp, @zanivan, @ZeeshanTamboli

## 5.15.12

<!-- generated comparing v5.15.11..master -->

_Mar 5, 2024_

A big thanks to the 21 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@pigment-css/react@0.0.1`, `@pigment-css/nextjs-plugin@0.0.1`, `@pigment-css/vite-plugin@0.0.1`, & `@pigment-css/unplugin@0.0.1`

- This is the first public release of our new zero-runtime CSS-in-JS library, Pigment CSS.

### `@mui/material@5.15.12`

- &#8203;<!-- 52 -->Support props callback type in theme variants (#40946) @ZeeshanTamboli
- &#8203;<!-- 50 -->[Alert] Convert to support zero runtime (#41230) @siriwatknp
- &#8203;<!-- 49 -->[Alert] Deprecate composed classes (#40688) @DiegoAndai
- &#8203;<!-- 44 -->[Button] Deprecate classes for v6 (#40675) @sai6855
- &#8203;<!-- 43 -->[Checkbox] `large` size added in type (#34909) @smox
- &#8203;<!-- 42 -->[Chip] Deprecate composed classes (#41235) @sai6855
- &#8203;<!-- 41 -->[Chip] Correct `deleteIconColorPrimary` and `deleteIconColorSecondary` class descriptions (#41231) @sai6855
- &#8203;<!-- 17 -->Remove unused dev dependency on @mui/lab (#41198) @brijeshb42
- &#8203;<!-- 15 -->[Slider] Deprecate components and componentProps props for v6 (#40777) @lhilgert9

### `@mui/system@5.15.12`

- &#8203;<!-- 16 -->[pigment-css][material-ui] Render badge demos (#41353) @siriwatknp
- &#8203;<!-- 14 -->[pigment-css] Update to latest wyw version (#41363) @brijeshb42
- &#8203;<!-- 13 -->[pigment-css] Rename scope to @pigment-css (#41354) @brijeshb42
- &#8203;<!-- 12 -->[pigment-css] Rename zero-runtime to pigmentcss (#41317) @brijeshb42
- &#8203;<!-- 11 -->Fix createSpacing return type (#41318) @matystroia
- &#8203;<!-- 06 -->[zero] Add support for styled tagged-template literals (#41268) @brijeshb42
- &#8203;<!-- 05 -->[zero] Set up Material UI migration demos (#41267) @siriwatknp
- &#8203;<!-- 04 -->[zero] Move extendTheme to already existing @mui/zero-runtime/utils (#41254) @brijeshb42
- &#8203;<!-- 03 -->[zero] Remove `object` intersection from CSS Fallback (#41271) @siriwatknp
- &#8203;<!-- 02 -->[zero] Minor wording changes in README (#41253) @brijeshb42
- &#8203;<!-- 01 -->[zero] Prepare zero-runtime packages for public release (#41226) @brijeshb42

### `@mui/joy@5.0.0-beta.30`

- &#8203;<!-- 48 -->[joy-ui][Autocomplete] Fix text overflow in Chip (#40229) @PunitSoniME

### Docs

- &#8203;<!-- 47 -->[base-ui] Update the docs post repo separation (#41328) @danilo-leal
- &#8203;<!-- 34 -->Fix missing partner link @oliviertassinari
- &#8203;<!-- 33 -->Update links to GitHub projects (#41297) @danilo-leal
- &#8203;<!-- 32 -->Standardize WAI-ARIA referencest @oliviertassinari
- &#8203;<!-- 31 -->Fix image layout shift when loading @oliviertassinari
- &#8203;<!-- 23 -->[joy-ui] Add stray adjustments throughout the docs (#41211) @danilo-leal
- &#8203;<!-- 22 -->[material-ui] Remove duplicated text at FAQ page (#41326) @zanivan
- &#8203;<!-- 21 -->[material-ui] Fix color mode toggle of the landing page template (#41293) @zanivan
- &#8203;<!-- 20 -->[system] Tweak the Usage demos (#41242) @danilo-leal
- &#8203;<!-- 19 -->[zero] Add a Why section on the README (#41284) @danilo-leal

### Core

- &#8203;<!-- 51 -->Revert "[utils] Port `useLocalStorageState` hook from Toolpad (#41096)" @oliviertassinari
- &#8203;<!-- 46 -->[blog] Add post about how MUI uses Toolpad (#40172) @prakhargupta1
- &#8203;<!-- 45 -->[blog] No bundled demos in blog posts @oliviertassinari
- &#8203;<!-- 40 -->[code-infra] Embed translations in the @mui/docs package (#41246) @Janpot
- &#8203;<!-- 39 -->[code-infra] Prepare the markdown package for publishing (#41240) @michaldudak
- &#8203;<!-- 38 -->[code-infra] Unpin the version of docs-utils in scripts (#41232) @michaldudak
- &#8203;<!-- 37 -->[core] Use runtime agnostic setTimeout type @oliviertassinari
- &#8203;<!-- 36 -->[core] Remove window. reference for common globals @oliviertassinari
- &#8203;<!-- 35 -->[core] Add a script to build all packages (#40631) @michaldudak
- &#8203;<!-- 30 -->[docs-infra] Fix missing non breaking spaces @oliviertassinari
- &#8203;<!-- 29 -->[docs-infra] Add design customizations to the disclosure element (#41285) @danilo-leal
- &#8203;<!-- 28 -->[docs-infra] Adjust headings dark mode color (#41292) @danilo-leal
- &#8203;<!-- 27 -->[docs-infra] Fix Stack Overflow breaking space @oliviertassinari
- &#8203;<!-- 26 -->[docs-infra] Fix product selector popup not closing on route change (#41166) @divyammadhok
- &#8203;<!-- 25 -->[docs-infra] Improve fix blank links ad @oliviertassinari
- &#8203;<!-- 24 -->[docs-infra] Support interfaces for X docs (#41069) @alexfauquette
- &#8203;<!-- 18 -->[infra] Adjust the links to search for issues (#41008) @michelengelen
- &#8203;<!-- 10 -->[website] Move the `React Engineer - xCharts` to `Next roles` section (#41368) @DanailH
- &#8203;<!-- 09 -->[website] Add James to About Us (#41362) @atomiks
- &#8203;<!-- 08 -->[website] Polish Button outline primary medium (#41298) @oliviertassinari
- &#8203;<!-- 07 -->[website] Remove Heatmap chart from community plan on pricing table (#41081) @alexfauquette

All contributors of this release in alphabetical order: @alexfauquette, @atomiks, @brijeshb42, @DanailH, @danilo-leal, @DiegoAndai, @divyammadhok, @Janpot, @lhilgert9, @matystroia, @michaldudak, @michelengelen, @mj12albert, @oliviertassinari, @prakhargupta1, @PunitSoniME, @sai6855, @siriwatknp, @smox, @zanivan, @ZeeshanTamboli

## 5.15.11

<!-- generated comparing v5.15.10..master -->

_Feb 21, 2024_

A big thanks to the 26 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.15.11`

- [Alert] Deprecate components and componentsProps props (#40681) @DiegoAndai
- [Autocomplete] Caret transformation issue with font size change (#41066) @dpertsin
- [StepLabel] Add type for StepIconComponent (#41082) @harrydigos
- [TablePagination] Fix type error in Select props (#39137) @PaulKristoffersson
- [Transitions] External ownerState is incorrectly forwarded to inner components (#41187) @gitstart
- Use direct import (#40851) @siriwatknp

### `@mui/material-nextjs@5.15.11`

- Fix missing babel/runtime dependency in material-ui-nextjs (#41077) @siriwatknp

### `@mui/system@5.15.11`

- Use direct import (#40851) @siriwatknp
- Move useMediaQuery to system (#39463) @justintoman
- Consolidate the variants props callback arguments (#41222) @mnajdova
- Merge props and ownerState in the variants props callback (#41219) @mnajdova

### `@mui/codemod@5.15.11`

- [AccordionSummary] Add contentGutters deprecation codemods (#41006) @DiegoAndai
- [PaginationItem] Add codemod for deprecated classes (#41145) @sai6855

### `@mui/utils@5.15.11`

- Port `useLocalStorageState` hook from Toolpad (#41096) @Janpot

### `@mui/base@5.0.0-beta.37`

- [Switch] Add missing role attribute (#40907) @KirankumarAmbati
- [TextareaAutosize] Improve implementation (#40789) @ZeeshanTamboli

### `@mui/lab@5.0.0-alpha.164`

- [Masonry] Ability to sort elements from left to right (#39904) @Rishi556

### Docs

- [base-ui] Fix focus state demo in Base UI autocomplete (#41104) @oliviertassinari
- [base-ui] Update the Accessibility page demos design (#40995) @danilo-leal
- [joy-ui] Fix LinearProgressWithLabel example (#41194) @khgiddon
- [joy-ui] Fix 404 image on the docs @oliviertassinari
- [material-ui] Add a "start now" section on the Overview page (#41137) @danilo-leal
- [material-ui] Inform about deprecated TablePagination SelectProps usage (#41186) @gitstart
- [material-ui] Update the Testing page's Argos link (#41170) @gregberge
- [material-ui] Remove Masonry component and Material Design icon from the landing page template (#41080) @zanivan
- [material-ui] Standardize all references to Material Design 3 (M3) (#40903) @samuelsycamore
- [material-ui] Add stray fixes around a few pages (#41038) @danilo-leal
- [material-ui][joy-ui][system] Restore and revise the Box docs (#40622) @samuelsycamore
- [material-ui] Add simpler demo for default behavior (#40980) @zanivan
- [system] Explain why AppRouterCacheProvider (#40909) @oliviertassinari
- Link to react-transition group with https @oliviertassinari
- Update broken URL hashes (#41185) @LukasTy
- Prefer https links @oliviertassinari
- Fix MUI Treasury link @oliviertassinari
- Migrate the last > quotes to ::: callouts @oliviertassinari

### Core

- [blog] Add new product tags and stray adjustments (#41193) @danilo-leal
- [blog] Simplify `/base-ui-2024-plans/` page (#41171) @oliviertassinari
- [blog] Add link to Base UI API changes RFC (#41089) @michaldudak
- [blog] Remove the "News" tag (#41208) @danilo-leal
- [changelog] Remove @mui/system@5.15.10 (#41093) @michaldudak
- [code-infra] Move typescript-to-proptypes to internal-scripts package (#41079) @michaldudak
- [code-infra] Move Link to @mui/docs (#40889) @Janpot
- [code-infra] Use `experimental.cpus` to control amount of export workers in Next.js (#41132) @Janpot
- [code-infra] Load commonjs files in next.config.mjs with require (#41108) @Janpot
- [code-infra] Prepare babel macros package for publishing to npm (#41178) @michaldudak
- [code-infra] Build internal packages before publishing (#41210) @michaldudak
- [core] Improve the release instructions (#40973) @mnajdova
- [core] Simplify Next.js Demo Zero Runtime guide @oliviertassinari
- [core] Fix TypeScript spelling in changelog @oliviertassinari
- [core] Fix small detail in the autocomplete demo @oliviertassinari
- [core] Increase node memory limit on Netlify build (#41111) @michaldudak
- [core][Tooltip] Remove incorrect code comment (#41179) @ZeeshanTamboli
- [core] Fix missing context display names (#41168) @oliviertassinari
- [core][base-ui] Remove `@mui/base` dev dependency from Base UI workspace (#41216) @ZeeshanTamboli
- [zero][demo] Fix sample next app build (#41197) @brijeshb42
- [docs-infra] Simplify copy logic (#41167) @oliviertassinari
- [docs-infra] New way of providing API layout config (#41106) @alexfauquette
- [docs-infra] Reduce scrollbar width on ROC (#41148) @oliviertassinari
- [docs-infra] Add external link arrow (#41129) @siriwatknp
- [docs-infra] Fix external link arrow (#41181) @alexfauquette
- [docs-infra] Flag npm and GitHub as wrong spellings @oliviertassinari
- [docs-infra] Fix display when the default props is undefined (#41114) @oliviertassinari
- [docs-infra] Remove random layout assignment (#40862) @alexfauquette
- [docs-infra] Add spacing and contrast improvements (#41191) @danilo-leal
- [docs-infra] Share vale-config (#41176) @alexfauquette
- [test] Generalize test utils (#41175) @michaldudak
- [typescript-to-proptypes] Support using `Omit` on types with conditional properties (#41033) @flaviendelangle
- [website] Match chart component names @oliviertassinari
- [website] Add Marblism diamond sponsor (#41097) @rluzists1
- [website] Add overall improvements to the Material UI page (#41075) @danilo-leal
- [website] Fix responsive breakpoints @oliviertassinari
- [website] Fix overloading of footer JS files @oliviertassinari
- [website] Improve the footer's chip contrast (#41209) @danilo-leal
- [zero] Update to latest version of wyw-in-js (#41182) @brijeshb42
- [zero] Setup basic testing framework (#40986) @brijeshb42
- [zero] Fix wrong CSS order by moving import to last (#41002) @siriwatknp
- [zero] Export `extendTheme` for creating a zero-runtime theme (#40897) @siriwatknp
- [zero] Move extendTheme to its own subpath (#41204) @brijeshb42

All contributors of this release in alphabetical order: @alexfauquette, @brijeshb42, @danilo-leal, @DiegoAndai, @dpertsin, @flaviendelangle, @gitstart, @gregberge, @harrydigos, @Janpot, @justintoman, @khgiddon, @KirankumarAmbati, @LukasTy, @michaldudak, @mnajdova, @nikosgavalas, @oliviertassinari, @PaulKristoffersson, @Rishi556, @rluzists1, @sai6855, @samuelsycamore, @siriwatknp, @zanivan, @ZeeshanTamboli

## 5.15.10

<!-- generated comparing v5.15.9..master -->

_Feb 12, 2024_

A big thanks to the 8 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.15.10`

- [Avatar] Add props deprecation with a codemod (#40853) @siriwatknp

### `@mui/joy@5.0.0-beta.28`

- [Button] Fix `disabled` prop priority when inside button group (#41000) @Smileek
- [IconButton] Support loading prop (#40949) @Smileek

### Docs

- [Button][material-ui] Fix 'File upload' demo a11y (#40943) @oliviertassinari
- [TableRow][material-ui] Escape markup in `children` prop so docgen tools don't parse it as HTML (#40992) @millerized
- [material-ui] Remove outdated example projects link (it uses Joy UI now) (#40913) @oliviertassinari
- [material-ui] Fix the "Intro to the MUI ecosystem" link placement (#40988) @danilo-leal
- Fix 301 redirection to StackBlitz @oliviertassinari
- Fix h1 on Joy UI templates @oliviertassinari
- Have MUI workspace own the CodeSandbox @oliviertassinari
- Add notification for MUI X v7 beta (#41001) @joserodolfofreitas
- Fix 301 links @oliviertassinari
- Fix Next.js v13.5.1 <title> SEO regression (#40302) @oliviertassinari
- Add a 404 page (#40884) @danilo-leal
- Fix missing GitHub label when opening new issue @oliviertassinari
- [Stack] Update import statement for Stack component (#41032) @sai6855

### Core

- [blog] Add post about upcoming plans for Base UI (#40882) @danilo-leal
- [core] Simplify CodeSandbox reproduction @oliviertassinari
- [core] Missing redirection @oliviertassinari
- [core] Export functions from `copyFiles` script to reuse in MUI X repo (#40970) @cherniavskii
- [core] Avoid variable shorthands @oliviertassinari
- [docs-infra] Fix search icon issue (#40957) @oliviertassinari
- [docs-infra] Ignore classes tagged with `@ignore` (#41009) @cherniavskii
- [docs-infra] Fix selected tab on codeblocks (#41036) @danilo-leal
- [website] Polish Customer Support Agent role @oliviertassinari

All contributors of this release in alphabetical order: @cherniavskii, @danilo-leal, @joserodolfofreitas, @millerized, @oliviertassinari, @sai6855, @siriwatknp, @Smileek

## 5.15.9<!-- generated comparing v5.15.8..master -->

_Feb 8, 2024_

A big thanks to the 7 contributors who made this release possible. Here are some highlights ✨:

- 🐛 A critical fix to remove non-published library usage in `@mui/material` peerDependencies.

### `@mui/material@5.15.9`

- &#8203;<!-- 11 -->[autocomplete] Avoid spread operator (#40968) @oliviertassinari
- &#8203;<!-- 05 -->[material] Remove zero-runtime from peer dep (#41003) @brijeshb42

### `@mui/base@5.0.0-beta.36`

- &#8203;<!-- 10 -->[base-ui] Update props using Array to ReadonlyArray type (#40754) @RaghavenderSingh

### `@mui/system@5.15.9`

- &#8203;<!-- 02 -->[system] use `ReadonlyArray` for CSS related types (#40972) @siriwatknp
- &#8203;<!-- 01 -->[zero] Migrate to use wyw-in-js instead of linaria (#40866) @brijeshb42

### Docs

- &#8203;<!-- 06 -->[docs] Polish codemod git diff format @oliviertassinari
- &#8203;<!-- 05 -->[material-ui][docs] Migrating from deprecated apis follow up (#40981) @DiegoAndai

### Core

- &#8203;<!-- 09 -->[code-infra] Move next config to ESM (#40869) @Janpot
- &#8203;<!-- 08 -->[code-infra] Update prettier (#40772) @Janpot
- &#8203;<!-- 07 -->[code-infra] Add codemod for `light` prop removal (#40947) @sai6855

All contributors of this release in alphabetical order: @brijeshb42, @DiegoAndai, @Janpot, @oliviertassinari, @RaghavenderSingh, @sai6855, @siriwatknp

## 5.15.8<!-- generated comparing v5.15.7..master -->

_Feb 6, 2024_

A big thanks to the 17 contributors who made this release possible. Here are some highlights ✨:

- 💫 Added a redesigned [landing page template](https://mui.com/material-ui/getting-started/templates/landing-page/) (#37557) @zanivan
- ✨ Added support for Arrow Down/Up + Shift and Page Up/Down keys for faster stepping in the Slider component (#40676) @mnajdova
- many 🐛 bug fixes and 📚 documentation improvements

### `@mui/material@5.15.8`

- &#8203;<!-- 36 -->[Avatar] Simplify valid children assertion (#40834) @oliviertassinari

### `@mui/codemod@5.15.8`

- &#8203;<!-- 37 -->[Accordion] Update props actual.js test case (#40879) @DiegoAndai
- &#8203;<!-- 12 -->Fix `findComponentJSX` util (#40855) @sai6855

### `@mui/system@5.15.8`

- &#8203;<!-- 10 -->Add blend color manipulator (#40258) @romgrk
- &#8203;<!-- 38 -->Support variants in `theme.styleOverrides` (#40690) @siriwatknp
- &#8203;<!-- 02 -->[zero] Always replace the `createUseThemeProps` call (#40885) @siriwatknp
- &#8203;<!-- 01 -->[zero] Add README with installation and basic usage (#40761) @brijeshb42

### `@mui/base@5.0.0-beta.34`

- &#8203;<!-- 35 -->[Button] Add support for `hostElementName` prop to improve SSR (#40507) @mj12albert
- &#8203;<!-- 30 -->[Menu] Use Popup instead of Popper (#40731) @michaldudak
- &#8203;<!-- 29 -->[useNumberInput] Integrate useNumberInput with useControllableReducer (#40206) @mj12albert
- &#8203;<!-- 11 -->[Slider] Add support for Arrow Down/Up + Shift and Page Up/Down keys (#40676) @mnajdova

### Docs

- &#8203;<!-- 34 -->[base-ui] Update usage.md (#40916) @adebiyial
- &#8203;<!-- 33 -->[base-ui] Improve Base UI traffic from Material UI (#40875) @oliviertassinari
- &#8203;<!-- 32 -->[base-ui] Change Radio component terminology from Button to Group (#40888) @danilo-leal
- &#8203;<!-- 42 -->[base-ui] Remove redundant "Styled" prefix (#40478) @oliviertassinari
- &#8203;<!-- 48 -->[base-ui] Update listbox slot style in demo (#40952) @sai6855
- &#8203;<!-- 14 -->[material-ui] Fix createTheme import and markdown format in the Next.js guide (#40895) @hsmtkk
- &#8203;<!-- 13 -->[material-ui] Correct Google font CDN URL as Roboto 600 weight is not used (#40852) @xuhdev
- &#8203;<!-- 14 -->[material-ui] Replace the Album template with a landing page (#37557) @zanivan
- &#8203;<!-- 21 -->[material-ui] Add deprecations migration guide (#40767) @DiegoAndai
- &#8203;<!-- 22 -->[material-ui] Improve aria-label throughout the Button Group demos (#40892) @danilo-leal
- &#8203;<!-- 17 -->[joy-ui] Update the Overview callout (#40900) @danilo-leal
- &#8203;<!-- 22 -->Fix image size and dark mode @oliviertassinari
- &#8203;<!-- 21 -->Migrate links from legacy.reactjs.org to react.dev @oliviertassinari
- &#8203;<!-- 20 -->Fix 301 links @oliviertassinari
- &#8203;<!-- 19 -->Fix outdated link @oliviertassinari
- &#8203;<!-- 18 -->Fix URL and typo in CONTRIBUTING.md (#40899) @Smileek

### Core

- &#8203;<!-- 28 -->[blog] Optimize images for /blog/mui-x-v7-beta/ @oliviertassinari
- &#8203;<!-- 27 -->[blog] Clarify barrel index tree-shaking @oliviertassinari
- &#8203;<!-- 26 -->[code-infra] Simplify bug reproduction (#40833) @oliviertassinari
- &#8203;<!-- 25 -->[code-infra] Prepare publishing @mui-internal/typescript-to-proptypes to npm (#40842) @michaldudak
- &#8203;<!-- 40 -->[code-infra] Remove babel alias from the docs (#40792) @Janpot
- &#8203;<!-- 24 -->[core] Use Google Font v2 API @oliviertassinari
- &#8203;<!-- 23 -->[core] Add missing change to v5.15.7 changelog (#40872) @DiegoAndai
- &#8203;<!-- 31 -->[core] Normalize `<meta name="viewport" />`` @oliviertassinari
- &#8203;<!-- 28 -->[dependencies] Do not update envinfo test dependencies (#40950) @michaldudak
- &#8203;<!-- 17 -->[docs-infra] Fix arbitrary gap between paragraphs in callouts (#40911) @oliviertassinari
- &#8203;<!-- 16 -->[docs-infra] Allow developers to build their CodeSandbox export (#40878) @oliviertassinari
- &#8203;<!-- 15 -->[docs-infra] Improve StackBlitz support (#40832) @oliviertassinari
- &#8203;<!-- 21 -->[docs-infra] Improve support for absolute locale URL (#40940) @oliviertassinari
- &#8203;<!-- 31 -->[Menu][base-ui] Fix improperly merged tests (#40896) @michaldudak
- &#8203;<!-- 09 -->[utils] Use consistent build approach (#40837) @siriwatknp
- &#8203;<!-- 08 -->[website] Fix React missing key console error @oliviertassinari
- &#8203;<!-- 07 -->[website] Fix broken link @oliviertassinari
- &#8203;<!-- 06 -->[website] Fix heading structure (#40912) @oliviertassinari
- &#8203;<!-- 05 -->[website] Add Customer Support Agent role to careers page (#40890) @rluzists1
- &#8203;<!-- 04 -->[website] Refine the Material UI homepage demo (#40881) @danilo-leal
- &#8203;<!-- 03 -->[website] Use em-dash when relevant over hyphen @oliviertassinari
- &#8203;<!-- 03 -->[zero] Fix build for demo next.js app (#40854) @brijeshb42

All contributors of this release in alphabetical order: @adebiyial, @brijeshb42, @danilo-leal, @DiegoAndai, @hsmtkk, @Janpot, @michaldudak, @mj12albert, @mnajdova, @oliviertassinari, @rluzists1, @romgrk, @sai6855, @siriwatknp, @Smileek, @xuhdev, @zanivan

## 5.15.7

<!-- generated comparing v5.15.6..master -->

_Jan 31, 2024_

A big thanks to the 21 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.15.7`

- &#8203;<!-- 55 -->[Select] Fix to show notched outline when `displayEmpty` (#40865) @ZeeshanTamboli
- &#8203;<!-- 51 -->[Avatar] Improve fallback when `children` is empty string or boolean (#40766) @mirus-ua
- &#8203;<!-- 50 -->[AvatarGroup] Refactor component thereby fixing custom spacing logic (#40686) @ZeeshanTamboli

### `@mui/codemod@5.15.7`

- &#8203;<!-- 38 -->Add accordion props deprecation (#40771) @siriwatknp

### `@mui/system@5.15.7`

- &#8203;<!-- 56 -->[zero-runtime] Use lodash instead of its subpackages (#40868) @michaldudak
- &#8203;<!-- 19 -->Add `applyStyles()` to theme (#40667) @siriwatknp
- &#8203;<!-- 02 -->[zero] Use `theme.applyStyles` in the demo app (#40787) @siriwatknp
- &#8203;<!-- 01 -->[zero] Add `useThemeProps` processor (#40648) @siriwatknp

### `@mui/utils@5.15.7`

- &#8203;<!-- 16 -->[core] Remove unnecessary default export (#40788) @siriwatknp
- &#8203;<!-- 15 -->[core] Convert all exports to modules (#39882) @mnajdova
- &#8203;<!-- 20 -->[perf] Prevent unneeded `clearTimeout` calls (#39060) @romgrk

### `@mui/base@5.0.0-beta.34`

- &#8203;<!-- 48 -->[Input] Add OTP input demo (#40539) @sai6855
- &#8203;<!-- 47 -->[Menu] Focus last item after opening a menu using up arrow (#40764) @Jaswanth-Sriram-Veturi
- &#8203;<!-- 46 -->[Menu] Focus Menu Items on hover (#40755) @michaldudak

### `@mui/joy@5.0.0-beta.25`

- &#8203;<!-- 22 -->Change the color scheme type to `SupportedColorScheme` (#40776) @Nikhilh26

### `@mui/lab@5.0.0-alpha.163`

- &#8203;<!-- 21 -->[TabContext] Support number type in `value` (#40829) @srinidhi9831

### Docs

- &#8203;<!-- 57 -->[material-ui] Fix the icon preview dialog (#40863) @danilo-leal
- &#8203;<!-- 53 -->[material-ui] Fix typo on styled-components guide (#40858) @dancielos
- &#8203;<!-- 49 -->[base-ui] Fix CSS vars from the plain CSS theme stylesheet (#40762) @zanivan
- &#8203;<!-- 31 -->[material-ui][Divider] Remove light prop references from docs (#40782) @sai6855
- &#8203;<!-- 30 -->Fix build @oliviertassinari
- &#8203;<!-- 29 -->Add support pages for each product @oliviertassinari
- &#8203;<!-- 28 -->Add survey banner to docs and website (#40553) @joserodolfofreitas
- &#8203;<!-- 24 -->[Menu] Fix hydration mismatch error on Base UI's Menu docs (#40758) @michaldudak
- &#8203;<!-- 25 -->[material-nextjs] Add theming and configuration content to the page (#40626) @siriwatknp

### Core

- &#8203;<!-- 54 -->[website] Move `React Engineer - X` into the future roles section (#40867) @DanailH
- &#8203;<!-- 52 -->[material-ui][test][Alert] Add action, icon, and iconMapping tests (#40682) @DiegoAndai
- &#8203;<!-- 45 -->[blog] Lint duplicate h1 on the page (#40835) @oliviertassinari
- &#8203;<!-- 44 -->[blog] MUI X v7 beta announcement blogpost (#40784) @joserodolfofreitas
- &#8203;<!-- 43 -->[code-infra] Remove custom TS installation script (#40636) @michaldudak
- &#8203;<!-- 42 -->[code-infra] Correct API Docs Builder dependencies (#40775) @michaldudak
- &#8203;<!-- 41 -->[code-infra] Migrate to prettier async APIs (#40668) @Janpot
- &#8203;<!-- 40 -->[code-infra] Refined docs generation (#40603) @alexfauquette
- &#8203;<!-- 39 -->[code-infra] Explain how to install the browsers (#40474) @oliviertassinari
- &#8203;<!-- 37 -->`missingKeyGenerator` do no longer exist (#40830) @oliviertassinari
- &#8203;<!-- 36 -->Rely on immutable ref when possible (#40831) @oliviertassinari
- &#8203;<!-- 35 -->Remove deprecated `@types/markdown-to-jsx` package from docs (#40828) @ZeeshanTamboli
- &#8203;<!-- 34 -->Remove unneeded `@slack/web-api` package (#40840) @ZeeshanTamboli
- &#8203;<!-- 33 -->Clarify TODO instruction @oliviertassinari
- &#8203;<!-- 32 -->Remove unneeded use-clients (#40663) @oliviertassinari
- &#8203;<!-- 27 -->[docs-infra] Fix anchor link hook (#40836) @oliviertassinari
- &#8203;<!-- 26 -->[docs-infra] Avoid layout shift on docs-pages (#40749) @oliviertassinari
- &#8203;<!-- 23 -->[examples] Fix build on Next.js Pages Router examples (#40665) @oliviertassinari
- &#8203;<!-- 18 -->[test] Speed up the envinfo test (#40669) @michaldudak
- &#8203;<!-- 17 -->[typescript-to-proptypes] Allow to represent dates as `PropTypes.object` (#40774) @flaviendelangle
- &#8203;<!-- 14 -->[website] Add new Base UI role (#40773) @colmtuite
- &#8203;<!-- 13 -->[website] Fix a couple of rough edges (#40849) @danilo-leal
- &#8203;<!-- 12 -->[website] Small polishing after latest changes to the theme (#40846) @zanivan
- &#8203;<!-- 11 -->[website] Polish some pages and stray components (#40797) @danilo-leal
- &#8203;<!-- 10 -->[website] Refine the careers page slightly (#40793) @danilo-leal
- &#8203;<!-- 09 -->[website] Fix missing key on the Testimonials section (#40791) @Janpot
- &#8203;<!-- 08 -->[website] Fix Footer layout shift (#40786) @oliviertassinari
- &#8203;<!-- 07 -->[website] Revamp the testimonial section in the homepage (#40752) @danilo-leal
- &#8203;<!-- 06 -->[website] Fix pricing license model toggle style (#40747) @oliviertassinari
- &#8203;<!-- 05 -->[website] Fine-tune colors and styles on the branding theme (#40751) @danilo-leal
- &#8203;<!-- 04 -->[website] Fix Toggle Button styles in the homepage demos (#40744) @mohamedsaiedd
- &#8203;<!-- 03 -->[website] Update stats on the testimonials section (#40769) @EyaOuenniche

All contributors of this release in alphabetical order: @alexfauquette, @colmtuite, @danilo-leal, @DiegoAndai, @EyaOuenniche, @flaviendelangle, @Janpot, @Jaswanth-Sriram-Veturi, @joserodolfofreitas, @michaldudak, @mirus-ua, @mnajdova, @mohamedsaiedd, @Nikhilh26, @oliviertassinari, @romgrk, @sai6855, @siriwatknp, @srinidhi9831, @zanivan, @ZeeshanTamboli

## 5.15.6

<!-- generated comparing v5.15.5..master -->

_Jan 22, 2024_

A big thanks to the 17 contributors who made this release possible. Here are some highlights ✨:
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.15.6`

- &#8203;<!-- 29 -->[Avatar] Use variants api (#40324) @mnajdova
- &#8203;<!-- 27 -->[TablePagination] Accept readonly array for `rowsPerPageOptions` prop (#40481) @pcorpet
- &#8203;<!-- 06 -->[PaginationItem] Deprecate classes for v6 (#40673) @sai6855
- &#8203;<!-- 05 -->[Rating] Fix rating width via min-content (#40503) @devhik0

### `@mui/base@5.0.0-beta.33`

- &#8203;<!-- 26 -->[Select] Fix display of selected Options with rich content (#40689) @michaldudak
- &#8203;<!-- 25 -->[Select] Use Popup instead of Popper (#40524) @michaldudak
- &#8203;<!-- 26 -->[useMenuButton] Fix non native button triggers (#40645) @DiegoAndai

### `@mui/system@5.15.6`

- &#8203;<!-- 02 -->[zero] Fix theme token import in source file (#40691) @brijeshb42
- &#8203;<!-- 01 -->[zero] Add support for css import (#40541) @brijeshb42

### `@mui/icons-material@5.15.6`

- &#8203;<!-- 07 -->[icons-material] Fix icons package.json version (#40655) @mj12albert

### Docs

- &#8203;<!-- 28 -->[base-ui] Polish the Slider demos (#40332) @danilo-leal
- &#8203;<!-- 24 -->[base-ui][Slider] Fix plain CSS demo's wrong keyboard behavior (#40652) @mnajdova
- &#8203;<!-- 23 -->[base-ui][TextareaAutosize] Add border-box to demo (#40646) @ANUGLYPLUGIN
- &#8203;<!-- 16 -->Fix brand name non-breaking space (#40701) @oliviertassinari
- &#8203;<!-- 15 -->Improve error message for MUI Vale rule @oliviertassinari
- &#8203;<!-- 14 -->Add notification to publish the survey (#40552) @joserodolfofreitas
- &#8203;<!-- 04 -->[system] Explain a bit more how AppRouterCacheProvider works @oliviertassinari
- &#8203;<!-- 12 -->[joy-ui] Move tutorial to iframe (#40567) @oliviertassinari
- &#8203;<!-- 10 -->[material-ui][Slider] Remove custom divs from new demo (#40674) @zanivan
- &#8203;<!-- 11 -->[material-ui] Improve TabContext, TabList, and TabPanel documentation (#40587) @anle9650
- &#8203;<!-- 09 -->[material-ui][Slider] Added custom mark labels demo (#40647) @DonikaV

### Core

- &#8203;<!-- 30 -->[core] Fix RXDB-logo Url (#40724) @mohamedsaiedd
- &#8203;<!-- 24 -->[code-infra] Improve proptypes-generation (#40617) @alexfauquette
- &#8203;<!-- 22 -->[code-infra] Disable pnpm package cache on CircleCI (#40670) @michaldudak
- &#8203;<!-- 21 -->[code-infra] Add missing package to CodeSandbox CI config (#40657) @michaldudak
- &#8203;<!-- 20 -->[code-infra] Remove unnecessary @mui/utils dependency from api-docs-builder (#40632) @michaldudak
- &#8203;<!-- 19 -->[core] Polish issue templates @oliviertassinari
- &#8203;<!-- 13 -->[docs-infra] Support markdown link in slots descriptions (#40679) @alexfauquette
- &#8203;<!-- 08 -->[examples] Simplify Next.js example (#40661) @oliviertassinari
- &#8203;<!-- 03 -->[website] Fix broken styles on Base UI page (#40683) @michaldudak

All contributors of this release in alphabetical order: @alexfauquette, @anle9650, @ANUGLYPLUGIN, @brijeshb42, @danilo-leal, @devhik0, @DiegoAndai, @DonikaV, @joserodolfofreitas, @michaldudak, @mj12albert, @mnajdova, @mohamedsaiedd, @oliviertassinari, @pcorpet, @sai6855, @zanivan

## 5.15.5

<!-- generated comparing v5.15.4..master -->

_Jan 17, 2024_

A big thanks to the 18 contributors who made this release possible. Here are some highlights ✨:

- Base UI's CSS class prefix is now `base-` (#40205) @michaldudak
- Bug fixes, and a lot of improvements to code and docs infra

### `@mui/material@5.15.5`

- &#8203;<!-- 42 -->[Accordion] Deprecate \*Props props and classes for v6 (#40418) @DiegoAndai
- &#8203;<!-- 41 -->[Alert] Update TypeScript types to allow color override types to be added to `iconMapping` and `severity` props (#40551) @2metres
- &#8203;<!-- 27 -->[Dialog] Remove deprecated onBackdropClick from Dialog tests (#40505) @sai6855
- &#8203;<!-- 26 -->[Divider] Deprecate props and classes for v6 (#40563) @sai6855

### `@mui/material-next@6.0.0-alpha.118`

- &#8203;<!-- 36 -->[ButtonGroup] Apply MD3 style to `ButtonGroup` (#40124) @lhilgert9

### `@mui/base@5.0.0-beta.32`

#### Breaking changes

- &#8203;<!-- 40 -->Change the CSS class prefix to `base-` (#40205) @michaldudak

The class prefix of Base UI components have been changed from `Mui-` to `base-`. This only affects codebases that uses class names verbatim, i.e. not exposed by JS objects such as `buttonClasses`, but as plain strings or in CSS stylesheets (`.MuiButton.root`)

To adapt your code to the new pattern:

- replace all occurrences of the regex `.Mui([A-Z][A-Za-z]*)-` with `.base-$1-` (so `MuiButton-root` becomes `base-Button-root`, etc.),
- replace all occurrences of the regex `.Mui-([a-z]*)` with `.base--$1` (so `Mui-disabled` becomes `base--disabled`, etc.).

#### Changes

- &#8203;<!-- 39 -->[Select] Fix screen-reader CSS to avoid body scrollbar (#40599) @brijeshb42
- &#8203;<!-- 38 -->[Switch] Add border-box to demos (#40638) @zanivan

### `@mui/joy@5.0.0-beta.23`

- &#8203;<!-- 11 -->[ModalDialog] Fix ModalDialog layout prop override (#40512) @maakcode
- &#8203;<!-- 10 -->[RadioGroup] Allow zero number as a value (#40344) @aacevski

### `@mui/codemod@5.15.5`

- &#8203;<!-- 31 -->Allow `json` files to be transformed (#40536) @ZeeshanTamboli

### `@mui/lab@5.0.0-alpha.161`

- &#8203;<!-- 09 -->Update `@mui/material` peer dependency version (#40528) @ZeeshanTamboli

### `@mui/material-nextjs@5.15.5`

- &#8203;<!-- 08 -->Fix release script (#40519) @petrovmiroslav
- &#8203;<!-- 07 -->Support Nonces in the App Router Provider (#40269) @josh-feldman
- &#8203;<!-- 06 -->Polish @mui/material-nextjs (#40473) @oliviertassinari

### `@mui/system@5.15.5`

- &#8203;<!-- 05 -->Fix import path for @mui/system in vite apps (#40490) @brijeshb42
- &#8203;<!-- 02 -->Fix css vars generation and simplify the code (#40530) @siriwatknp
- &#8203;<!-- 01 -->Identify zero runtime styled path (#40555) @brijeshb42

### Docs

- &#8203;<!-- 35 -->[joy-ui][Card] Fix text alignment in horizontal aligned card demo (#40562) @ZeeshanTamboli
- &#8203;<!-- 25 -->Improve instructions about peer dependencies (#40621) @danilo-leal
- &#8203;<!-- 24 -->Solve page description length @oliviertassinari
- &#8203;<!-- 23 -->Fix MUI Treasury links (#40561) @siriwatknp
- &#8203;<!-- 17 -->[material-ui] Update <ListItem button/> to ListItemButton in demos (#40564) @sai6855
- &#8203;<!-- 16 -->[material-ui] Revise the Snackbar page (#39298) @danilo-leal
- &#8203;<!-- 15 -->[material-ui] Sharpen Material 3 copy on demo pages (#40546) @samuelsycamore
- &#8203;<!-- 14 -->[material-ui] Fix typo on Next.js integration guide (#40538) @zanivan
- &#8203;<!-- 13 -->[material-ui][Snackbar] Remove unused `State` interface from Consecutive Snackbars demo (#40410)
  @zinoroman
- &#8203;<!-- 12 -->[website] Resolve broken links reported by `docs:link-check` (#40547) @samuelsycamore

### Core

- &#8203;<!-- 37 -->[blog] Fix 404 link to MUI Treasury @oliviertassinari
- &#8203;<!-- 34 -->[code-infra] Add `run` command to deploy docs (#40513) @siriwatknp
- &#8203;<!-- 32 -->[core] Update the lockfile (#40628) @michaldudak
- &#8203;<!-- 31 -->[core] Remove dead code and follow standard @oliviertassinari
- &#8203;<!-- 30 -->[core] Simplify server detection (#40471) @oliviertassinari
- &#8203;<!-- 29 -->[core] Sync playwright cache between MUI X and Material UI (#40475) @oliviertassinari
- &#8203;<!-- 28 -->[dependencies] Bump tough-cookie (#40437) @michaldudak
- &#8203;<!-- 22 -->[docs-infra] Enforce brand name rules (#40525) @oliviertassinari
- &#8203;<!-- 21 -->[docs-infra] Minimize ad layout shift on mobile (#40582) @oliviertassinari
- &#8203;<!-- 20 -->[docs-infra] Improve API page deprecation info (#40440) @DiegoAndai
- &#8203;<!-- 19 -->[docs-infra] Remove old tocs banners (#40537) @oliviertassinari
- &#8203;<!-- 18 -->[docs-infra] Remove dead code aria-label sponsors (#40526) @oliviertassinari
- &#8203;<!-- 04 -->[utils] Centralize clamp implementation in utils (#40267) @Kamino0
- &#8203;<!-- 03 -->[website] Polish the Base UI page demos (#40504) @danilo-leal

All contributors of this release in alphabetical order: @2metres, @aacevski, @brijeshb42, @danilo-leal, @DiegoAndai, @josh-feldman, @Kamino0, @lhilgert9, @maakcode, @michaldudak, @oliviertassinari, @petrovmiroslav, @sai6855, @samuelsycamore, @siriwatknp, @zanivan, @ZeeshanTamboli, @zinoroman

## 5.15.4

<!-- generated comparing v5.15.3..master -->

_Jan 10, 2024_

A big thanks to the 22 contributors who made this release possible. Here are some highlights:

- ✨ Material UI's [ToggleButtonGroup](https://mui.com/material-ui/react-toggle-button/) now supports non-button element as a child (e.g., showing a Tooltip on a disabled ToggleButton) (#40220) @Methuselah96

### `@mui/material@5.15.4`

- [TextField][FormLabel][InputLabel][FormControl] Use exact children type to allow React children type augmentation (#38872) @nicegamer7
- [Select] Add form submission regression test (#40176) @mj12albert
- [ToggleButtonGroup] Support different elements under it (#40220) @Methuselah96
- [ClickAwayListener] Fix export of types (#40485) @illume

### `@mui/material-nextjs@5.15.4`

- Improve build size issue (#40436) @siriwatknp

### `@mui/icons-material@5.15.4`

- Update the icons (#40365) @michaldudak

### `@mui/system@5.15.4`

- [zero] Move zero runtime related packages (#40426) @brijeshb42
- Fix all use of styled(Box) (#40449) @oliviertassinari

### `@mui/utils@5.15.4`

- Fix `isPlainObject` to work across realm (#39981) @brijeshb42

### `@mui/base@5.0.0-beta.31`

- [base-ui][NumberInput] Remove inputId and inputRef types from NumberInput component (#40425) @sai6855

### `@mui/joy@5.0.0-beta.22`

- [Badge] Shouldn't crash when using custom size (#39986) @iamsaumya
- [ToggleButtonGroup] Add `JoyToggleButtonGroup` to extendTheme's components type export (#40407) @RiceCrisp

### Docs

- [base-ui] Visual tweaks to the plain CSS theme stylesheet (#40487) @zanivan
- [base-ui] Add tokens to plain CSS theme stylesheet (#40113) @zanivan
- [base-ui] Update the overview page (#40412) @danilo-leal
- [material-ui][Drawer] Resolve flickering when double-clicking on the backdrop to close it (#40343) @aacevski
- [material-ui] Refactor form submission in FormDialog component (#40470) @sai6855
- [material-ui] Replace recharts with MUI X charts on the Dashboard template (#40107) @alexfauquette
- [material-ui] Revise the Alert demo page (#34892) @samuelsycamore
- [material-ui] Revise the Accordion page (#40284) @anle9650
- [material-ui] Add docs for complementary Card components (#40346) @anle9650
- [material-ui] Add Material 3 components page (#40350) @DiegoAndai
- Fix strange italic style @oliviertassinari
- Fix references to ESLint @oliviertassinari
- Fix 301 redirections @oliviertassinari
- Right-to-left revision and addition to Joy UI (#39158) @danilo-leal

### Core

- [examples] Use material-nextjs integration package (#40199) @siriwatknp
- [code-infra] Configure build dependencies in nx (#40482) @michaldudak
- [code-infra] Don't require noreferrer on target link (#40447) @oliviertassinari
- [code-infra] Use tsx instead of ts-node (#40428) @michaldudak
- [code-infra] Add options to docs-api generation to support X code structure (#40111) @alexfauquette
- [docs-infra] Add types for withDocsInfra (#40301) @oliviertassinari
- [docs-infra] Fix anchor links in API pages (#40450) @oliviertassinari
- [docs-infra] Fix API table full-width (#40476) @oliviertassinari
- [docs-infra] Fix the copy button overlapping with the scrollbar (#40405) @danilo-leal
- [docs-infra] Remove explicit `mui-x` dependency resolving (#40257) @LukasTy
- [docs-infra] Update the outlined Demo container dark mode color (#40488) @danilo-leal
- [core] Lock file maintenance (#34538) @renovate[bot]
- [core] Remove duplicate devDependencies (#40438) @michaldudak
- [core] Remove issue emoji @oliviertassinari
- [core] Move prefetch at the core, will propagate to MUI X @oliviertassinari
- [core] Change package manager to pnpm (#36287) @Janpot
- [core][docs] Remove the "Understand MUI packages" page (#39835) @savalaram-redkar
- [website] Evolve the Developer Advocate role (#40337) @oliviertassinari
- [website] Fix default social card @oliviertassinari
- [test] Restore the `t` command (#40430) @michaldudak

All contributors of this release in alphabetical order: @aacevski, @alexfauquette, @anle9650, @brijeshb42, @danilo-leal, @DiegoAndai, @iamsaumya, @illume, @Janpot, @LukasTy, @Methuselah96, @michaldudak, @mj12albert, @nicegamer7, @oliviertassinari, @renovate[bot], @RiceCrisp, @sai6855, @samuelsycamore, @savalaram-redkar, @siriwatknp, @zanivan

## 5.15.3

<!-- generated comparing v5.15.2..master -->

_Jan 3, 2024_

A big thanks to the 11 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material-nextjs@5.15.3`

- &#8203;<!-- 06 -->[material-nextjs] Fix order of emotion server (#40409) @siriwatknp

### `@mui/base@5.0.0-beta.30`

- &#8203;<!-- 40 -->[useSlider] Rearrange passive option in eventlisteners (#40235) @Kamino0

### `@mui/lab@5.0.0-alpha.159`

- &#8203;<!-- 14 -->Add use client directive (#40358) @DiegoAndai

### `@mui/material-next@6.0.0-alpha.116`

- &#8203;<!-- 13 -->[List] Copy all List\* components to material-next package (#40367) @sai6855
- &#8203;<!-- 12 -->Update CONTRIBUTING guide given v6/v7 rescheduling (#40363) @DiegoAndai
- &#8203;<!-- 11 -->[MenuItem] Fix spec import (#40271) @DiegoAndai
- &#8203;<!-- 10 -->[Option] Add Option component (#40270) @DiegoAndai
- &#8203;<!-- 09 -->[Slider] Replace lodash with internal utils (#40261) @DiegoAndai
- &#8203;<!-- 08 -->[Switch] Change files to TypeScript (#39894) @lhilgert9
- &#8203;<!-- 07 -->[theme] Move ref palette out of color schemes (#40341) @DiegoAndai

### Docs

- &#8203;<!-- 41 -->[base-ui] Polish the Table Pagination demos (#40281) @danilo-leal
- &#8203;<!-- 22 -->[joy-ui] Fix dashboard template console errors (#40316) @oliviertassinari
- &#8203;<!-- 21 -->[joy-ui] Fix image size on the Files template (#40315) @oliviertassinari
- &#8203;<!-- 20 -->[material-ui] Revise the Divider page (#40356) @danilo-leal
- &#8203;<!-- 19 -->[material-ui] Polish the Next.js integration page (#40317) @oliviertassinari
- &#8203;<!-- 18 -->[material-ui] Adding autoFocus on Virtual Popover (#40239) @aacevski
- &#8203;<!-- 17 -->[material-ui] Revise the Roadmap page (#40054) @danilo-leal
- &#8203;<!-- 16 -->[material-ui] Update the "showing and hiding" section on the Tooltip page (#40283) @anle9650
- &#8203;<!-- 05 -->[material-ui] Fix Slider's customized iOS demo to use updated official colors (#39813) @Super-Kenil
- &#8203;<!-- 15 -->[examples] Simplify Next.js example (#40318) @oliviertassinari

### Core

- &#8203;<!-- 39 -->[blog] Update open-graph cards for all posts (#40328) @danilo-leal
- &#8203;<!-- 38 -->[blog] Correct git diff @oliviertassinari
- &#8203;<!-- 37 -->[code-infra] Update lerna and unpin its version (#40399) @michaldudak
- &#8203;<!-- 36 -->[code-infra] Break package dependency cycle between @mui/material and @mui/icons-material (#40400) @michaldudak
- &#8203;<!-- 35 -->[code-infra] Break package dependency cycles (#40398) @michaldudak
- &#8203;<!-- 34 -->[code-infra] Sync bug issue template (#40305) @oliviertassinari
- &#8203;<!-- 33 -->[docs] Fix 301 link to Base UI (#40396) @oliviertassinari
- &#8203;<!-- 32 -->[docs] Link new MUI X components in sidnav (#40345) @oliviertassinari
- &#8203;<!-- 31 -->[docs] Fix 301 links to Toolpad @oliviertassinari
- &#8203;<!-- 30 -->[docs] Remove old notifications @oliviertassinari
- &#8203;<!-- 29 -->[docs] Always mention the npm tag with npx (#40335) @oliviertassinari
- &#8203;<!-- 28 -->[docs] Reduce network use on the All Components pages (#40313) @oliviertassinari
- &#8203;<!-- 27 -->[docs-infra] Fix missing button aria-label (#40394) @oliviertassinari
- &#8203;<!-- 26 -->[docs-infra] Fix a11y violation rule (#40393) @oliviertassinari
- &#8203;<!-- 25 -->[docs-infra] Prefetch pages on hover (#40314) @oliviertassinari
- &#8203;<!-- 24 -->[docs-infra] Fix footer links to link to the main domain (#40373) @oliviertassinari
- &#8203;<!-- 23 -->[docs-infra] Add stray design adjustments (#40347) @danilo-leal
- &#8203;<!-- 04 -->[website] Fix Base UI page's component section imports & styles (#40231) @danilo-leal
- &#8203;<!-- 03 -->[website] Fix outdated Nhost image link @oliviertassinari
- &#8203;<!-- 02 -->[website] Shorten Joy UI description, 7 chars too long @oliviertassinari
- &#8203;<!-- 01 -->[website] Update some social preview images (#40282) @danilo-leal

All contributors of this release in alphabetical order: @aacevski, @anle9650, @danilo-leal, @DiegoAndai, @Kamino0, @lhilgert9, @michaldudak, @oliviertassinari, @sai6855, @siriwatknp, @Super-Kenil

## 5.15.2

<!-- generated comparing v5.15.1..master -->

_Dec 25, 2023_

A big thanks to the 13 contributors who made this release possible. Here are some highlights ✨:

- 🚀 Added support for callbacks in the [variant's props definition](https://mui.com/material-ui/customization/theme-components/#creating-new-component-variants) (#40094) @mnajdova
- 💫 Published a [blogpost](https://mui.com/blog/2023-material-ui-v6-and-beyond/) for the 2024's plan about Material UI

### `@mui/material@5.15.2`

- &#8203;<!-- 31 -->[Badge] Use the variants API in the styled call (#40213) @mnajdova
- &#8203;<!-- 04 -->[Paper] Add missing Paper classes descriptions (#40300) @sai6855

### `@mui/system@5.15.2`

- &#8203;<!-- 03 -->Support props callback in the variant's definition (#40094) @mnajdova

### `@mui/base@5.0.0-beta.29`

- &#8203;<!-- 29 -->[Popup] Use context-based transition API (#39326) @michaldudak
- &#8203;<!-- 28 -->[Popup] Popup no longer opens outside viewport (#39827) @adamhylander
- &#8203;<!-- 27 -->[useSelect] Refactor to use DOM focus management instead of active descendant (#39675) @DiegoAndai

### `@mui/material-next@6.0.0-alpha.115`

- &#8203;<!-- 15 -->[material-next][Divider]Divider ts support (#40307) @sai6855

### Docs

- &#8203;<!-- 30 -->[base-ui] Fix form submission Select demo (#40014) @ZeeshanTamboli
- &#8203;<!-- 24 -->[blog] Add Material UI v6 and beyond blog post (#40242) @mnajdova
- &#8203;<!-- 10 -->[material-ui] Fix broken links in the All components page (#40303) @muazaqdas
- &#8203;<!-- 09 -->[material-ui] Fix broken links on the All components page (#40279) @danilo-leal
- &#8203;<!-- 08 -->[material-ui] Add aria-current for nav tabs demo (#39594) @Kimzify
- &#8203;<!-- 07 -->[material-ui] Add an "All components" page (#40256) @danilo-leal
- &#8203;<!-- 06 -->[material-ui][Tooltip] Add demo to show how to change distance between tooltip and its anchor (#40087) @anle9650

### Core

- &#8203;<!-- 26 -->[blog] Fix 301 redirection @oliviertassinari
- &#8203;<!-- 25 -->[blog] Reduce max image size @oliviertassinari
- &#8203;<!-- 23 -->[code-infra] Extract Babel macro from mui-utils (#40262) @michaldudak
- &#8203;<!-- 22 -->[core] Simplify a bit the release instructions @oliviertassinari
- &#8203;<!-- 21 -->[core] Push force to deploy is not unexpected @oliviertassinari
- &#8203;<!-- 20 -->[core] Fix CSS2 vs. CSS3 ::after syntax @oliviertassinari
- &#8203;<!-- 19 -->[core] Fix CSS2 vs. CSS3 ::before syntax @oliviertassinari
- &#8203;<!-- 05 -->[core] Use direct import from utils package (#40254) @siriwatknp
- &#8203;<!-- 18 -->[core] Yaml format match most common convention @oliviertassinari
- &#8203;<!-- 17 -->[core] Polish docs-feedback issue template @oliviertassinari
- &#8203;<!-- 16 -->[core] Pin Node version on GitHub Actions to 18.18 (#40187) @michaldudak
- &#8203;<!-- 14 -->[docs] Fix SEO regression (#40306) @oliviertassinari
- &#8203;<!-- 13 -->[docs] Fix docs redirections @oliviertassinari
- &#8203;<!-- 12 -->[docs] Update the Contributing guide to give more guidance on documentation (#40274) @samuelsycamore
- &#8203;<!-- 11 -->[docs-infra] Add polish to the inline code block (#40260) @danilo-leal
- &#8203;<!-- 02 -->[website] Update Gold sponsors: add RxDB, standardize formatting (#40273) @samuelsycamore
- &#8203;<!-- 01 -->[website] Revert change to homepage SEO attributes @oliviertassinari

All contributors of this release in alphabetical order: @adamhylander, @anle9650, @danilo-leal, @DiegoAndai, @Kimzify, @michaldudak, @mnajdova, @muazaqdas, @oliviertassinari, @sai6855, @samuelsycamore, @siriwatknp, @ZeeshanTamboli

## 5.15.1

<!-- generated comparing v5.15.0..master -->

_Dec 19, 2023_

A big thanks to the 15 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.15.1`

- &#8203;<!-- 13 -->[CardActions][DialogActions] Apply margin for all children except for 1st child (#40168) @sai6855
- &#8203;<!-- 03 -->[TablePagination] Add ability to change icons in TablePaginationActions using `slots` and `slotProps` (#33797) @pratikkarad
- &#8203;<!-- 13 -->[CssVarsProvider] Fix HSL breaking button styles (#39869) @gitstart

### `@mui/joy@5.0.0-beta.19`

- &#8203;<!-- 05 -->[FormControl] Fix issue with the conditional setting of `htmlFor` and `id` attributes not functioning properly for form labels (#40180) @ReaZzy
- &#8203;<!-- 04 -->[typescript] Address TypeScript issue with custom zIndex not functioning (#40133) @qiweiii

### `@mui/styled-engine-sc@6.0.0-alpha.9`

- &#8203;<!-- 02 -->[typescript] Fix `theme` being of type any (#40200) @mnajdova

### Docs

- &#8203;<!-- 09 -->[joy-ui] Fix typo in the CSS baseline page (#40222) @possibilities
- &#8203;<!-- 08 -->[joy-ui] Document missing Autocomplete props (#39979) @ZeeshanTamboli
- &#8203;<!-- 10 -->[joy-ui] Fix typo about Typography levels (#40230) @zanivan
- &#8203;<!-- 07 -->[material-ui] Update the related projects page to list `mui-tiptap` for rich text (#40216) @sjdemartini
- &#8203;<!-- 06 -->[material-ui] Fix typo in the Next.js integration page (#40209) @s8990

### Core

- &#8203;<!-- 14 -->[blog] Fix access to Notion without an account @oliviertassinari
- &#8203;<!-- 12 -->[core] Update workflows and issue templates to reflect the updated label (#40197) @MBilalShafi
- &#8203;<!-- 11 -->[docs] Replace Twitter with X (#40193) @mbrookes
- &#8203;<!-- 10 -->[docs-infra] Uplift the Algolia search modal design (#40186) @danilo-leal
- &#8203;<!-- 01 -->[website] Update product descriptions and social preview images (#32181) @danilo-leal

All contributors of this release in alphabetical order: @danilo-leal, @gitstart, @MBilalShafi, @mbrookes, @mnajdova, @oliviertassinari, @possibilities, @pratikkarad, @qiweiii, @ReaZzy, @s8990, @sai6855, @sjdemartini, @zanivan, @ZeeshanTamboli

## 5.15.0

<!-- generated comparing v5.14.20..master -->

_Dec 11, 2023_

A big thanks to the 15 contributors who made this release possible. Here are some highlights ✨:

- 🚀 Added [a new package for a better Material UI integration with Next.js](https://mui.com/material-ui/guides/nextjs) (#39947) @siriwatknp

### `@mui/material@5.15.0`

- &#8203;<!-- 12 -->Revert "[Select][material-ui] Add name to hidden input element" (#40174) @mj12albert
- &#8203;<!-- 02 -->[material-ui] Refactor ComponentsVariants type into a generic (#39623) @blakenetz

### `@mui/material-nextjs@5.15.0`

- &#8203;<!-- 01 -->[material-ui] Add new Next.js integration package (#39947) @siriwatknp

### `@mui/material-next@6.0.0-alpha.113`

- &#8203;<!-- 11 -->[Badge][material-next] Apply new OwnerState type to Badge (#40119) @lhilgert9
- &#8203;<!-- 06 -->[material-next][ButtonGroup] Change `ButtonGroup` files to ts (#39794) @lhilgert9

### `@mui/icons-material@5.15.0`

- &#8203;<!-- 05 -->[icons] Add X logo (#38811) @abreel

### `@mui/base@5.0.0-beta.27`

- &#8203;<!-- 10 -->[base-ui] useControllableReducer warns when controlled props become uncontrolled (and vice versa) (#39096) @mj12albert

### `@mui/joy@5.0.0-beta.18`

- &#8203;<!-- 04 -->[joy-ui][Radio][Input] Fix inheritance of disabled prop (#39934) @sai6855

### `@mui/lab@5.0.0-alpha.156`

- &#8203;<!-- 03 -->[lab][LoadingButton] LoadingButton now inherits props from ButtonGroup (#39679) @lhilgert9

### Docs

- &#8203;<!-- 09 -->[docs] Fix reference to non-existent checkmark in supported-components.md (#40056) @mbrookes
- &#8203;<!-- 09 -->[docs][base-ui] Add copy button & primary color picker to the component gallery page (#39884) @mnajdova
- &#8203;<!-- 08 -->[docs-infra] Update CodeSandbox links (#39992) @anle9650
- &#8203;<!-- 04 -->[material-ui][docs] Fix wrong root element for emotion styles in shadow DOM (#35326) @EloB
- &#8203;<!-- 03 -->[material-ui][docs] Move the responsive font charts from recharts to MUI X (#40097) @alexfauquette
- &#8203;<!-- 02 -->[joy-ui][templates] Remove outdated code (#40095) @zanivan
- &#8203;<!-- 05 -->[material-ui][docs][Popper] Update Positioned Popper demo styles (#40170) @sai6855

### Core

- &#8203;<!-- 08 -->[blog] Add a Phuket retreat blog post (#40055) @mikailaread
- &#8203;<!-- 07 -->[blog] Adjust the latest MUI X blog post (#40046) @danilo-leal
- &#8203;<!-- 05 -->[core] Migrate from tslint to eslint (#40020) @ZeeshanTamboli

All contributors of this release in alphabetical order: @abreel, @alexfauquette, @anle9650, @blakenetz, @danilo-leal, @EloB, @lhilgert9, @mbrookes, @mikailaread, @mj12albert, @mnajdova, @sai6855, @siriwatknp, @zanivan, @ZeeshanTamboli

## 5.14.20

<!-- generated comparing v5.14.19..master -->

_Dec 5, 2023_

A big thanks to the 14 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material-next@6.0.0-alpha.112`

- &#8203;<!-- 07 -->[Menu] Enable again the usage of the autoFocus prop (#39960) @mnajdova
- &#8203;<!-- 06 -->[ProgressIndicator] Apply MD3 style to `CircularProgress` (#39825) @lhilgert9

### Docs

- &#8203;<!-- 15 -->Remove outdated showcase (#40063) @oliviertassinari
- &#8203;<!-- 14 -->Support yaml as prism language (#40044) @Janpot
- &#8203;<!-- 12 -->[material-ui] Fix SearchAppBar width on `sm` screens (#40049) @DiegoAndai
- &#8203;<!-- 11 -->[material-ui] Update the basic Grid section copy (#40035) @REX500
- &#8203;<!-- 10 -->[material-ui] Add a react-admin example project (#39972) @fzaninotto
- &#8203;<!-- 09 -->[material-ui][TextField] Add a performance section (#39692) @anle9650
- &#8203;<!-- 05 -->[material-ui][tabs] Refine scrollable tabs description (#40037) @zanivan
- &#8203;<!-- 08 -->[system] Fix typo on the CSS theme variables page (#40040) @caweidmann

### Core

- &#8203;<!-- 17 -->[blog] Fix scrollbar on mobile (#40057) @oliviertassinari
- &#8203;<!-- 16 -->[blog] Fix link to charts getting started page (#40043) @alexfauquette
- &#8203;<!-- 13 -->[docs-infra] Simplify CSS classes extraction in API docs generator (#39808) @michaldudak
- &#8203;<!-- 04 -->[website] Polish dark mode colors (#40052) @danilo-leal
- &#8203;<!-- 03 -->[website] Add why Design Engineer for data grid (#40016) @oliviertassinari
- &#8203;<!-- 02 -->[website] Add stray fixes to the Base UI page (#40051) @danilo-leal
- &#8203;<!-- 01 -->[website] Revise the Developer Advocate job posting (#39210) @samuelsycamore

All contributors of this release in alphabetical order: @alexfauquette, @anle9650, @caweidmann, @danilo-leal, @DiegoAndai, @fzaninotto, @Janpot, @lhilgert9, @michaldudak, @mnajdova, @oliviertassinari, @REX500, @samuelsycamore, @zanivan

## 5.14.19

<!-- generated comparing v5.14.18..master -->

_Nov 29, 2023_

A big thanks to the 18 contributors who made this release possible. Here are some highlights ✨:

- 🐛 Fix Material UI Autocomplete behavior when there are duplicate labels (#36426) @islandryu
- 🚀 Added Material You Linear Progress to `material-next` package (#39807) @lhilgert9

### `@mui/material@5.14.19`

- &#8203;<!-- 44 -->[Autocomplete] Fix behavior when there are duplicate labels (#36426) @islandryu
- &#8203;<!-- 37 -->[Box] Added boxClasses (#39889) @sadik-malik
- &#8203;<!-- 18 -->[FilledInput] Fix slot props deepmerge order (#38922) @dhaub-exelixis
- &#8203;<!-- 12 -->[Select] Add id to hidden input element (#39414) @DarhkVoyd
- &#8203;<!-- 11 -->[Select] Remove unnecessary picking of `onChange` type from SelectInputProps (#39891) @ZeeshanTamboli

### `@mui/base@5.0.0-beta.25`

- &#8203;<!-- 46 -->[Menu] Fix navigation of items when 1st item is disabled (#39828) @sai6855
- &#8203;<!-- 42 -->[Modal] Refine demos (#39824) @zanivan
- &#8203;<!-- 41 -->[NumberInput] Implement `numberInputReducer` (#38723) @mj12albert
- &#8203;<!-- 40 -->[useNumberInput] Fix change handlers passed through slotProps (#39407) @mj12albert

### `@mui/joy@5.0.0-beta.16`

- &#8203;<!-- 38 -->[Box] Added boxClasses (#39895) @sadik-malik

### `@mui/system@5.14.19`

- &#8203;<!-- 36 -->[Box] Added boxClasses (#39896) @sadik-malik
- &#8203;<!-- 09 -->Add outlineColor to defaultSxConfig (#39962) @brijeshb42

### `@mui/types@7.2.10`

- &#8203;<!-- 08 -->Add `PartiallyRequired` type (#39939) @lhilgert9

### `@mui/material-next@6.0.0-alpha.111`

- &#8203;<!-- 47 -->[ProgressIndicator] Apply MD3 style to `LinearProgress` (#39807) @lhilgert9
- &#8203;<!-- 17 -->[FormHelperText] Add FormHelperText component (#39503) @mj12albert
- &#8203;<!-- 16 -->[IconButton] Copy IconButton to material-next (#39945) @mj12albert
- &#8203;<!-- 13 -->[Switch] Copy `Switch` to material next (#39887) @lhilgert9

### Docs

- &#8203;<!-- 19 -->[material-ui] Fix theme prop in v5 migration guide (#39976) @sai6855
- &#8203;<!-- 43 -->[base-ui] Improve Next.js Link docs (#39838) @oliviertassinari
- &#8203;<!-- 39 -->[base-ui] Export Base UI theme in stylesheet (#39694) @mnajdova
- &#8203;<!-- 52 -->[joy-ui] Fix the date min & max slot props values on the Input demo (#40018) @avikalpg
- &#8203;<!-- 35 -->[joy-ui][ButtonGroup] Fix orientation prop description (#39876) @sai6855
- &#8203;<!-- 25 -->[joy-ui] Update gif from the Dark Mode Optimization page (#39726) @danilo-leal
- &#8203;<!-- 24 -->[joy-ui] Fix h1 template (#40017) @oliviertassinari
- &#8203;<!-- 23 -->[joy-ui] Fix wrong product id @oliviertassinari
- &#8203;<!-- 22 -->[joy-ui] Fixes in theme scoping documentation (#39899) @ZeeshanTamboli
- &#8203;<!-- 21 -->[joy-ui] Refine the Email, Teams and Files templates (#39579) @zanivan
- &#8203;<!-- 15 -->[joy-ui][templates] Fix layout shift on Profile template (#40022) @zanivan
- &#8203;<!-- 14 -->[joy-ui][Templates] Update thumbnails (#39938) @zanivan
- &#8203;<!-- 20 -->[material-next] Add contributing guide (#39944) @mj12albert
- &#8203;<!-- 51 -->End v6 blogpost notification (#39879) @joserodolfofreitas
- &#8203;<!-- 31 -->Fix nested CSS warning (#39932) @mnajdova
- &#8203;<!-- 30 -->Make integration searchable (#39967) @oliviertassinari
- &#8203;<!-- 29 -->Fix use of quote in markdown (#39953) @oliviertassinari
- &#8203;<!-- 28 -->Show design links on Joy UI (#39955) @oliviertassinari
- &#8203;<!-- 27 -->Restore Algolia results when searching for "Figma" (#39956) @oliviertassinari
- &#8203;<!-- 26 -->Fix two tone icon dark mode color (#39868) @mnajdova

### Core

- &#8203;<!-- 45 -->Improve lerna's renovate package rules (#40029) @DiegoAndai
- &#8203;<!-- 34 -->Downgrade lerna to 7.2.0 (#40026) @DiegoAndai
- &#8203;<!-- 32 -->Rename OpenCollective @oliviertassinari
- &#8203;<!-- 48 -->[docs-infra] Env variables should be string (#39991) @oliviertassinari
- &#8203;<!-- 10 -->[Portal] Improve docs for container prop (#39180) @oliviertassinari
- &#8203;<!-- 50 -->[website] Update pricing table (#40023) @cherniavskii
- &#8203;<!-- 49 -->[website][docs] Update the product identifier menu and X page (#39832) @danilo-leal
- &#8203;<!-- 07 -->[website] Mark TreeView and Charts as stable (#39975) @flaviendelangle
- &#8203;<!-- 06 -->[website] Update career page (#40015) @oliviertassinari
- &#8203;<!-- 05 -->[website] Sync about dataset @oliviertassinari
- &#8203;<!-- 04 -->[website] Fix 301 redirection to X tree-view @oliviertassinari
- &#8203;<!-- 03 -->[website] Change redirection prefixes @oliviertassinari
- &#8203;<!-- 02 -->[website] Fix 301 link @oliviertassinari
- &#8203;<!-- 01 -->[website] Fix modal not being closed with the escape key on the Base UI page (#39880) @ZeeshanTamboli

All contributors of this release in alphabetical order: @avikalpg, @brijeshb42, @cherniavskii, @danilo-leal, @DarhkVoyd, @dhaub-exelixis, @DiegoAndai, @flaviendelangle, @islandryu, @joserodolfofreitas, @lhilgert9, @mj12albert, @mnajdova, @oliviertassinari, @sadik-malik, @sai6855, @zanivan, @ZeeshanTamboli

## 5.14.18

<!-- generated comparing v5.14.17..master -->

_Nov 14, 2023_

A big thanks to the 14 contributors who made this release possible. Here are some highlights ✨:

- 💫 Introduced new [Stepper](https://mui.com/joy-ui/react-stepper/) component in Joy UI (#39688) @siriwatknp
- other 🐛 bug fixes and 📚 documentation improvements

### `@mui/material@5.14.18`

- &#8203;<!-- 32 -->[Autocomplete] Add `defaultMuiPrevented` to onKeyDown type (#39820) @sai6855
- &#8203;<!-- 31 -->[Autocomplete] Fix React key warning in Next.js (#39795) @mj12albert
- &#8203;<!-- 24 -->[Checkbox] Asterisk placement aligned correctly (#39721) @axelbostrom
- &#8203;<!-- 04 -->[Rating] Fix the hover highlighting for spaced icons (#39775) @ZeeshanTamboli
- &#8203;<!-- 03 -->[TablePagination] Implement `slotProps` pattern for the actions and the select slots (#39353) @anle9650
- &#8203;<!-- 02 -->[TextField] Fix padding on small filled multiline TextField (#39769) @mj12albert

### `@mui/joy@5.0.0-beta.15`

- &#8203;<!-- 11 -->[Stepper] Add new `Stepper` component (#39688) @siriwatknp
- &#8203;<!-- 12 -->[Select] Fix displaying placeholder when multiple is true (#39806) @sai6855

### `@mui/material-next@6.0.0-alpha.110`

- &#8203;<!-- 26 -->[ButtonGroup] Copy `ButtonGroup` to material next (#39739) @lhilgert9
- &#8203;<!-- 09 -->[ProgressIndicator] Change `CircularProgress` files to ts (#39791) @lhilgert9
- &#8203;<!-- 08 -->[ProgressIndicator] Change `LinearProgress` files to ts (#39793) @lhilgert9
- &#8203;<!-- 07 -->[ProgressIndicator] Copy `LinearProgress` to material next (#39779) @lhilgert9
- &#8203;<!-- 06 -->[ProgressIndicator] Copy `CircularProgress` to material next (#39780) @lhilgert9
- &#8203;<!-- 05 -->[TextField] Add FormLabel and InputLabel components (#39483) @mj12albert

## Docs

- &#8203;<!-- 30 -->[base-ui][NumberInput] Fix inconsistent demo component names (#39786) @mnajdova
- &#8203;<!-- 29 -->[base-ui][Menu] Refine demos (#39823) @zanivan
- &#8203;<!-- 28 -->[base-ui][Switch] Refine demos (#39822) @zanivan
- &#8203;<!-- 16 -->[joy-ui] Fix API generation for Grid (#39861) @oliviertassinari
- &#8203;<!-- 15 -->[joy-ui] Fix menu in color inversion header demo (#39785) @sai6855
- &#8203;<!-- 14 -->[joy-ui] Change the design kit link on the Overview page (#39725) @danilo-leal
- &#8203;<!-- 13 -->[joy-ui] Add `CssBaseline` to integration with Material UI page (#39790) @swillianc
- &#8203;<!-- 10 -->[joy-ui][Tabs] Add wordBreak style to demo (#39821) @sai6855

## Core

- &#8203;<!-- 27 -->[blog] MUI X late v6 blog post (#39700) @joserodolfofreitas
- &#8203;<!-- 25 -->[CHANGELOG] Correct the Joy UI version in the changelog (#39788) @michaldudak
- &#8203;<!-- 23 -->[core] Remove legacy docs files (#39860) @oliviertassinari
- &#8203;<!-- 22 -->[core] Fix GitHub title tag consistency @oliviertassinari
- &#8203;<!-- 21 -->[core] Make the API docs builder configurable per project (#39772) @michaldudak
- &#8203;<!-- 20 -->[docs] Fix the default theme viewer font family (#39782) @danilo-leal
- &#8203;<!-- 19 -->[docs-infra] Fix hydration api (#39706) @oliviertassinari
- &#8203;<!-- 18 -->[docs-infra] Adjust the website & docs footer (#39810) @danilo-leal
- &#8203;<!-- 17 -->[docs-infra] Fix bug on API prop copy experience (#39707) @oliviertassinari
- &#8203;<!-- 01 -->[website] Change roadmap link destination (#39639) @danilo-leal

All contributors of this release in alphabetical order: @anle9650, @axelbostrom, @danilo-leal, @joserodolfofreitas, @lhilgert9, @michaldudak, @mj12albert, @mnajdova, @oliviertassinari, @sai6855, @siriwatknp, @swillianc, @zanivan, @ZeeshanTamboli

## 5.14.17

<!-- generated comparing v5.14.16..master -->

_Nov 6, 2023_

A big thanks to the 12 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.14.17`

- [Dialog] Should not close until the IME is cancelled (#39713) @megos
- [InputBase] Add `sx` type to `input` and `root` slot (#39569) @sai6855

### `@mui/joy@5.0.0-beta.14`

- [ModalDialog] Remove redundant code (#39719) @sai6855
- [ToggleButtonGroup] Fix toggling button state when `Button` is not immediate children (#39571) @sai6855

### `@mui/base@5.0.0-beta.23`

- Make list components more reliable (#39380) @michaldudak

### `@mui/material-next@6.0.0-alpha.109`

- [InputBase] InputBase slotProps accepts sx type (#39714) @mj12albert
- [OutlinedInput] Copy v5 OutlinedInput (#39698) @mj12albert

### `@mui/lab@5.0.0-alpha.152`

- [TreeView] Remove tree view import from @mui/lab (#39685) @alexfauquette

### Docs

- Update Taiwan country name in demos (#39729) @chiahao
- Update release doc for unchanged packages (#39487) @brijeshb42
- [joy-ui] Make code readable to set next color in color inversion demos (#39669) @ZeeshanTamboli
- [material-ui] Remove numeric input workaround from TextField docs (#39629) @mj12albert
- [material-ui] Add comment about code to be removed from Drawer demo (#39678) @samuelsycamore

### Core

- [docs-infra] Fix path bloat client-side (#39708) @oliviertassinari
- [docs-infra] Render footer in SSR (#39710) @oliviertassinari
- [docs-infra] Simplify footer (#39709) @oliviertassinari
- [docs-infra] Fix dark theme color (#39720) @alexfauquette
- [docs-infra] Remove the design feedback alert (#39691) @danilo-leal
- [docs-infra] Bring back scroll gradient in API page with table (#39689) @alexfauquette
- [docs-infra] Clarify the content of the ads @oliviertassinari
- [docs-infra] Link to ScaffoldHub v2 @oliviertassinari
- [docs-infra] Improve access to the component demos via the API page (#39690) @danilo-leal
- [docs-infra] Add appropriate aria-label to docs buttons (#39638) @danilo-leal
- [docs-infra] Fix crawler on API pages (#39490) @alexfauquette
- [docs–infra] Small polish on API toggle (#39704) @oliviertassinari
- [core] Speed up the CI by removing the second build (#39684) @michaldudak
- [core][docs] Fix broken MUI System link in README.md (#39734) @samuelsycamore
- [website] List benefits for sponsors (#39640) @oliviertassinari
- [website] Add Vadym teamMember card to 'About' (#39701) @hasdfa
- [test] Fix flaky screenshot (#39711) @oliviertassinari

All contributors of this release in alphabetical order: @alexfauquette, @brijeshb42, @chiahao, @danilo-leal, @hasdfa, @megos, @michaldudak, @mj12albert, @oliviertassinari, @sai6855, @samuelsycamore, @ZeeshanTamboli

## 5.14.16

<!-- generated comparing v5.14.15..master -->

_Oct 31, 2023_

A big thanks to the 19 contributors who made this release possible. Here are some highlights ✨:

- ✨ New highly requested Joy UI component: [Snackbar](https://mui.com/joy-ui/react-snackbar) (#38801) @ZeeshanTamboli

### `@mui/material@5.14.16`

- &#8203;<!-- 03 -->Fix ownerstate being propagated to DOM node when using styled-components v6 (#39586) @mnajdova

### `@mui/base@5.0.0-beta.22`

- &#8203;<!-- 28 -->[Autocomplete] Standardize box shadow on demos (#39519) @zanivan
- &#8203;<!-- 27 -->[useSelect] Support browser autofill (#39595) @DiegoAndai
- &#8203;<!-- 30 -->[base-ui] Fix mergeSlotProps className join order (#39616) @mj12albert

### `@mui/joy@5.0.0-beta.13`

- &#8203;<!-- 29 -->[Accordion] Add type button to accordion
- &#8203;<!-- 28 -->[Card] Fix CardOverflow in nested cards (#39668) @siriwatknp summary (#39532) @Popppins
- &#8203;<!-- 08 -->[Menu] Fix closing of listbox in `MenuList` demo (#39459) @sai6855
- &#8203;<!-- 07 -->[Progress] Revamp Linear and Circular progress variants (#39492) @zanivan
- &#8203;<!-- 06 -->[Select] Support selection of `multiple` options (#39454) @sai6855
- &#8203;<!-- 05 -->[Textarea] Add ref usage instructions (#39615) @danilo-leal
- &#8203;<!-- 10 --> Fix sticky hover media query issue on mobile (#37467) @gitstart
- &#8203;<!-- 09 --> Add Snackbar component (#38801) @ZeeshanTamboli

### `@mui/material-next@6.0.0-alpha.108`

- &#8203;<!-- 04 -->[theme] Update Material You typescale tokens (#39514) @mj12albert

### Docs

- &#8203;<!-- 22 -->Fix 301 link to Primer design system @oliviertassinari
- &#8203;<!-- 19 -->[joy-ui] Revise the CSS vars page (#39335) @danilo-leal
- &#8203;<!-- 18 -->[joy-ui] Add docs for changing styles based on states (#39597) @siriwatknp
- &#8203;<!-- 17 -->[joy-ui] Fix wrong messages (#39602) @siriwatknp
- &#8203;<!-- 16 -->[material-ui] Include link to bundler how-to for Styled Components users (#39620) @jcoyle37
- &#8203;<!-- 15 -->[material-ui] Improve Dialog demos (#39642) @ZeeshanTamboli
- &#8203;<!-- 14 -->[material-ui] Add stray design fine-tuning to the example collection (#39581) @danilo-leal
- &#8203;<!-- 13 -->[system] Clean up `@mui/styles` docs and discourage users from installing it (#39644) @samuelsycamore
- &#8203;<!-- 12 -->[examples] Upgrade Remix to v2 (#39229) @Nkzn
- &#8203;<!-- 11 -->[examples][material-ui] Remove hardcoded `color="black"` from Next.js App Router layout (#39577) @samuelsycamore

### Core

- &#8203;<!-- 26 -->[core] Setup vale for enforcing style guides (#39633) @alexfauquette
- &#8203;<!-- 25 -->[core] Remove unused use client (#38967) @oliviertassinari
- &#8203;<!-- 24 -->[core] Remove duplicate export (#39346) @oliviertassinari
- &#8203;<!-- 23 -->[core] Remove not used `@types/loader-utils` package from `zero-next-plugin` (#39609) @ZeeshanTamboli
- &#8203;<!-- 21 -->[docs-infra] Add meta charset in codesandbox examples (#39424) @Janpot
- &#8203;<!-- 20 -->[docs-infra] Fix settings drawer accessibility issues (#39589) @emamoah
- &#8203;<!-- 02 -->[website] Add stray adjustments and clean-ups (#39673) @danilo-leal
- &#8203;<!-- 01 -->[website] Open the `Design Engineer - xGrid` role (#39375) @DanailH

All contributors of this release in alphabetical order: @alexfauquette, @Best-Sardar, @DanailH, @danilo-leal, @DiegoAndai, @emamoah, @gitstart, @Janpot, @jcoyle37, @mj12albert, @mnajdova, @Nkzn, @oliviertassinari, @Popppins, @sai6855, @samuelsycamore, @siriwatknp, @zanivan, @ZeeshanTamboli

## 5.14.15

<!-- generated comparing v5.14.14..master -->

_Oct 24, 2023_

A big thanks to the 17 contributors who made this release possible.

### `@mui/material@5.14.15`

- &#8203;<!-- 24 -->[Checkbox][Radio] Fix theme style overrides not working for different sizes (#39377) @gitstart
- &#8203;<!-- 12 -->[InputLabel] InputLabel supports ownerState.focused for styleOverrides (#39470) @mj12albert
- &#8203;<!-- 07 -->[ToggleButton] Add `fullWidth` to `toggleButtonClasses` and `toggleButtonGroupClasses` (#39536) @Semigradsky

### `@mui/base@5.0.0-beta.21`

- &#8203;<!-- 29 -->[useAutocomplete] Correct keyboard navigation with multiple disabled options (#38788) @VadimZvf
- &#8203;<!-- 28 -->[Select] Standardize box shadow on demos (#39509) @zanivan
- &#8203;<!-- 27 -->[Slider] Refine demos (#39526) @zanivan
- &#8203;<!-- 34 -->[Input] Update and port additional tests from material-ui (#39584) @mj12albert

### `@mui/material-next@6.0.0-alpha.107`

- &#8203;<!-- 16 -->[FilledInput] Add FilledInput component (#39307) @mj12albert
- &#8203;<!-- 13 -->[InputAdornment] Fix unstable_capitalize import (#39510) @DiegoAndai
- &#8203;<!-- 08 -->[Snackbar] copy files to mui-material-next (#39232) @Best-Sardar
- &#8203;<!-- 33 -->[Menu] Use useMenu hook (#38934) @mnajdova

### `@mui/joy@5.0.0-beta.12`

- &#8203;<!-- 26 -->[Button] Fix button size being a decorator (#39529) @siriwatknp
- &#8203;<!-- 25 -->[CardOverflow] Remove conditional CSS to support Next.js App dir (#39101) @siriwatknp
- &#8203;<!-- 11 -->[Link] Apply `userSelect: none` only when it's a button (#39486) @mwskwong

### `@mui/lab@5.0.0-alpha.150`

- &#8203;<!-- 09 -->Update peer dep of @mui/material (#39398) @brijeshb42

### `@pigment-css/react@0.0.1-alpha.0`

- &#8203;<!-- 06 -->Implement typings for public runtime API (#39215) @brijeshb42

### `@mui/zero-vite-plugin@0.0.1-alpha.0`

- &#8203;<!-- 05 -->Modify plugin to transform node_modules (#39517) @brijeshb42

### Docs

- &#8203;<!-- 31 -->[base-ui] Standardize grey palette across demos (#39504) @zanivan
- &#8203;<!-- 30 -->[base-ui] Overall demos design review (#38820) @zanivan
- &#8203;<!-- 19 -->[joy-ui] Adjust the responsiveness of the template card (#39534) @danilo-leal
- &#8203;<!-- 18 -->[material-ui] Typo fixes in overview page (#39540) @Evan151
- &#8203;<!-- 35 -->[material-ui] Add stray design tweaks to the templates collection (#39583) @danilo-leal
- &#8203;<!-- 17 -->[system] Revise the Box page (#39159) @danilo-leal
- &#8203;<!-- 22 -->Fix git diff format @oliviertassinari
- &#8203;<!-- 15 -->[I10n] Add Norwegian (nynorsk) (nn-NO) locale (#39481) @hjalti-lifekeys
- &#8203;<!-- 10 -->[l10n] Fix double space typo in ar-EG @oliviertassinari
- &#8203;<!-- 14 -->[I10n] Additions to Icelandic (is-IS) locale (#39480) @hjalti-lifekeys

### Core

- &#8203;<!-- 23 -->[core] Replace a `useCallback` by `useRef` in useEventCallback (#39078) @romgrk
- &#8203;<!-- 21 -->[docs-infra] Prevent docs crash (#39214) @alexfauquette
- &#8203;<!-- 20 -->[docs-infra] Fix no-op autoprefixer warning (#39385) @oliviertassinari
- &#8203;<!-- 32 -->[docs-infra] Refine the API page design (#39520) @alexfauquette
- &#8203;<!-- 25 -->[docs-infra] Fix cut-off sponsors (#39572) @oliviertassinari
- &#8203;<!-- 04 -->[website] Add missing h1 on page @oliviertassinari
- &#8203;<!-- 03 -->[website] Fix unrecognized prop warning @oliviertassinari
- &#8203;<!-- 02 -->[website] Store Engineer role filled @oliviertassinari
- &#8203;<!-- 01 -->[website] Add stray design adjustments (#39496) @danilo-leal

All contributors of this release in alphabetical order: @alexfauquette, @Best-Sardar, @brijeshb42, @danilo-leal, @DiegoAndai, @Evan151, @gitstart, @hjalti-lifekeys, @mj12albert, @mnajdova, @mwskwong, @oliviertassinari, @romgrk, @Semigradsky, @siriwatknp, @VadimZvf, @zanivan

## 5.14.14

<!-- generated comparing v5.14.13..master -->

_Oct 17, 2023_

A big thanks to the 24 contributors who made this release possible. Here are some highlights ✨:
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.14.14`

- &#8203;<!-- 29 -->[material-ui][AppBar] Support all default palette colors in TypeScript (#39389) @BreakBB
- &#8203;<!-- 28 -->[material-ui][AvatarGroup] Add `renderSurplus` prop (#39283) @uuxxx
- &#8203;<!-- 25 -->[material-ui][Box] Fix system properties has incorrect `Theme` interface when applied directly (#39404) @Semigradsky
- &#8203;<!-- 04 -->[material-ui][Pagination] Update `type` parameter of `getItemAriaLabel` prop (#39390) @Simer13
- &#8203;<!-- 06 -->[material][tab] Show/hide scroll buttons for dynamically added children (#39415) @brijeshb42

### `@mui/base@5.0.0-beta.20`

- &#8203;<!-- 26 -->[base-ui][Menu] Do not reopen the menu after clicking on a trigger in Safari (#39393) @michaldudak

### `@mui/material-next@6.0.0-alpha.106`

- &#8203;<!-- 23 -->[Divider][material-next] Add Divider component (#39179) @Best-Sardar

### `@mui/joy@5.0.0-beta.11`

- &#8203;<!-- 08 -->[joy-ui][List] Add the `marker` prop (#39313) @siriwatknp
- &#8203;<!-- 07 -->[joy-ui][Skeleton] Fix semi-transparent scenario when with surface components and color inversion (#39400) @TheNatkat
- &#8203;<!-- 06 -->[joy-ui][Textarea] Fix focus ring for error state (#39391) @vineetjk

### `@mui/icons-material@5.14.14`

- &#8203;<!-- 09 -->[icons] Fix VoiceChatOutlined showing the wrong icon (#39418) @devuser200

### `@mui/system@5.14.14`

- &#8203;<!-- 03 -->[mui-system][style] bug fix for style value check color in nullable object (#39457) @DarhkVoyd

### `@mui/styled-engine-sc@6.0.0-alpha.2`

- &#8203;<!-- 05 -->[styled-engine-sc] Fix TS issues because of missing types (#39395) @mnajdova

### Docs

- &#8203;<!-- 27 -->[docs][base-ui] Renaming demos to BaseXxx (#39104) @christophermorin
- &#8203;<!-- 26 -->[docs] Accessibility in Base UI (#39264) @michaldudak
- &#8203;<!-- 22 -->[docs] Fix 301 redirection @oliviertassinari
- &#8203;<!-- 21 -->[docs] Improve Base UI table of contents for APIs (#39412) @ZeeshanTamboli
- &#8203;<!-- 20 -->[docs] Adjust design kits-related content (#39367) @danilo-leal
- &#8203;<!-- 19 -->[docs] Revise the Contributing Guide (#39190) @samuelsycamore
- &#8203;<!-- 12 -->[docs][joy-ui] Fix row hover prop name in the Table page (#39431) @adrienbrault
- &#8203;<!-- 11 -->[docs][joy-ui] Fix color inversion demos (#39403) @danilo-leal
- &#8203;<!-- 10 -->[docs][material-ui] Remove irrelevant TODO from Snackbar demo (#39396) @ZeeshanTamboli
- &#8203;<!-- 06 -->[docs][material-ui][Table] Bug in "Sorting & Selecting" demo (#39426) @codewithrabeeh
- &#8203;<!-- 05 -->[docs][joy-ui][typography] Update docs after lineHeight changes (#39366) @zanivan

### Core

- &#8203;<!-- 24 -->[core] Fix multiple typos across the repo (#39422) @parikshitadhikari
- &#8203;<!-- 18 -->[docs-infra] Add refinements to the API content design (#39425) @danilo-leal
- &#8203;<!-- 17 -->[docs-infra] Add a min height to the layout component (#39416) @danilo-leal
- &#8203;<!-- 16 -->[docs-infra] Prevent horizontal scroll in the TOC (#39417) @danilo-leal
- &#8203;<!-- 15 -->[docs-infra] Add a collapsible list & table views to the API content display (#38265) @alexfauquette
- &#8203;<!-- 14 -->[docs-infra] Adjust the `kbd` tag styles (#39397) @danilo-leal
- &#8203;<!-- 13 -->[docs-infra] Fix strong style regression (#39384) @oliviertassinari
- &#8203;<!-- 04 -->[website] Add the LinkedIn profile to the contributors section on the About page (#39455) @chhawinder
- &#8203;<!-- 03 -->[website] Update new role template (#39386) @oliviertassinari
- &#8203;<!-- 02 -->[website] Add stray design fine-tunning to the Pricing page (#39472) @danilo-leal
- &#8203;<!-- 01 -->[website] Fix career anchor link to perks & benefits @oliviertassinari

All contributors of this release in alphabetical order: @adrienbrault, @alexfauquette, @Best-Sardar, @BreakBB, @brijeshb42, @chhawinder, @christophermorin, @codewithrabeeh, @danilo-leal, @DarhkVoyd, @devuser200, @michaldudak, @mnajdova, @oliviertassinari, @parikshitadhikari, @samuelsycamore, @Semigradsky, @Simer13, @siriwatknp, @TheNatkat, @uuxxx, @vineetjk, @zanivan, @ZeeshanTamboli

## 5.14.13

<!-- generated comparing v5.14.12..master -->

_Oct 10, 2023_

A big thanks to the 22 contributors who made this release possible. Here are some highlights ✨:

- 🚀 Added support for `styled-components` v6 (#39042) @mnajdova

### `@mui/material@5.14.13`

- [Checkbox] Fix checkbox hover bg with extendTheme (#39319) @brijeshb42
- [Chip] Outlined Chip variant is wider than the Filled counterpart (#39342) @chirag3003
- [Select] Add notice about select's a11y improvement on v5.14.12 changelog (#39310) @DiegoAndai
- [Typography] Color prop check for primitive type (#39071) @DarhkVoyd
- [Pagination] Fix background color on hover and keyboard focus when using CSS theme variables (#39220) @ValkonX33
- [Popper] Add missing `styleOverrides` Popper type in theme (#39154) @axelbostrom
- [Slider] Support all default palette colors in TypeScript (#39058) @gugudwt

### `@mui/base@5.0.0-beta.19`

- [Menu] Add the anchor prop (#39297) @michaldudak

### `@mui/material-next@6.0.0-alpha.105`

- [Menu] Copy v5 Menu components (#39301) @mnajdova

### `@mui/joy@5.0.0-beta.10`

- [Autocomplete] Add `type=button` to clear button (#39263) @brijeshb42
- [Button] Fix the text wrap issue (#38696) @atharva3333
- [Drawer] Apply color inversion to content slot instead (#39312) @siriwatknp
- [Switch] Fix missing class name (#39327) @Bestwebdesign

### `@mui/styled-engine-sc@6.0.0-alpha.1`

- &#8203;<!-- 03 -->[system] Add support for `styled-components` v6 (#39042) @mnajdova

### Docs

- [joy-ui] Adjust the templates page card design (#39369) @danilo-leal
- Rename the Data Grid "Quick filter" to "Search" (#37724) @alexfauquette
- Remove obsolete translations (#39221) @mbrookes
- Update link to add custom color in palette (#39359) @ZeeshanTamboli
- Denser code demo @oliviertassinari
- Set up MD3 experiments pages (#39323) @mj12albert
- [Drawer] Fix right anchored persistent drawer intercepts click when it is closed (#39318) @ZeeshanTamboli
- [joy-ui] Revise the Color Inversion page (#39306) @danilo-leal
- [joy-ui] Remove redundant `error` prop from input validation demo (#39280) @sai6855
- [material-ui] Rename themed components doc, fix typos (#39368) @samuelsycamore
- [material-ui] Adjust the Material You Chip section (#39325) @danilo-leal
- [system] Add documentation on how to augment custom theme types for the `sx` prop callback (#39259) @3xp10it3r
- [joy-ui][Input] Add debounce input demo (#39300) @sai6855

### Core

- [docs-infra] Improve the open diamond sponsor spot callout (#39332) @danilo-leal
- [docs-infra] Fix Code Sandbox download issue (#39317) @ARJ2160
- [docs-infra] Remove overflow: hidden for demo gradient bg (#39225) @oliviertassinari
- [website] Fix footer responsiveness (#39355) @danilo-leal
- [website] Host Figma redirections in the store for now @oliviertassinari

All contributors of this release in alphabetical order: @3xp10it3r, @alexfauquette, @ARJ2160, @atharva3333, @axelbostrom, @Bestwebdesign, @brijeshb42, @chirag3003, @danilo-leal, @DarhkVoyd, @DiegoAndai, @gugudwt, @mbrookes, @michaldudak, @mj12albert, @mnajdova, @oliviertassinari, @sai6855, @samuelsycamore, @siriwatknp, @ValkonX33, @ZeeshanTamboli

## 5.14.12

<!-- generated comparing v5.14.11..master -->

_Oct 3, 2023_

A big thanks to the 17 contributors who made this release possible. Here are some highlights ✨:

- 🎨 Introduced color inversion utilities to Joy UI (#38916) @siriwatknp
- 🚀 Added Chip and related TextField components to Material You @DiegoAndai, @mj12albert
- 🏗️ Improve the Select's component a11y by adding the combobox role and aria-controls attribute (#38785) @xulingzhihou. If your tests require selecting the trigger element by the "button" role, then you'll have to change it to use the "combobox" role instead

### `@mui/material@5.14.12`

- [DialogActions] Apply margin-left when children is not of `button` type (#39189) @sai6855
- [Select] Improve a11y by adding combobox role and aria-controls attribute (#38785) @xulingzhihou
- [Select] Fix MenuProps slotProps forwarding (#39177) @DiegoAndai
- [TextField] Polish types in Textfield demo (#39140) @sai6855
- [ButtonGroup] Fix rendering with conditional elements (#38989) @ZeeshanTamboli

### `@mui/system@5.14.12`

- [system] Add support for `variants` in the styled() util (#39073) @mnajdova
- [Box] Add missing logical spacing property types (#39169) @Semigradsky

### `@mui/base@5.0.0-beta.18`

- [useSlider] Align externalProps handling (#38854) @mj12albert
- [useTabs] Align external props handling for useTab/useTabPanel/useTabsList (#39037) @mj12albert
- [test] Fix import paths in useTab tests (#39291) @mj12albert

### `@mui/material-next@6.0.0-alpha.104`

- [Chip] Add Material You Chip component (#38927) @DiegoAndai
- [Divider] Copy v5 Divider (#39197) @mj12albert
- [FilledInput] Copy v5 FilledInput (#39040) @mj12albert
- [FormControl] Add FormControl component (#39032) @mj12albert
- [Select] Copy Select files from v5 (#39188) @DiegoAndai
- [TextField] Copy v5 TextField's inner components (#39166) @mj12albert

### `@mui/joy@5.0.0-beta.9`

- Introduce color inversion utilities (#38916) @siriwatknp
- Replace margin with `gap` property (#39147) @siriwatknp
- [CssBaseline] use Joy `GlobalStyles` (#39278) @siriwatknp
- [Drawer] Apply content styles from theme to content slot (#39199) @sai6855
- [List] Add gap and missing active styles (#39146) @siriwatknp
- [Switch] Slight adjustments to the design (#39276) @danilo-leal

### Docs

- [docs] Update Autocomplete demo for React 18 (#39162) @oliviertassinari
- [docs-infra] Tweak feedback footer section design (#36556) @danilo-leal
- [docs-infra] Improve code syntax highlight (#39181) @oliviertassinari
- [docs][base] Add Tailwind CSS + plain CSS demo on the TextArea page (#39046) @alisasanib
- [docs][base-ui] Fix style for root div of multiline input (#39182) @ttlpta
- [docs][base-ui] Improve Select's country select demo (#38983) @oliviertassinari
- [docs][joy-ui] Add scrollable tabs example (#39260) @siriwatknp
- [docs][joy-ui] Match `Autocomplete` github label demo to actual github label dropdown (#39228) @sai6855
- [docs][joy-ui] Refine the Rental dashboard template (#39059) @zanivan
- [docs][joy-ui] Removed incomplete sentence in the Aspect Ratio page (#39227) @Erik-McKelvey
- [docs][joy-ui] Fix typo in the Accordion page (#39226) @Erik-McKelvey
- [docs][joy-ui] Update and standardize template Sidemenus (#39271) @zanivan
- [docs][joy-ui] Add a roadmap page (#39163) @danilo-leal
- [docs][material-ui] Replace `Box` with `Stack` in applicable demos (#39174) @sai6855
- [docs][material-ui] Add small polish to the Templates page (#39224) @danilo-leal
- [docs][material-ui] Small revision to the Icons page (#38840) @danilo-leal

### Core

- Add next lint config to eslint (#39183) @Janpot
- [core] Update eslint rules (#39178) @romgrk
- [core] Fix Greg GitHub slug @oliviertassinari
- [core] Priority Support casing normalization @oliviertassinari
- [website] Add Heatmap in pricing page (#39269) @oliviertassinari
- [website] Update `React Engineer - xCharts` Ashby link (#39172) @DanailH
- [website] Add Charts to the pricing table (#38680) @alexfauquette
- [website] Polish career experience @oliviertassinari
- [website] Simplify the Core products file (#39194) @danilo-leal

All contributors of this release in alphabetical order: @alexfauquette, @brijeshb42, @DanailH, @danilo-leal, @DiegoAndai, @Erik-McKelvey, @Janpot, @mj12albert, @mnajdova, @oliviertassinari, @romgrk, @sai6855, @Semigradsky, @siriwatknp, @xulingzhihou, @zanivan, @ZeeshanTamboli

## 5.14.11

<!-- generated comparing v5.14.10..master -->

_Sep 26, 2023_

A big thanks to the 23 contributors who made this release possible.
This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.14.11`

- [Autocomplete] Re-export `AutocompleteValue` to make it available from path import (#38638) @vadimka123
- [Select][material-ui] Missing aria-multiselectable attribute on multiple Select component (#38855) @gitstart
- [l10n] labelDisplayedRows is added for trTR localization (#39056) @tebersefa

### `@mui/utils@5.14.11`

- Support RSC in `isMuiElement` util (#38129) @sai6855

### `@mui/base@5.0.0-beta.17`

- [NumberInput] Support adornments (#38900) @anle9650
- [Menu] Align external props handling for useMenu/MenuButton/MenuItem (#38946) @mj12albert
- [Select] Align external props handling (#39038) @mj12albert
- [TextareaAutosize] Simplify logic and add test (#38728) @oliviertassinari

### `@mui/joy@5.0.0-beta.8`

- [Button] Fix disabled button styling when component prop is provided (#38996) @sai6855
- [Drawer] Add missing `JoyDrawer` in theme components (#39074) @Studio384

### `@mui/material-next@6.0.0-alpha.103`

- [FormControl] Copy v5 FormControl (#39039) @mj12albert

### `@mui/lab@5.0.0-alpha.146`

- [TreeView] Fix JSDoc comments in TreeView and TreeItem (#38874) @jergason

### Docs

- Improve focus trap demo (#38985) @oliviertassinari
- Add Tailwind CSS + plain CSS demo on the Tabs page (#39000) @alisasanib
- Improve the default theme viewer design (#39049) @danilo-leal
- Add live demo with CssVarsProvider (#38792) @oliviertassinari
- Fix wrong hash on Card's page (#39151) @mnajdova
- Revise the Drawer page (#38988) @danilo-leal
- Simplify the button's loading indicator demo (#39082) @danilo-leal
- Fix the Templates link on the Overview page (#39086) @danilo-leal
- Refine the Sign in template (#38942) @zanivan
- Add `use-count-up` integration with the Circular Progress (#38952) @anon-phantom

### Core

- [blog] Add a company values blog post (#38802) @mikailaread
- [core] Downgrade lerna to 7.2.0 (#39149) @michaldudak
- [core] Simplify docs feedback interaction (#39075) @alexfauquette
- [core] Improve ref type definition (#38903) @oliviertassinari
- [core] Simplify career (#39112) @oliviertassinari
- [core] Update Babel types along with source packages (#39070) @michaldudak
- [core] Add a comment to explain `useEnhancedEffect` (#39035) @Janpot
- [docs-infra] Fix code removal in table of content (#39165) @alexfauquette
- [docs-infra] Improve callouts design (#39084) @danilo-leal
- [docs-infra] Fix key warning in Base UI Slider slots section (#38954) @ZeeshanTamboli
- [docs-infra] Fix error when redirecting to the root page (#38451) @maheshguntur
- [docs-infra] Open demo crash in the right repository (#39006) @oliviertassinari
- [test] Split the test package (#39061) @michaldudak
- [website] React Engineer - xCharts role (#38976) @DanailH
- [website] Improve the highlighter component colors (#39087) @danilo-leal
- [website] Fix Pricing page row hover (#39097) @danilo-leal
- [website] Fix typo with straight quote @oliviertassinari
- [website] Sync about page @oliviertassinari
- [website] Update the about page (#38733) @danilo-leal
- [website] Small fixes on the X marketing page (#38975) @flaviendelangle
- [website] Add stray design tweaks to the X page (#38589) @danilo-leal

All contributors of this release in alphabetical order: @alexfauquette, @alisasanib, @anle9650, @anon-phantom, @DanailH, @danilo-leal, @DiegoAndai, @flaviendelangle, @gitstart, @Janpot, @jergason, @maheshguntur, @michaldudak, @mikailaread, @mj12albert, @mnajdova, @oliviertassinari, @sai6855, @Studio384, @tebersefa, @vadimka123, @zanivan, @ZeeshanTamboli

## 5.14.10

<!-- generated comparing v5.14.9..master -->

_Sep 18, 2023_

A big thanks to the 16 contributors who made this release possible. This release was mostly about 🐛 bug fixes and 📚 documentation improvements.

### `@mui/material@5.14.10`

- &#8203;<!-- 20 -->[Chip] Add cursor CSS property reset (#38984) @DiegoAndai

### `@mui/utils@5.14.10`

- &#8203;<!-- 05 -->[utils] Move @types/prop-types back to dependencies (#39030) @Methuselah96

### `@mui/base@5.0.0-beta.16`

- &#8203;<!-- 24 -->[NumberInput][base-ui] Warn when changing control mode with `useControlled` (#38757) @sai6855
- &#8203;<!-- 23 -->[Select][base-ui] Fix Select button layout shift, add placeholder prop (#38796) @mj12albert
- &#8203;<!-- 22 -->[useList][base-ui] Accept arbitrary external props and forward to root (#38848) @mj12albert
- &#8203;<!-- 25 -->[Autocomplete][base-ui] Added ref to getInputProps return value (#38919) @DarhkVoyd

### `@mui/joy@5.0.0-beta.7`

- &#8203;<!-- 26 -->[AccordionGroup][joy-ui] Fix console warning when using custom color (#38950) @sai6855
- &#8203;<!-- 07 -->[GlobalStyles][joy-ui] Ensure compatibility with RSC (#38955) @mateuseap

### Docs

- &#8203;<!-- 21 -->[docs][base] Add Tailwind CSS + plain CSS demo on the NumberInput page (#38928) @alisasanib
- &#8203;<!-- 13 -->[docs][Dialog] Add non-modal dialog docs & demo (#38684) @mnajdova
- &#8203;<!-- 12 -->[docs] Fix number input wrong demo @oliviertassinari
- &#8203;<!-- 11 -->[docs] Exclude joy-ui LinearProgressCountup from visual regression (#38969) @siriwatknp
- &#8203;<!-- 09 -->[docs][joy-ui] Revise the Overview page (#38842) @danilo-leal
- &#8203;<!-- 08 -->[docs][material-ui][Pagination] Add `TablePagination` to the API components list (#38486) @MonstraG

### Core

- &#8203;<!-- 19 -->[core] Add more context about useEventCallback @oliviertassinari
- &#8203;<!-- 18 -->[core] Allow deeper import of @mui/utils (#38806) @oliviertassinari
- &#8203;<!-- 17 -->[core] Remove react-dom from @mui/utils peerDependencies (#38974) @michaldudak
- &#8203;<!-- 16 -->[core] Remove react from styled-engine dependencies (#38971) @michaldudak
- &#8203;<!-- 15 -->[core] Fix image loading bug on Safari @oliviertassinari
- &#8203;<!-- 14 -->[core] Fix bundle size upload to S3 job (#38956) @Janpot
- &#8203;<!-- 20 -->[core] Move eslint to peer dependencies of eslint-plugin-material-ui (#39033) @michaldudak
- &#8203;<!-- 10 -->[docs-infra] Display markdown lists correctly in docs for props description (#38973) @ZeeshanTamboli
- &#8203;<!-- 04 -->[website] Improve lighthouse score (#39011) @oliviertassinari
- &#8203;<!-- 03 -->[website] Fix lighthouse issues @oliviertassinari
- &#8203;<!-- 02 -->[website] Create the `InfoCard` component (#38987) @danilo-leal
- &#8203;<!-- 01 -->[website] Small tweaks for performance @oliviertassinari
- &#8203;<!-- 06 -->[zero][next] Setup nextjs plugin package (#38852) @brijeshb42

All contributors of this release in alphabetical order: @alisasanib, @brijeshb42, @danilo-leal, @DarhkVoyd, @DiegoAndai, @Janpot, @mateuseap, @Methuselah96, @michaldudak, @mj12albert, @mnajdova, @MonstraG, @oliviertassinari, @sai6855, @siriwatknp, @ZeeshanTamboli

## 5.14.9

<!-- generated comparing v5.14.8..master -->

_Sep 13, 2023_

A big thanks to the 18 contributors who made this release possible. Here are some highlights ✨:

- 🎉 Added the [`Drawer` component](https://mui.com/joy-ui/react-drawer/) to Joy UI (#38169) @mnajdova
- ✨ Material UI's [`ButtonGroup` component](https://mui.com/material-ui/react-button-group/) now styles button elements within it correctly (#38520) @ZeeshanTamboli

### `@mui/material@5.14.9`

- &#8203;<!-- 44 -->[ButtonGroup] Determine first, last and middle buttons to support different elements with correct styling (#38520) @ZeeshanTamboli
- &#8203;<!-- 07 -->[Modal] Fix console warning when onTransitionEnter , onTransitionExit provided (#38868) @sai6855
- &#8203;<!-- 54 -->Revert "[Autocomplete] Type multiple values with readonly arrays." (#38827) @mnajdova
- &#8203;<!-- 57 -->[Tabs] Scrollable tabs shouldn't crash when customizing their styles in the theme with slot callbacks (#38544) @brentertz
- &#8203;<!-- 59 -->[AlertTitle][BreadCrumbs] Fix inheritance message in docs (#38876) @sai6855

### `@mui/base@5.0.0-beta.15`

- &#8203;<!-- 63 -->[useSnackbar] Align externalProps handling (#38935) @mj12albert
- &#8203;<!-- 48 -->[useInput] Align ExternalProps naming (#38849) @mj12albert
- &#8203;<!-- 13 -->[FocusTrap] Refactor & cleanup (#38878) @mnajdova
- &#8203;<!-- 12 -->[FocusTrap] Fix `disableEnforceFocus` behavior (#38816) @mnajdova
- &#8203;<!-- 06 -->[Switch] Simplify source (#38910) @oliviertassinari

### `@mui/joy@5.0.0-beta.6`

- &#8203;<!-- 15 -->[Drawer] Add Drawer component (#38169) @mnajdova
- &#8203;<!-- 11 -->Reduce height of some variants (#38527) @zanivan
- &#8203;<!-- 10 -->Refine the default theme color palette (#38416) @zanivan
- &#8203;<!-- 34 -->[Dialog] Add `DialogActions`, `DialogTitle` and `DialogContent` (#38382) @siriwatknp
- &#8203;<!-- 60 -->[AccordionGroup] Add missing `variant` and `color` classes (#38814) @sai6855

### `@mui/lab@5.0.0-alpha.144`

- &#8203;<!-- 09 -->Add TypeScript deprecations (#38833) @oliviertassinari
- &#8203;<!-- 08 -->Fix `@mui/x-tree-view` dependency (#38822) @flaviendelangle

### `@mui/system@5.14.9`

- &#8203;<!-- 05 -->Remove dead code (#38884) @oliviertassinari
- &#8203;<!-- 04 -->Remove getInitColorSchemeScript leading spaces (#38794) @oliviertassinari

### `@mui/zero-vite-plugin@0.0.1-alpha.0`

- &#8203;<!-- 02 -->[vite] Create a package for vite plugin (#38685) @brijeshb42

### Docs

- &#8203;<!-- 53 -->[docs][base-ui] Improve recommended usage guide (#38570) @oliviertassinari
- &#8203;<!-- 52 -->[docs][base-ui] Create hooks contribution guide (#38679) @michaldudak
- &#8203;<!-- 51 -->[docs][base-ui] Structure and style revisions for Component docs (#38826) @samuelsycamore
- &#8203;<!-- 50 -->[docs][base-ui] Add Number Input to the all components page (#38839) @danilo-leal
- &#8203;<!-- 49 -->[docs][base-ui] Mark Popup with the Preview tag (#38851) @michaldudak
- &#8203;<!-- 47 -->[blog] Polish component reference name @oliviertassinari
- &#8203;<!-- 46 -->[blog] Fix missing card (#38834) @oliviertassinari
- &#8203;<!-- 45 -->[Button][docs][material-ui] Update the file upload demo (#38823) @danilo-leal
- &#8203;<!-- 33 -->[docs][DialogTitle] Fix props docs doesn't mention it extends `Typography` props (#38856) @sai6855
- &#8203;<!-- 32 -->[docs] Improve npm experience (#38906) @oliviertassinari
- &#8203;<!-- 31 -->[docs] Fix redirection to Base UI URLs @oliviertassinari
- &#8203;<!-- 30 -->[docs] Fix use of callouts (#38747) @oliviertassinari
- &#8203;<!-- 29 -->[docs] Fix 301 links for SEO @oliviertassinari
- &#8203;<!-- 28 -->[docs] Remove flag from installation page @oliviertassinari
- &#8203;<!-- 27 -->[docs] Fix strange break line on mobile in between product name @oliviertassinari
- &#8203;<!-- 26 -->[docs] Clearer npm package homepages (#38864) @oliviertassinari
- &#8203;<!-- 25 -->[docs] enableColorScheme prop was removed (#38795) @oliviertassinari
- &#8203;<!-- 24 -->[docs] Fix a11y issues in tables demos (#38829) @michaldudak
- &#8203;<!-- 62 -->[docs][joy-ui] Refine the Messages template (#38807) @zanivan
- &#8203;<!-- 22 -->[docs][joy-ui] Fix copy on the Tutorial page (#38907) @danilo-leal
- &#8203;<!-- 21 -->[docs][joy-ui] Fix grammar and update Usage section in color inversion page (#38850) @ZeeshanTamboli
- &#8203;<!-- 20 -->[docs][joy-ui] Revise the Lists page (#36324) @LadyBluenotes
- &#8203;<!-- 19 -->[docs][joy-ui] Refine the Profile Dashboard template (#38599) @zanivan
- &#8203;<!-- 18 -->[docs][material-ui] Revise the Paper component docs (#38841) @danilo-leal
- &#8203;<!-- 17 -->[docs][material-ui] Revise the Typography page (#38543) @danilo-leal
- &#8203;<!-- 16 -->[docs][material-ui] Revise and split up "Styled engine" doc (#37774) @samuelsycamore
- &#8203;<!-- 03 -->[TextareaAutosize][docs] Fix component creation in render (#38577) @oliviertassinari

### Examples

- &#8203;<!-- 14 -->[examples] Add shortcut to open example in online IDE (#38572) @oliviertassinari
- &#8203;<!-- 61 -->[examples][base-ui] Add Base UI + Vite + Tailwind CSS example in TypeScript (#37595) @dvkam

### Core

- &#8203;<!-- 65 -->[core] Remove package declaration from same package dependencies (#38951) @DiegoAndai
- &#8203;<!-- 64 -->[core] Remove workspace dependencies from root package.json (#38940) @michaldudak
- &#8203;<!-- 43 -->[core] Fix prop-types generation (#38831) @flaviendelangle
- &#8203;<!-- 42 -->[core] Move types packages to docs' devDependencies (#38914) @michaldudak
- &#8203;<!-- 41 -->[core] Improve DX when browsing the package on npm and GitHub @oliviertassinari
- &#8203;<!-- 40 -->[core] TrapFocus was renamed to FocusTrap @oliviertassinari
- &#8203;<!-- 39 -->[core] Add types extension for clarity @oliviertassinari
- &#8203;<!-- 38 -->[core] Hoist rewriteImportPaths to parent scope @oliviertassinari
- &#8203;<!-- 37 -->[core] Bump aws-cli orb to 4.1 (#38857) @Janpot
- &#8203;<!-- 36 -->[core] Explicitly define package dependencies (#38859) @michaldudak
- &#8203;<!-- 35 -->[core] Fix yarn docs:create-playground script @oliviertassinari
- &#8203;<!-- 56 -->[docs-infra] Improve show code button affordance (#38824) @danilo-leal
- &#8203;<!-- 55 -->[docs–infra] Fix callout container width (#38880) @oliviertassinari
- &#8203;<!-- 23 -->[docs-infra] Catch duplicated trailing splashes in links (#38758) @oliviertassinari
- &#8203;<!-- 01 -->[website] add Michel Engelen to the about us page (#38818) @michelengelen
- &#8203;<!-- 58 -->[website] Add a templates & design kits section to the Material UI page (#38617) @danilo-leal

All contributors of this release in alphabetical order: @brentertz, @brijeshb42, @danilo-leal, @DiegoAndai, @dvkam, @flaviendelangle, @Janpot, @LadyBluenotes, @michaldudak, @michelengelen, @mj12albert, @mnajdova, @oliviertassinari, @sai6855, @samuelsycamore, @siriwatknp, @zanivan, @ZeeshanTamboli

## 5.14.8

<!-- generated comparing v5.14.7..master -->

_Sep 5, 2023_

A big thanks to the 25 contributors who made this release possible.

### `@mui/material@5.14.8`

- &#8203;<!-- 53 -->ImageItemList fix incorrect (below) rendering (#38452) @omriklein
- &#8203;<!-- 42 -->[Button] Add demo for file upload (#38786) @anle9650
- &#8203;<!-- 12 -->[Slider] Add missing classes for `Slider` `InputLabel` `InputBase` `Radio` (#38401) @sai6855
- &#8203;<!-- 11 -->[Select] Merge slotProps.paper with internal Paper props (#38703) @michaldudak
- &#8203;<!-- 09 -->[Tabs] Fix `ref` type (#38717) @ZeeshanTamboli
- &#8203;<!-- 08 -->[TabScrollButton] Extend ButtonBase types (#38719) @ZeeshanTamboli

### `@mui/base@5.0.0-beta.14`

- &#8203;<!-- 50 -->[Autocomplete] Type multiple values with readonly arrays. (#38253) @pcorpet
- &#8203;<!-- 07 -->[TextField] Fix unstable height of memoized multiline TextField component (#37135) @amal-qb

### `@mui/joy@5.0.0-beta.5`

- &#8203;<!-- 53 -->[Accordion] Fix incorrect display of classname (#38695) @sai6855
- &#8203;<!-- 51 -->[AspectRatio] Correct `ratio` prop description (#38743) @sai6855
- &#8203;<!-- 43 -->[Button] Fix disablity of button (#38673) @sai6855
- &#8203;<!-- 35 -->[design] Stray design tweaks to components (#38476) @zanivan
- &#8203;<!-- 05 -->[Typography] Added position only when Skeleton is a direct child (#38799) @siriwatknp

### `@mui/lab@5.0.0-alpha.143`

- &#8203;<!-- 06 -->[TreeView] Use Tree View from MUI X in the lab (#38261) @flaviendelangle
- &#8203;<!-- 13 -->[LoadingButton] Fix HTML rule button > div forbidden nesting (#38584) @oliviertassinari

### `@mui/system@5.14.8`

- &#8203;<!-- 11 -->[system] Fix the inconsistent types of the `mergeBreakpointsInOrder` function (#38749) @imevanc
- &#8203;<!-- 10 -->[system] Fix maxWidth incorrectly resolving breakpoints with non-pixel units (#38633) @mj12albert
- &#8203;<!-- 05 -->[typescript] Introduce \*OwnProps interfaces for components (#36798) @szalonna

### Docs

- &#8203;<!-- 52 -->Update changelog (#38704) @mj12albert
- &#8203;<!-- 49 -->[docs][Autocomplete] Require referentially stable value (#38734) @michaldudak
- &#8203;<!-- 48 -->[docs][base-ui] Add type parameter to the button in prepareForSlot demo (#38640) @michaldudak
- &#8203;<!-- 47 -->[docs][base-ui] Fix the broken image in the Tailwind CSS guide (#38721) @michaldudak
- &#8203;<!-- 46 -->[docs][base-ui]: Working With Tailwind Guide - revises example code to avoid import errors (#38693) @christophermorin
- &#8203;<!-- 45 -->[docs][base] Add Tailwind CSS + plain CSS demo on the Menu page (#38618) @alisasanib
- &#8203;<!-- 44 -->[blog] Clearer blog release title @oliviertassinari
- &#8203;<!-- 43 -->[blog] Add a post for the Tree View migration (#38407) @flaviendelangle
- &#8203;<!-- 34 -->[docs] Fix broken links to Next.js docs (#38764) @ruflair
- &#8203;<!-- 33 -->[docs] Trim trailing whitespace (#38793) @oliviertassinari
- &#8203;<!-- 32 -->[docs] Fix a typo in lab-tree-view-to-mui-x.md @mbrookes
- &#8203;<!-- 31 -->[docs] Clean up not used Usage files (#38715) @danilo-leal
- &#8203;<!-- 30 -->[docs] Improve theme builder exceptions (#38709) @jyash97
- &#8203;<!-- 29 -->[docs] Polish Slider demos (#38759) @oliviertassinari
- &#8203;<!-- 28 -->[docs] Fix Joy UI docs link regression (#38761) @oliviertassinari
- &#8203;<!-- 27 -->[docs] Fix typo @oliviertassinari
- &#8203;<!-- 26 -->[docs] Fix e.g. typo (#38748) @oliviertassinari
- &#8203;<!-- 25 -->[docs] Fix Next.js pages router example redirect link (#38750) @sai6855
- &#8203;<!-- 24 -->[docs] Fix SEO issue broken links @oliviertassinari
- &#8203;<!-- 23 -->[docs] Improve SSR example reference (#38651) @oliviertassinari
- &#8203;<!-- 17 -->[docs][joy-ui] Integrate a count-up feature to the Linear Progress (#38738) @anon-phantom
- &#8203;<!-- 16 -->[docs][joy-ui] Fix Link's `overlay` prop demo (#38702) @danilo-leal
- &#8203;<!-- 15 -->[docs][joy-ui] Polish the Stack page (#38623) @danilo-leal
- &#8203;<!-- 14 -->[docs][material-ui] Adjust simple Slide demo (#38646) @rajgop1

### Core

- &#8203;<!-- 43 -->[core] Re-add nx and setup build caching (#38752) @brijeshb42
- &#8203;<!-- 41 -->[core] Remove dead code seoTitle @oliviertassinari
- &#8203;<!-- 40 -->[core] Use immutable refs (#38762) @oliviertassinari
- &#8203;<!-- 39 -->[core] Rework `typescript-to-proptypes` to share the AST parsing with `parseStyles` (#38517) @flaviendelangle
- &#8203;<!-- 38 -->[core] Fix CI @oliviertassinari
- &#8203;<!-- 37 -->[core] Remove unnecessary `@types/webpack` package (#38720) @ZeeshanTamboli
- &#8203;<!-- 36 -->[core] Remove duplicate prop @oliviertassinari

- &#8203;<!-- 22 -->[docs-infra] Fix mobile display in CodeSandbox (#38767) @oliviertassinari
- &#8203;<!-- 21 -->[docs-infra] Remove legacy GA (#37579) @alexfauquette
- &#8203;<!-- 20 -->[docs-infra] Fix emotion :first-child console log (#38690) @oliviertassinari
- &#8203;<!-- 19 -->[docs-infra] Fix leaking callout content (#38712) @danilo-leal
- &#8203;<!-- 18 -->[docs-infra] Remove emoji from callouts (#38694) @danilo-leal

- &#8203;<!-- 04 -->[website] Fix out of date discount value @oliviertassinari
- &#8203;<!-- 03 -->[website] Fix out-of-date label on Toolpad (#38744) @bharatkashyap
- &#8203;<!-- 02 -->[website] Fine-tune branding buttons box shadows (#38731) @danilo-leal
- &#8203;<!-- 01 -->[website] Fix pricing table style (#38681) @alexfauquette

All contributors of this release in alphabetical order: @alexfauquette, @alisasanib, @amal-qb, @anle9650, @anon-phantom, @bharatkashyap, @brijeshb42, @christophermorin, @danilo-leal, @flaviendelangle, @imevanc, @jyash97, @mbrookes, @michaldudak, @mj12albert, @oliviertassinari, @omriklein, @pcorpet, @rajgop1, @ruflair, @sai6855, @siriwatknp, @szalonna, @zanivan, @ZeeshanTamboli

## 5.14.7

<!-- generated comparing v5.14.6..master -->

_Aug 29, 2023_

A big thanks to the 11 contributors who made this release possible. This release focuses primarily on 🐛 bug fixes, 📚 documentation, and ⚙️ infrastructure improvements.

### `@mui/material@5.14.7`

- [Autocomplete] Fix listbox opened unexpectedly when component is `disabled` (#38611) @mj12albert
- [Select][material-ui] Fix select menu moving on scroll when disableScrollLock is true (#37773) @VishruthR

### `@mui/base@5.0.0-beta.13`

- [useButton][base-ui] Accept arbitrary props in getRootProps and forward them (#38475) @DiegoAndai

### `@pigment-css/react@0.0.1-alpha.1`

- [system][zero][tag] Add support for sx prop (#38535) @brijeshb42

### Docs

- [docs] Number Input docs fixes (#38521) @mj12albert
- [docs] Show all the code in the usage section (#38691) @oliviertassinari
- [docs][joy-ui] Change the customization and how-to guides docs tree (#38396) @danilo-leal
- [docs][lab][LoadingButton] Improve `loading` prop documentation (#38625) @sai6855
- [docs][material-ui] Format `key` prop JSDoc description in `Snackbar` component code correctly (#38603) @jaydenseric

### Core

- [core] Add use-client to custom icons (#38132) @mj12albert
- [core] Remove unnecessary `@types/jsdom` (#38657) @renovate[bot]
- [core] Improve sponsors GA labels (#38649) @oliviertassinari
- [core] Fix ESM issues with regression tests (#37963) @Janpot
- [core] Potential fix for intermittent ci crashes in e2e test (#38614) @Janpot
- [docs-infra] Mark unstable components with a chip in the nav drawer (#38573) @michaldudak
- [docs-infra] Adjust the Material You playground demo design (#38636) @danilo-leal
- [docs-infra] Hide the SkipLink button if user prefers reduced motion (#38632) @DerTimonius
- [website] Add tiny fixes the homepage Sponsors section (#38635) @danilo-leal

All contributors of this release in alphabetical order: @brijeshb42, @danilo-leal, @DerTimonius, @DiegoAndai, @Janpot, @jaydenseric, @mj12albert, @oliviertassinari, @renovate[bot], @sai6855, @VishruthR

## 5.14.6

<!-- generated comparing v5.14.5..master -->

_Aug 23, 2023_

A big thanks to the 21 contributors who made this release possible. Here are some highlights ✨:

- 🚀 Added the [Popup](https://mui.com/base-ui/react-popup/) component to Base UI (#37960) @michaldudak
  It's intended to replace the Popper component, which uses the deprecated Popper JS library. The Popup is built on top of Floating UI and has a similar API to the Popper.
- 🚀 Added the [Accordion](https://mui.com/joy-ui/react-accordion/) component to Joy UI (#38164) @siriwatknp
- 🚀 Added InputBase and ButtonBase components to `material-next` (#38319) @DiegoAndai @mj12albert
- 🔋 First iteration on the zero-runtime styling engine compatible with Server Components (#38378) @brijeshb42

### `@mui/material@5.14.6`

- [Modal] Update it to use the useModal hook (#38498) @mnajdova
- [Select] Add `root` class to `SelectClasses` (#38424) @sai6855
- [Skeleton] Soften the pulse animation (#38506) @oliviertassinari
- [TextField] Fix onClick regressions handling changes (#38474) @mj12albert
- [TextField] Fix TextField onClick test (#38597) @mj12albert

### `@mui/base@5.0.0-beta.12`

- [Popup] New component (#37960) @michaldudak

### `@mui/joy@5.0.0-beta.3`

- [Accordion] Add Joy UI Accordion components (#38164) @siriwatknp
- [Select] Add `required` prop (#38167) @siriwatknp
- Miscellaneous fixes (#38462) @siriwatknp

### `@mui/material-next@6.0.0-alpha.98`

- [ButtonBase] Add ButtonBase component (#38319) @DiegoAndai
- [Input] Add InputBase component (#38392) @mj12albert

### `@pigment-css/react@0.0.1-alpha.0`

- Implementation of styled tag processor for linaria (#38378) @brijeshb42

### Docs

- [blog] Clarify tree view move @oliviertassinari
- [docs] Improve the "Understanding MUI packages" page images (#38619) @danilo-leal
- [docs][base-ui] Revise the structure of the Component docs (#38529) @samuelsycamore
- [docs][base-ui] Fix Menu Hooks demo (#38479) @homerchen19
- [docs][base-ui] Correct the MUI System quickstart example (#38496) @michaldudak
- [docs][base-ui] Add Tailwind & plain CSS demos for Autocomplete page (#38157) @mj12albert
- [docs][base-ui] Add Tailwind CSS + plain CSS demo on the Input page (#38302) @alisasanib
- [docs][base-ui] Add Tailwind CSS + plain CSS demo on the Snackbar, Badge, Switch pages (#38425) @alisasanib
- [docs][base-ui] Add Tailwind CSS + plain CSS demo on the Slider page (#38413) @alisasanib
- [docs][base-ui] Add Tailwind CSS + plain CSS demo on the Select page (#38367) @alisasanib
- [docs][joy-ui] Fix typo: Classname -> Class name for consistency (#38510) @alexfauquette
- [docs][joy-ui] Revise the theme color page (#38402) @danilo-leal
- [docs][joy-ui] Sort templates by popularity (#38490) @oliviertassinari
- [docs][joy-ui] Fix the `fullWidth` prop description for the Input (#38545) @0xturner
- [docs][joy-ui] Updated the List playground demo (#38499) @zanivan
- [docs][joy-ui] Changed bgcolor of the Playground demo (#38502) @zanivan
- [docs][material-ui] Fix key warning in SimpleDialog demo (#38580) @ZeeshanTamboli
- [docs][material-ui] Fixed Google Fonts link for material two-tone icons in CodeSandbox and Stackblitz (#38247) @ZeeshanTamboli
- [docs][material-ui] Fix the Drawer's `onClose` API docs (#38273) @johnmatthiggins
- [docs][material-ui] Improve nav link tab example (#38315) @oliviertassinari
- [docs][material-ui] Fix missing import in the styled engine guide (#38450) @codersjj
- [docs][material-ui][Dialog] Improve screen reader announcement of Customized Dialog (#38592) @ZeeshanTamboli
- [docs] Add 3rd party libraries integration examples for Joy Input (#38541) @siriwatknp
- [docs] Hide translation call to action (#38449) @cristianmacedo
- [docs] Fix codemod name in changelog of v5.14.4 (#38593) @GresilleSiffle
- [docs] More space for theme builder (#38532) @oliviertassinari
- [docs] Fix the math symbol of the width sx prop range @oliviertassinari
- [docs] Fix typo on a11y section of Tabs @oliviertassinari
- [docs] Clarify System peer dependencies @oliviertassinari
- [docs] Fix horizontal scrollbar @oliviertassinari
- [docs] Code style convention @oliviertassinari
- [docs] Fix typo in Base UI @oliviertassinari
- [docs] Update the backers page (#38505) @danilo-leal
- [docs] Add stray design adjustments to the docs (#38501) @danilo-leal
- [docs] Use IBM Plex Sans in Tailwind CSS demos (#38464) @mnajdova
- [docs] Fix SEO issues reported by ahrefs (#38423) @oliviertassinari

### Examples

- [examples] Start to remove Gatsby (#38567) @oliviertassinari
- [examples][joy-ui] Fix Joy UI example CLI (#38531) @oliviertassinari
- [examples][joy-ui] Improve example when using Next Font (#38540) @mwskwong

### Core

- [changelog] Fix issues in highlight @oliviertassinari
- [core] Remove redundant `@material-ui/` aliases from regression test Webpack config (#38574) @ZeeshanTamboli
- [core] Fix CI error @oliviertassinari
- [core] Remove unnecessary Box (#38461) @oliviertassinari
- [core] Set GitHub Action top level permission @oliviertassinari
- [docs-infra][joy-ui] Polish the usage and CSS vars playgrounds (#38600) @danilo-leal
- [docs-infra] Support link title (#38579) @oliviertassinari
- [docs-infra] Fix ad layout shift (#38622) @oliviertassinari
- [docs-infra] Add light tweaks to the ad container (#38504) @danilo-leal
- [docs-infra] Fix anchor scroll without tabs (#38586) @oliviertassinari
- [docs-infra] Retain velocity animation speed (#38470) @oliviertassinari
- [docs-infra] Follow import and CSS token standard (#38508) @oliviertassinari
- [docs-infra] Add icon to callouts (#38525) @alexfauquette
- [docs-infra] Fix the anchor link on headings (#38528) @danilo-leal
- [docs-infra] Cleanup code on demo code block expansion (#38522) @ZeeshanTamboli
- [docs-infra] Improve the heading buttons positioning (#38428) @danilo-leal
- [docs-infra] Customize the blockquote design (#38503) @danilo-leal
- [docs-infra] Improve the alert before a negative feedback (#38500) @danilo-leal
- [docs-infra] Fix GoogleAnalytics missing event for code copy (#38469) @alexfauquette
- [docs-infra] Improve affordance on the code block expansion (#38421) @danilo-leal
- [website] Fine-tune the branding theme buttons (#38588) @danilo-leal
- [website] Improve the Base UI hero section demo (#38585) @danilo-leal
- [website] Add stray design improvements to the Material UI page (#38590) @danilo-leal
- [website] Fix mobile view Material UI page (#38568) @oliviertassinari
- [website] Fix reference to the data grid @oliviertassinari
- [website] Configure Apple Pay @oliviertassinari
- [website] Fix template link on the homepage (#38471) @danilo-leal

All contributors of this release in alphabetical order: @0xturner, @alexfauquette, @alisasanib, @brijeshb42, @codersjj, @cristianmacedo, @danilo-leal, @DiegoAndai, @GresilleSiffle, @homerchen19, @johnmatthiggins, @michaldudak, @mj12albert, @mnajdova, @mwskwong, @oliviertassinari, @sai6855, @samuelsycamore, @siriwatknp, @zanivan, @ZeeshanTamboli

## 5.14.5

<!-- generated comparing v5.14.4..master -->

_Aug 14, 2023_

A big thanks to the 17 contributors who made this release possible. Here are some highlights ✨:

- @mnajdova [made it easier to use third-party components in Base UI slots](https://mui.com/base-ui/getting-started/customization/#overriding-subcomponent-slots) with the introduction of the `prepareForSlot` utility (#38138)

### `@mui/material@5.14.5`

- &#8203;<!-- 04 -->[TextField] Fix to handle `onClick` on root element (#38072) @LukasTy

### `@mui/codemod@5.14.5`

- &#8203;<!-- 31 -->[codemod] Add v5.0.0/tree-view-moved-to-x codemod (#38248) @flaviendelangle

### `@mui/joy@5.0.0-beta.2`

- &#8203;<!-- 07 -->[Input][joy-ui] Fix the `FormHelperText` icon color (#38387) @TheNatkat
- &#8203;<!-- 06 -->[Skeleton][joy-ui] Soften the pulse animation (#38384) @zanivan
- &#8203;<!-- 05 -->[TabPanel][joy-ui] Add `keepMounted` prop (#38293) @decadef20

### `@mui/base@5.0.0-beta.11`

- &#8203;<!-- 30 -->[base-ui] Remove the legacy Extend\* types (#38184) @michaldudak
- &#8203;<!-- 29 -->[base-ui] Add `useModal` hook (#38187) @mnajdova
- &#8203;<!-- 28 -->[base-ui] Add `prepareForSlot` util (#38138) @mnajdova
- &#8203;<!-- 26 -->[useButton][base-ui] Fix tabIndex not being forwarded (#38417) @DiegoAndai
- &#8203;<!-- 25 -->[useButton][base-ui] Fix onFocusVisible not being handled (#38399) @DiegoAndai

### Docs

- &#8203;<!-- 32 -->[blog] Blog post for MUI X mid v6. Date Pickers, Data Grid, and Charts (#38241) @richbustos
- &#8203;<!-- 35 -->[docs][base-ui] Update number input API docs (#38363) @mj12albert
- &#8203;<!-- 29 -->[docs] Improve page transition speed (#38394) @oliviertassinari
- &#8203;<!-- 28 -->[docs] Improve examples (#38398) @oliviertassinari
- &#8203;<!-- 19 -->[docs][docs] Add `FileUpload` demo (#38420) @sai6855
- &#8203;<!-- 18 -->[docs][joy-ui] Refine the Order Dashboard template design (#38395) @zanivan
- &#8203;<!-- 17 -->[docs][material-ui][joy-ui] Simplify the Quickstart section on the Usage page (#38385) @danilo-leal
- &#8203;<!-- 16 -->[docs][Menu][joy] Explain how to control the open state (#38355) @michaldudak
- &#8203;<!-- 15 -->[docs][material] Revise the Support page (#38207) @samuelsycamore
- &#8203;<!-- 14 -->[docs][material-ui] Remove incorrect `aria-label`s in extended variant examples of Floating Action Button (#37170) @ashleykolodziej
- &#8203;<!-- 13 -->[docs][material-ui] Adjust slightly the installation page content (#38380) @danilo-leal
- &#8203;<!-- 12 -->[docs][Switch] Fix the readOnly class name in docs (#38277) @michaldudak
- &#8203;<!-- 11 -->[docs][TablePagination] Add Tailwind CSS & plain CSS introduction demo (#38286) @mnajdova

### Examples

- &#8203;<!-- 10 -->[examples] Add Joy UI + Vite.js + TypeScript example app (#37406) @nithins1

### Core

- &#8203;<!-- 30 -->[core] Consistent URL add leading / @oliviertassinari
- &#8203;<!-- 27 -->[docs-infra] Fix rebase issue @oliviertassinari
- &#8203;<!-- 26 -->[docs-infra] Fix typo in docs infra docs @oliviertassinari
- &#8203;<!-- 25 -->[docs-infra] Fix nested list margin (#38456) @oliviertassinari
- &#8203;<!-- 24 -->[docs-infra] Move the Diamond Sponsors to the TOC (#38410) @danilo-leal
- &#8203;<!-- 22 -->[docs-infra] Move imports into page data (#38297) @alexfauquette
- &#8203;<!-- 21 -->[docs-infra] Adjust heading styles (#38365) @danilo-leal
- &#8203;<!-- 20 -->[docs-infra] Fix info callout border color (#38370) @danilo-leal
- &#8203;<!-- 05 -->[website] Upgrade the homepage hero demos design (#38388) @danilo-leal
- &#8203;<!-- 04 -->[website] Improve Base UI hero section demo (#38255) @danilo-leal
- &#8203;<!-- 03 -->[website] Fix EmailSubscribe look (#38429) @oliviertassinari
- &#8203;<!-- 02 -->[website] Link Discord in footer (#38369) @richbustos
- &#8203;<!-- 01 -->[website] Clean up the `GetStartedButtons` component (#38256) @danilo-leal

All contributors of this release in alphabetical order: @alexfauquette, @ashleykolodziej, @danilo-leal, @decadef20, @DiegoAndai, @flaviendelangle, @LukasTy, @michaldudak, @mj12albert, @mnajdova, @nithins1, @oliviertassinari, @richbustos, @sai6855, @samuelsycamore, @TheNatkat, @zanivan

## 5.14.4

<!-- generated comparing v5.14.3..master -->

_Aug 8, 2023_

A big thanks to the 18 contributors who made this release possible. Here are some highlights ✨:

- 🎉 Added [Number Input](https://mui.com/base-ui/react-number-input/) component & [useNumberInput](https://mui.com/base-ui/react-number-input/#hook) hook in [Base UI](https://mui.com/base-ui/getting-started/) @mj12albert

### `@mui/material@5.14.4`

- &#8203;<!-- 25 -->[Checkbox][material] Add size classes (#38182) @michaldudak
- &#8203;<!-- 03 -->[Typography] Improve inherit variant logic (#38123) @ZeeshanTamboli

### `@mui/system@5.14.4`

- &#8203;<!-- 34 -->Revert "[Box] Remove `component` from TypeMap (#38168)" (#38356) @michaldudak

### `@mui/base@5.0.0-beta.10`

#### Breaking changes

- &#8203;<!-- 32 -->[base] Ban default exports (#38200) @michaldudak

  Base UI default exports were changed to named ones. Previously we had a mix of default and named ones.
  This was changed to improve consistency and avoid problems some bundlers have with default exports.
  See https://github.com/mui/material-ui/issues/21862 for more context.

  ```diff
  - import Button, { buttonClasses } from '@mui/base/Button';
  + import { Button, buttonClasses } from '@mui/base/Button';
  - import BaseMenu from '@mui/base/Menu';
  + import { Menu as BaseMenu } from '@mui/base/Menu';
  ```

  Additionally, the `ClassNameGenerator` has been moved to the directory matching its name:

  ```diff
  - import ClassNameGenerator from '@mui/base/className';
  + import { ClassNameGenerator } from '@mui/base/ClassNameGenerator';
  ```

  A codemod is provided to help with the migration:

  ```bash
  npx @mui/codemod@latest v5.0.0/base-use-named-exports <path>
  ```

#### Changes

- &#8203;<!-- 31 -->[base] Create useNumberInput and NumberInput (#36119) @mj12albert
- &#8203;<!-- 28 -->[Select][base] Fix flicker on click of controlled Select button (#37855) @VishruthR
- &#8203;<!-- 09 -->[Dropdown] Fix imports of types (#38296) @yash-thakur

### `@mui/joy@5.0.0-beta.1`

- &#8203;<!-- 06 -->[joy-ui][MenuButton] Fix disable of `MenuButton` (#38342) @sai6855

### Docs

- &#8203;<!-- 33 -->[docs][AppBar] Fix `ResponsiveAppBar` demo logo href (#38346) @iownthegame
- &#8203;<!-- 30 -->[docs][base] Add Tailwind CSS + plain CSS demo on the Button page (#38240) @alisasanib
- &#8203;<!-- 29 -->[docs][Menu][base] Remove `Unstyled` prefix from demos' function names (#38270) @sai6855
- &#8203;<!-- 22 -->[docs] Add themeable component guide (#37908) @siriwatknp
- &#8203;<!-- 21 -->[docs] Fix Joy UI demo background color (#38307) @oliviertassinari
- &#8203;<!-- 20 -->[docs] Update API docs for Number Input component (#38301) @ZeeshanTamboli
- &#8203;<!-- 14 -->[docs][joy-ui] Revise the theme typography page (#38285) @danilo-leal
- &#8203;<!-- 13 -->[docs][joy-ui] Add TS demo for Menu Bar (#38308) @sai6855
- &#8203;<!-- 10 -->[docs][joy-ui] Updated Typography callout at getting started (#38289) @zanivan
- &#8203;<!-- 12 -->[docs][joy-ui] Fix the Inter font installation instructions (#38284) @danilo-leal
- &#8203;<!-- 11 -->[docs][material] Add note to Autocomplete about ref forwarding (#38305) @samuelsycamore
- &#8203;<!-- 05 -->[docs][Skeleton] Make the demos feel more realistic (#38212) @oliviertassinari

- &#8203;<!-- 08 -->[examples] Swap Next.js examples between App Router and Pages Router; update naming convention (#38204) @samuelsycamore
- &#8203;<!-- 07 -->[examples][material-ui] Add Material UI + Next.js (App Router) example in JS (#38323) @samuelsycamore
- &#8203;<!-- 27 -->[blog] Discord announcement blog (#38258) @richbustos
- &#8203;<!-- 26 -->[blog] Fix 301 links to Toolpad @oliviertassinari
- &#8203;<!-- 04 -->[website] Updating Charts demo with real charts usage for MUI X marketing page (#38317) @richbustos
- &#8203;<!-- 03 -->[website] Adjust styles of the Product section on the homepage (#38366) @danilo-leal
- &#8203;<!-- 02 -->[website] Add Nora teamMember card to 'About' (#38358) @noraleonte
- &#8203;<!-- 01 -->[website] Fix image layout shift (#38326) @oliviertassinari

### Core

- &#8203;<!-- 24 -->[core] Fix docs demo export function consistency (#38191) @oliviertassinari
- &#8203;<!-- 23 -->[core] Fix the link-check script on Windows (#38276) @michaldudak
- &#8203;<!-- 26 -->[core] Use @testing-library/user-event direct API (#38325) @mj12albert
- &#8203;<!-- 29 -->[core] Port GitHub workflow for ensuring triage label is present (#38312) @DanailH
- &#8203;<!-- 19 -->[docs-infra] Consider files ending with .types.ts as props files (#37533) @mnajdova
- &#8203;<!-- 18 -->[docs-infra] Fix skip to content design (#38304) @oliviertassinari
- &#8203;<!-- 17 -->[docs-infra] Add a general round of polish to the API content display (#38282) @danilo-leal
- &#8203;<!-- 16 -->[docs-infra] Make the side nav collapse animation snappier (#38259) @danilo-leal
- &#8203;<!-- 15 -->[docs-infra] New Component API design followup (#38183) @cherniavskii
- &#8203;<!-- 06 -->[test] Remove unnecessary `async` keyword from test (#38373) @ZeeshanTamboli

All contributors of this release in alphabetical order: @alisasanib, @cherniavskii, @DanailH, @danilo-leal, @iownthegame, @michaldudak, @mj12albert, @mnajdova, @noraleonte, @oliviertassinari, @richbustos, @sai6855, @samuelsycamore, @siriwatknp, @VishruthR, @yash-thakur, @zanivan, @ZeeshanTamboli

## 5.14.3

<!-- generated comparing v5.14.2..master -->

_Jul 31, 2023_

A big thanks to the 17 contributors who made this release possible. Here are some highlights ✨:

- 🚀 [Joy UI](https://mui.com/joy-ui/getting-started/) is now in Beta
- ✨ Refine [Joy UI](https://mui.com/joy-ui/getting-started/)'s default theme @siriwatknp @zanivan
- 🎉 Added Dropdown higher-level menu component [Base UI](https://mui.com/base-ui/getting-started/) @michaldudak
- 💫 Added Material You [Badge](https://mui.com/material-ui/react-badge/#material-you-version) to `material-next` (#37850) @DiegoAndai

### `@mui/material@5.14.3`

- &#8203;<!-- 36 -->[Autocomplete][material][joy] Add default `getOptionLabel` prop in ownerState (#38100) @DSK9012
- &#8203;<!-- 26 -->[Menu][Divider][material] Do not allow focus on Divider when inside Menu list (#38102) @divyammadhok
- &#8203;<!-- 06 -->[typescript][material] Rename one letter type parameters (#38155) @michaldudak
- &#8203;<!-- 08 -->[Menu][material] Fixes slots and slotProps overriding defaults completely (#37902) @gitstart
- &#8203;<!-- 07 -->[Theme][material] Add missing styleOverrides type for theme MuiStack (#38189) @DiegoAndai
- &#8203;<!-- 04 -->[typescript][material] Add `component` field to `*Props` types (#38084) @michaldudak

### `@mui/base@5.0.0-beta.9`

#### Breaking changes

- &#8203;<!-- 11 -->[Dropdown][base][joy] Introduce higher-level menu component (#37667) @michaldudak

#### Other changes

- &#8203;<!-- 33 -->[typescript][base] Rename one letter type parameters (#38171) @michaldudak

### `@mui/joy@5.0.0-beta.0`

- &#8203;<!-- 10 -->[joy] Refine the default theme (#36843) @siriwatknp

### `@mui/material-next@6.0.0-alpha.95`

- &#8203;<!-- 35 -->[Badge][material-next] Add Badge component (#37850) @DiegoAndai
- &#8203;<!-- 30 -->[Chip][material-next] Copy chip component from material (#38053) @DiegoAndai
- &#8203;<!-- 09 -->[typescript][material-next] Rename one letter type parameters (#38172) @michaldudak

### `@mui/system@5.14.3`

- &#8203;<!-- 32 -->[Box][system] Remove `component` from TypeMap (#38168) @michaldudak
- &#8203;<!-- 05 -->[Stack][system] Fix CSS selector (#37525) @sai6855

### Docs

- &#8203;<!-- 49 -->[docs] Update Joy UI's package README (#38262) @ZeeshanTamboli
- &#8203;<!-- 48 -->[docs][base-ui] Add new batch of coming soon pages (#38025) @danilo-leal
- &#8203;<!-- 44 -->[docs] fix links to standardized examples (#38193) @emmanuel-ferdman
- &#8203;<!-- 43 -->[docs-infra] Small design polish to the Diamond Sponsor container (#38257) @danilo-leal
- &#8203;<!-- 42 -->[docs-infra] Show props in the table of content (#38173) @alexfauquette
- &#8203;<!-- 41 -->[docs-infra] Polish API page design (#38196) @oliviertassinari
- &#8203;<!-- 40 -->[docs-infra] Search with productCategory when product is missing (#38239) @oliviertassinari
- &#8203;<!-- 39 -->[docs][material] Revise and update Examples doc (#38205) @samuelsycamore
- &#8203;<!-- 38 -->[docs] Fix typo in notifications.json @mbrookes
- &#8203;<!-- 37 -->[docs-infra] Remove leftover standardNavIcon (#38252) @DiegoAndai
- &#8203;<!-- 34 -->[docs][base] Add Tailwind CSS & plain CSS demos on the Popper page (#37953) @zanivan
- &#8203;<!-- 31 -->[docs][Button][joy] Improve `loading` prop documentation (#38156) @sai6855
- &#8203;<!-- 25 -->[docs] Prepare docs infra for Tree View migration to X (#38202) @flaviendelangle
- &#8203;<!-- 24 -->[docs] Fix SEO issues reported by ahrefs @oliviertassinari
- &#8203;<!-- 23 -->[docs] Fix palette pages - live edit not working (#38195) @oliviertassinari
- &#8203;<!-- 22 -->[docs] Add Google Analytics action for the styling menu (#38085) @mnajdova
- &#8203;<!-- 21 -->[docs] Fix Discord redirection chain @oliviertassinari
- &#8203;<!-- 20 -->[docs] Cover pnpm in more places (#38161) @oliviertassinari
- &#8203;<!-- 19 -->[docs] Avoid broken link (#38154) @oliviertassinari
- &#8203;<!-- 18 -->[docs] Add notification for beta release of Toolpad (#38152) @prakhargupta1
- &#8203;<!-- 17 -->[docs-infra] Remove sidenav icons (#38174) @oliviertassinari
- &#8203;<!-- 16 -->[docs-infra] Fix search ranking when no productId (#38162) @oliviertassinari
- &#8203;<!-- 15 -->[docs-infra] Adjust the side nav for deeper nested items (#38047) @cherniavskii
- &#8203;<!-- 14 -->[docs][joy] Update TS file of adding more typography levels demo to match the corresponding JS file's styles (#38232) @ZeeshanTamboli
- &#8203;<!-- 13 -->[docs][joy] Add TS demo for reusable component section in approaches page (#38210) @sai6855
- &#8203;<!-- 12 -->[docs][joy] Add TS demo for theme typography new level customization (#38199) @sai6855

### Core

- &#8203;<!-- 47 -->[blog] Fix blog post slug Base UI (#38254) @oliviertassinari
- &#8203;<!-- 46 -->[core] Use native Node's fetch instead of node-fetch package (#38263) @michaldudak
- &#8203;<!-- 45 -->[core] Remove dead code @oliviertassinari
- &#8203;<!-- 29 -->[core] Polish Stack test to closer CSS injection order @oliviertassinari
- &#8203;<!-- 28 -->[core] Remove unnecessary `Required` utility type from Typography font style type (#38203) @ZeeshanTamboli
- &#8203;<!-- 27 -->[core] Fix generate Proptypes script skipping unstable items (#38198) @mj12albert
- &#8203;<!-- 03 -->[website] Adding Rich Bustos Twitter handle in bio (#38213) @richbustos
- &#8203;<!-- 02 -->[website] Prepare importing data from HiBob (#38238) @oliviertassinari
- &#8203;<!-- 01 -->[website] Sync team member with HiBob, add Raffaella (#38201) @rluzists1

All contributors of this release in alphabetical order: @cherniavskii, @DiegoAndai, @divyammadhok, @DSK9012, @flaviendelangle, @gitstart, @michaldudak, @mj12albert, @mnajdova, @oliviertassinari, @prakhargupta1, @richbustos, @rluzists1, @sai6855, @siriwatknp, @zanivan, @ZeeshanTamboli

## 5.14.2

<!-- generated comparing v5.14.1..master -->

_Jul 25, 2023_

A big thanks to the 23 contributors who made this release possible.

### @mui/material@5.14.2

- &#8203;<!-- 39 -->Revert "[core] Adds `component` prop to `OverrideProps` type (#35924)" (#38150) @michaldudak
- &#8203;<!-- 32 -->[Chip][material] Fix base cursor style to be "auto" not "default" (#38076) @DiegoAndai
- &#8203;<!-- 12 -->[Tabs] Refactor IntersectionObserver logic (#38133) @ZeeshanTamboli
- &#8203;<!-- 11 -->[Tabs] Fix and improve visibility of tab scroll buttons using the IntersectionObserver API (#36071) @SaidMarar

### @mui/joy@5.0.0-alpha.89

- &#8203;<!-- 15 -->[Joy] Replace leftover `Joy-` prefix with `Mui-` (#38086) @siriwatknp
- &#8203;<!-- 14 -->[Skeleton][joy] Fix WebkitMaskImage CSS property (#38077) @Bestwebdesign
- &#8203;<!-- 13 -->[Link][Joy UI] Fix font inherit (#38124) @oliviertassinari

### Docs

- &#8203;<!-- 37 -->[docs] Add listbox placement demo for Select (#38130) @sai6855
- &#8203;<!-- 36 -->[docs][base] Add Tailwind CSS & plain CSS demo on the Tabs page (#37910) @mnajdova
- &#8203;<!-- 35 -->[docs][base] Add Tailwind CSS & plain CSS demos on the Textarea