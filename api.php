<?php
/**
 * Islamic AI Animation Maker - Simple PHP API
 * No Laravel required - Pure PHP backend
 */

// Enable CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/api.php', '', $path);

// Story templates
$storyTemplates = [
    'musa' => [
        'title' => '<PERSON><PERSON><PERSON> (A.S.) aur Samundar',
        'story' => '<PERSON><PERSON><PERSON> (A.S.) ne Allah ke hukm se apni lathi uthai. Samundar do hisson mein bant gaya. Bani Israel safely cross kar gaye. Firoun ki army doob gayi. <PERSON> ka karishma dekh kar sab ne Allah ki tareef ki.',
        'scenes' => [
            ['text' => 'Hazrat Musa (A.S.) ne lathi uthai', 'duration' => 3],
            ['text' => 'Samundar do hisson mein bant gaya', 'duration' => 4],
            ['text' => 'Bani Israel safely cross kar gaye', 'duration' => 3],
            ['text' => 'Firoun ki army doob gayi', 'duration' => 3],
            ['text' => 'Sab ne Allah ki tareef ki', 'duration' => 2]
        ]
    ],
    'ibrahim' => [
        'title' => 'Hazrat Ibrahim (A.S.) aur Namrud',
        'story' => 'Hazrat Ibrahim (A.S.) ne Allah ki ibadat ki. Namrud ne unhe aag mein dala. Allah ne aag ko thanda kar diya. Hazrat Ibrahim (A.S.) salamat nikal aaye. Yeh Allah ka mojza tha.',
        'scenes' => [
            ['text' => 'Hazrat Ibrahim (A.S.) ne Allah ki ibadat ki', 'duration' => 3],
            ['text' => 'Namrud ne unhe aag mein dala', 'duration' => 3],
            ['text' => 'Allah ne aag ko thanda kar diya', 'duration' => 4],
            ['text' => 'Hazrat Ibrahim (A.S.) salamat nikal aaye', 'duration' => 3],
            ['text' => 'Yeh Allah ka mojza tha', 'duration' => 2]
        ]
    ],
    'yusuf' => [
        'title' => 'Hazrat Yusuf (A.S.) aur Kuan',
        'story' => 'Hazrat Yusuf (A.S.) ke bhai jealous the. Unhone unhe kuan mein daal diya. Allah ne unhe bachaya. Baad mein woh Misr ke wazir bane. Allah ka plan sabse behtar hai.',
        'scenes' => [
            ['text' => 'Hazrat Yusuf (A.S.) ke bhai jealous the', 'duration' => 3],
            ['text' => 'Unhone unhe kuan mein daal diya', 'duration' => 3],
            ['text' => 'Allah ne unhe bachaya', 'duration' => 3],
            ['text' => 'Woh Misr ke wazir bane', 'duration' => 3],
            ['text' => 'Allah ka plan sabse behtar hai', 'duration' => 2]
        ]
    ]
];

// Route handling
switch ($path) {
    case '/templates':
        if ($method === 'GET') {
            echo json_encode($storyTemplates);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;

    case '/generate':
        if ($method === 'POST') {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['story'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Story is required']);
                break;
            }

            $story = $input['story'];
            $language = $input['language'] ?? 'english';
            $voice = $input['voice'] ?? 'male';
            $template = $input['template'] ?? null;

            // Generate animation
            $animationId = 'anim_' . time() . '_' . rand(1000, 9999);
            
            // Parse story into scenes
            $scenes = parseStoryIntoScenes($story, $template, $storyTemplates);
            
            // Create video (simulation)
            $videoUrl = createAnimationVideo($animationId, $scenes, $language);
            
            $result = [
                'id' => $animationId,
                'status' => 'completed',
                'videoUrl' => $videoUrl,
                'scenes' => $scenes,
                'duration' => array_sum(array_column($scenes, 'duration')),
                'language' => $language,
                'voice' => $voice,
                'createdAt' => date('c')
            ];

            echo json_encode($result);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;

    case (preg_match('/^\/animation\/(.+)$/', $path, $matches) ? true : false):
        if ($method === 'GET') {
            $animationId = $matches[1];
            
            // Check if animation exists (simulation)
            $videoPath = "videos/{$animationId}.mp4";
            
            if (file_exists($videoPath)) {
                echo json_encode([
                    'id' => $animationId,
                    'status' => 'completed',
                    'videoUrl' => "/{$videoPath}",
                    'createdAt' => date('c')
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Animation not found']);
            }
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;

    default:
        http_response_code(404);
        echo json_encode(['error' => 'Endpoint not found']);
        break;
}

/**
 * Parse story into scenes
 */
function parseStoryIntoScenes($story, $template, $templates) {
    // If template is provided, use its scenes
    if ($template && isset($templates[$template])) {
        return $templates[$template]['scenes'];
    }
    
    // Simple story parsing
    $sentences = preg_split('/[.!?]+/', $story);
    $sentences = array_filter($sentences, function($s) { return trim($s) !== ''; });
    
    $scenes = [];
    foreach ($sentences as $sentence) {
        $sentence = trim($sentence);
        if ($sentence) {
            $scenes[] = [
                'text' => $sentence,
                'duration' => max(2, min(5, strlen($sentence) / 20))
            ];
        }
    }
    
    return $scenes;
}

/**
 * Create animation video (simulation)
 */
function createAnimationVideo($animationId, $scenes, $language) {
    // Create videos directory if it doesn't exist
    if (!file_exists('videos')) {
        mkdir('videos', 0777, true);
    }
    
    $videoPath = "videos/{$animationId}.mp4";
    
    // Create a simple HTML file as video placeholder
    $htmlContent = generateVideoHTML($scenes, $language);
    file_put_contents("videos/{$animationId}.html", $htmlContent);
    
    // Create a simple text file as MP4 placeholder
    $videoInfo = "Islamic Animation Video\n";
    $videoInfo .= "ID: {$animationId}\n";
    $videoInfo .= "Language: {$language}\n";
    $videoInfo .= "Scenes: " . count($scenes) . "\n";
    $videoInfo .= "Duration: " . array_sum(array_column($scenes, 'duration')) . " seconds\n\n";
    
    foreach ($scenes as $i => $scene) {
        $videoInfo .= "Scene " . ($i + 1) . ": " . $scene['text'] . " (" . $scene['duration'] . "s)\n";
    }
    
    file_put_contents($videoPath, $videoInfo);
    
    return "/{$videoPath}";
}

/**
 * Generate HTML video preview
 */
function generateVideoHTML($scenes, $language) {
    $html = '<!DOCTYPE html>
<html lang="' . $language . '">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🕌 Islamic Animation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            text-align: center;
            padding: 50px;
            margin: 0;
        }
        .animation-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        .scene {
            font-size: 24px;
            margin: 30px 0;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            animation: fadeIn 2s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .title {
            font-size: 36px;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .footer {
            margin-top: 40px;
            font-size: 16px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="animation-container">
        <h1 class="title">🕌 Islamic Animation</h1>';
    
    foreach ($scenes as $i => $scene) {
        $html .= '<div class="scene">Scene ' . ($i + 1) . ': ' . htmlspecialchars($scene['text']) . '</div>';
    }
    
    $html .= '
        <div class="footer">
            <p>Generated by Islamic AI Animation Maker</p>
            <p>🤲 May Allah bless this content</p>
        </div>
    </div>
</body>
</html>';
    
    return $html;
}
?>
