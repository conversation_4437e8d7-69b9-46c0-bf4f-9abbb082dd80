<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\StoryController;
use App\Http\Controllers\AnimationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::get('/', function () {
    return view('welcome');
});

// CORS headers for all routes
Route::middleware(['cors'])->group(function () {
    
    // Story routes
    Route::prefix('api')->group(function () {
        
        // Get story templates
        Route::get('/templates', [StoryController::class, 'getTemplates']);
        
        // Generate animation from story
        Route::post('/generate', [StoryController::class, 'generateAnimation']);
        
        // Get animation status
        Route::get('/animation/{id}', [AnimationController::class, 'getAnimation']);
        
        // Download animation
        Route::get('/download/{id}', [AnimationController::class, 'downloadAnimation']);
        
        // Get recent animations
        Route::get('/recent', [AnimationController::class, 'getRecentAnimations']);
        
    });
    
});

// Serve generated videos
Route::get('/videos/{filename}', function ($filename) {
    $path = storage_path('app/videos/' . $filename);
    
    if (!file_exists($path)) {
        abort(404);
    }
    
    return response()->file($path);
})->where('filename', '.*');
