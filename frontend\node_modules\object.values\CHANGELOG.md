# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.1](https://github.com/ljharb/Object.values/compare/v1.2.0...v1.2.1) - 2024-12-18

### Commits

- [actions] split out node 10-20, and 20+ [`c13655c`](https://github.com/ljharb/Object.values/commit/c13655cf506b26151debf588ea8df116a8824697)
- [<PERSON>] update `@es-shims/api`, `@ljharb/eslint-config`, `array.prototype.map`, `auto-changelog`, `tape` [`53dd34a`](https://github.com/ljharb/Object.values/commit/53dd34aa1df92d0eff7cb0c99d9137fd3525d342)
- [Refactor] avoid needing call-bound push [`427c25f`](https://github.com/ljharb/Object.values/commit/427c25f3a4f9e11501e20f335ea0ae3a0649ab93)
- [Refactor] use `call-bound` directly [`c8a03c7`](https://github.com/ljharb/Object.values/commit/c8a03c75c82338543c5b67c30486b7ce64044bf6)
- [Tests] replace `aud` with `npm audit` [`44d93b1`](https://github.com/ljharb/Object.values/commit/44d93b16319764d7ec695e316b8151b5334f3bf5)
- [Deps] update `call-bind` [`719d75c`](https://github.com/ljharb/Object.values/commit/719d75c73e664c4ec80c82da8a1be0089e76ea70)
- [Dev Deps] add missing peer dep [`0a19681`](https://github.com/ljharb/Object.values/commit/0a19681a6185008d41f5734e8b9df18f8ece8860)

## [v1.2.0](https://github.com/ljharb/Object.values/compare/v1.1.7...v1.2.0) - 2024-03-18

### Commits

- [Refactor] use `es-object-atoms` instead of `es-abstract`; update `call-bind`, `define-properties` [`a51f9fd`](https://github.com/ljharb/Object.values/commit/a51f9fd7c20458d2d0084d632fdba8a97f5d36b3)
- [Dev Deps] update `array.prototype.map`, `aud`, `npmignore`, `tape` [`540765d`](https://github.com/ljharb/Object.values/commit/540765d28f61687e35bc39423e85d76a65336e60)
- [New] add `auto` entrypoint [`ce8740c`](https://github.com/ljharb/Object.values/commit/ce8740c5783609df274f56747982bb9820436a1b)

## [v1.1.7](https://github.com/ljharb/Object.values/compare/v1.1.6...v1.1.7) - 2023-08-27

### Commits

- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`7a52cb5`](https://github.com/ljharb/Object.values/commit/7a52cb5bd457db370f7928ab411fa39c06c3a1ac)
- [Deps] update `define-properties`, `es-abstract` [`4adc158`](https://github.com/ljharb/Object.values/commit/4adc15840a9521a952f4620d80ef657397119da9)

## [v1.1.6](https://github.com/ljharb/Object.values/compare/v1.1.5...v1.1.6) - 2022-11-06

### Commits

- [actions] reuse common workflows [`4072b71`](https://github.com/ljharb/Object.values/commit/4072b716b4ed42cbd3f3f008ea6a53a374f31bf6)
- [meta] use `npmignore` to autogenerate an npmignore file [`6881278`](https://github.com/ljharb/Object.values/commit/688127818288a7ab3232aa45ab9271c678a702d5)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `array.prototype.map`, `safe-publish-latest`, `tape` [`28c21e6`](https://github.com/ljharb/Object.values/commit/28c21e6c67420b8a1c466321dce35e883b6e4e52)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `array.prototype.map`, `aud`, `auto-changelog`, `functions-have-names`, `tape` [`0e78caa`](https://github.com/ljharb/Object.values/commit/0e78caadf9d6fae70712ea8d5953517d6b3d9bdb)
- [actions] update rebase action to use reusable workflow [`6f37c60`](https://github.com/ljharb/Object.values/commit/6f37c60053cfe4bcdd44ddf0f29296fe3c312c1b)
- [actions] update codecov uploader [`d7c5f30`](https://github.com/ljharb/Object.values/commit/d7c5f3019ccdba81f0afea189e95d5996ea9dd95)
- [Deps] update `define-properties`, `es-abstract` [`911ca0e`](https://github.com/ljharb/Object.values/commit/911ca0ee81f38cd1541c1c7ec7c29ec9904d11d5)

## [v1.1.5](https://github.com/ljharb/Object.values/compare/v1.1.4...v1.1.5) - 2021-10-03

### Commits

- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `tape` [`c397925`](https://github.com/ljharb/Object.values/commit/c3979252140c24514aeebf3d452b422528e36349)
- [Deps] update `es-abstract` [`f98d0da`](https://github.com/ljharb/Object.values/commit/f98d0da2035bf8396b2544f2e2ac02aec766d36f)
- [Robustness] use a call-bound `Array.prototype.push` [`72a3213`](https://github.com/ljharb/Object.values/commit/72a32138e91a9a7b3a828fa1f8f02fe361097c51)
- [meta] npmignore coverage output [`3b0433f`](https://github.com/ljharb/Object.values/commit/3b0433fe3025cb079b0de2373a0a9cfd2e0777b5)

## [v1.1.4](https://github.com/ljharb/Object.values/compare/v1.1.3...v1.1.4) - 2021-05-26

### Commits

- [meta] add `auto-changelog` [`01ee3ac`](https://github.com/ljharb/Object.values/commit/01ee3acb5c767559ce37a3b24bbd30253eae280c)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`f403cba`](https://github.com/ljharb/Object.values/commit/f403cba8852664d82bbf744cd36ad019742a14b5)
- [readme] add actions and codecov badges [`bee5cd2`](https://github.com/ljharb/Object.values/commit/bee5cd21770c9dffa874a2d18d728cf8478e3e7d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`; switch `array-map` for `array.prototype.map` [`8c49dbe`](https://github.com/ljharb/Object.values/commit/8c49dbeed8e3312081d88848abe6bb04383d46ae)
- [Refactor] `propertyIsEnumerable` checks own-ness; remove `has` [`3cb48cf`](https://github.com/ljharb/Object.values/commit/3cb48cf8655504fee0d226036e9582be0fd3fbb0)
- [actions] update workflows [`eb1d757`](https://github.com/ljharb/Object.values/commit/eb1d7574489b86d5d276cf58e3faf77271d0a6f7)
- [Dev Deps] update `eslint`, `tape` [`0abdb2d`](https://github.com/ljharb/Object.values/commit/0abdb2d576aba1074075c1fb25de01ef943aaa77)
- [Tests] increase coverage [`8ca19a3`](https://github.com/ljharb/Object.values/commit/8ca19a3472a9e7883de91d4623fbbce978a0c535)
- [meta] use `prepublishOnly` script for npm 7+ [`88998c8`](https://github.com/ljharb/Object.values/commit/88998c80f21c0aaf7a640ad14caa7b9c08aa1e7c)
- [Deps] update `es-abstract` [`12515ab`](https://github.com/ljharb/Object.values/commit/12515ab54f055c42904caaadecce8d13edd673d8)
- [Deps] update `es-abstract` [`3083ce7`](https://github.com/ljharb/Object.values/commit/3083ce7cd7263cb2570d894947c5d6a4f64feffa)
- [meta] gitignore coverage output [`7aef22b`](https://github.com/ljharb/Object.values/commit/7aef22b2c623b756a3302c6df2265b69b1bb3e10)

## [v1.1.3](https://github.com/ljharb/Object.values/compare/v1.1.2...v1.1.3) - 2021-02-22

### Commits

- [meta] do not publish github action workflow files [`208c71c`](https://github.com/ljharb/Object.values/commit/208c71c6b34125317ea7784cd05f72c2c511fdfb)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `functions-have-names`, `has-strict-mode`, `tape` [`88849ab`](https://github.com/ljharb/Object.values/commit/88849abd1607d5e80cd4252246ad3d9d0876c431)
- [readme] remove travis badge [`0a9a67f`](https://github.com/ljharb/Object.values/commit/0a9a67f3f33fd82a8c45db6ec0059996c19b54cd)
- [actions] update workflows [`4ecb890`](https://github.com/ljharb/Object.values/commit/4ecb8909b6b5dead0a5ce65489f71c626a569826)
- [Deps] update `call-bind`, `es-abstract` [`28845c7`](https://github.com/ljharb/Object.values/commit/28845c737b5aa665344c39bcf87799484891fe47)

## [v1.1.2](https://github.com/ljharb/Object.values/compare/v1.1.1...v1.1.2) - 2020-11-26

### Commits

- [Tests] migrate tests to Github Actions [`6ab9d79`](https://github.com/ljharb/Object.values/commit/6ab9d798482ad84fbcb874b085820b6830d0d4e3)
- [Tests] add `implementation` test; run `es-shim-api` in postlint; use `tape` runner [`65667b7`](https://github.com/ljharb/Object.values/commit/65667b7b346a13f91ce6e541871a7687ff4673a4)
- [Tests] run `nyc` on all tests [`b83f36c`](https://github.com/ljharb/Object.values/commit/b83f36ce151a31a78f7e0b763a0b3a118be76256)
- [actions] add "Allow Edits" workflow [`44c7de1`](https://github.com/ljharb/Object.values/commit/44c7de155a409bda38215c32ce616eb33b0cf1cf)
- [Deps] update `es-abstract`; use `call-bind` where applicable [`35f88b0`](https://github.com/ljharb/Object.values/commit/35f88b0f8c7243238058640518446d09f3abe319)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`, `functions-have-names`; add `safe-publish-latest` [`2495030`](https://github.com/ljharb/Object.values/commit/2495030adccb3f3af615395f364950d76390517d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`1cc1742`](https://github.com/ljharb/Object.values/commit/1cc17421906a8afa3d2ece4ebb745c6b501cba0e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`; add `aud` [`f42bc75`](https://github.com/ljharb/Object.values/commit/f42bc75fc7c3a61a09eb0a2e5b4ae003f92d0023)
- [Fix] do not mutate the native function when present [`ea2a273`](https://github.com/ljharb/Object.values/commit/ea2a273cbd8f60f91cf9bb6528b8d5d1cae25f47)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`5040d09`](https://github.com/ljharb/Object.values/commit/5040d09bee5c3ea77694f76ff95c6d4b09bf4a0c)
- [Tests] only audit prod deps [`2803c92`](https://github.com/ljharb/Object.values/commit/2803c924114308f261e4c38dfc81706458046e7b)
- [Deps] update `es-abstract` [`d15d0e5`](https://github.com/ljharb/Object.values/commit/d15d0e5e0ac5342588aa558477e56a075b4b130f)
- [Dev Deps] update `tape` [`e83fe3a`](https://github.com/ljharb/Object.values/commit/e83fe3a4c38dc678866cd0905445d59ea7c58b5b)
- [Deps] update `es-abstract` [`cfab489`](https://github.com/ljharb/Object.values/commit/cfab48956d88dbf4a2659672c648465ca0cc3fc8)

## [v1.1.1](https://github.com/ljharb/Object.values/compare/v1.1.0...v1.1.1) - 2019-12-12

### Commits

- [Tests] use shared travis-ci configs [`2a82dea`](https://github.com/ljharb/Object.values/commit/2a82dea7bbde9773c0fbae64f7a70f9236effd86)
- [Tests] up to `node` `v12.7`, `v11.15`, `v10.16`, `v8.16`, `v6.17`; use `nvm install-latest-npm` [`58a4209`](https://github.com/ljharb/Object.values/commit/58a420950f906d62b9b62b6ad5881216f7585be2)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`80080cd`](https://github.com/ljharb/Object.values/commit/80080cdd93f8dbceb3383864e4d18f8cbb4947c0)
- [actions] add automatic rebasing / merge commit blocking [`e8a9297`](https://github.com/ljharb/Object.values/commit/e8a92975258fd674a2c00380f7ee7c2adb6ee009)
- [Refactor] use split-up `es-abstract` (85% bundle size decrease) [`a8ac36f`](https://github.com/ljharb/Object.values/commit/a8ac36fcad70e86a42d08cf0163f7b23945a3f45)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `functions-have-names` [`c74ad8a`](https://github.com/ljharb/Object.values/commit/c74ad8adcb78173f8e9cec541366c21307587de4)
- [Dev Deps] update `object-keys`, `tape`, `eslint` [`10adf18`](https://github.com/ljharb/Object.values/commit/10adf18de8784be02de042b7c8eec5dd911f4f1a)
- [meta] add `funding` field [`110dbca`](https://github.com/ljharb/Object.values/commit/110dbca51bc0413bab11fe56dabc5d0a373415a7)
- [Tests] use `functions-have-names` [`40746e5`](https://github.com/ljharb/Object.values/commit/40746e5311298d61385e3e316e3f0bc2956d9053)
- [Deps] update `es-abstract` [`9b7c99b`](https://github.com/ljharb/Object.values/commit/9b7c99b9881a3af022ec002cedd72a9aa64f34d2)

## [v1.1.0](https://github.com/ljharb/Object.values/compare/v1.0.4...v1.1.0) - 2019-01-01

### Commits

- [Tests] remove `jscs` [`a9d5f94`](https://github.com/ljharb/Object.values/commit/a9d5f94000ae6efb44cd964dcdfa5b986596392f)
- [Tests] up to `node` `v11.1`, `v10.13`, `v9.11`, `v8.12`, `v7.10`, `v6.14`, `v4.9`; use `nvm install-latest-npm` [`103a907`](https://github.com/ljharb/Object.values/commit/103a907829c29fabf16e880b178341d3b6832e32)
- [Tests] up to `node` `v7.4`, `v4.7`; improve test matrix [`66caa4a`](https://github.com/ljharb/Object.values/commit/66caa4a74e08de777c1b00185f796e609704bb83)
- [Tests] up to `node` `v11.6`, `v10.15`, `v8.15`, `v6.16` [`81de647`](https://github.com/ljharb/Object.values/commit/81de647b1eede60d69f49cf4ccbf970e3224528e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `nsp`, `tape` [`13c5dd1`](https://github.com/ljharb/Object.values/commit/13c5dd1fa7762cf509deafde12b14fe65e6c0c17)
- [Dev Deps] add missing `object-keys` dev dep [`7cbc7f3`](https://github.com/ljharb/Object.values/commit/7cbc7f38ac6b13738af8b42b62d0c89c5dce6bc6)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape` [`1bf1dbe`](https://github.com/ljharb/Object.values/commit/1bf1dbe0b1fa1c646ef2c6c1bc3a14f5d4045f92)
- [Deps] update `define-properties`, `es-abstract`, `function-bind`, `has` [`faa4051`](https://github.com/ljharb/Object.values/commit/faa40515ff43662e3f381147a902e978cf250bdc)
- [Tests] use `npm audit` instead of `nsp` [`428fb50`](https://github.com/ljharb/Object.values/commit/428fb50c79cc0ef1e62fc053dc959a8848b0d190)
- Only apps should have lockfiles [`f35eb85`](https://github.com/ljharb/Object.values/commit/f35eb8520052bb4dd27ccfd4a399c6011c2ec157)
- [New] add `auto` entry point [`ed730e3`](https://github.com/ljharb/Object.values/commit/ed730e3b2508e4d89691b57d00fc63eebce7130e)

## [v1.0.4](https://github.com/ljharb/Object.values/compare/v1.0.3...v1.0.4) - 2016-12-04

### Commits

- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `@es-shims/api` [`b0db351`](https://github.com/ljharb/Object.values/commit/b0db351280cfebad008eedff99556cead33a2f37)
- [Tests] up to `node` `v7.2`, `v6.9`, `v4.6`; improve test matrix. [`89362d3`](https://github.com/ljharb/Object.values/commit/89362d30ccee1a27f8d85c114adb14937b4ca7d0)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`883dce4`](https://github.com/ljharb/Object.values/commit/883dce455bcab08a045561554055003c3f06a917)
- [Dev Deps] update `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`67e83b8`](https://github.com/ljharb/Object.values/commit/67e83b8a84ff77f29c99b924760256e05e358dd8)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`2c1c6c7`](https://github.com/ljharb/Object.values/commit/2c1c6c759725418806d079abf828fd16b3df0aba)
- [Dev Deps] update `jscs`, `nsp`, `eslint`, `@es-shims/api` [`d878ea6`](https://github.com/ljharb/Object.values/commit/d878ea62dc98952e62136de5a42a8caab2a66e84)
- [Tests] up to `node` `v5.6`, `v4.3` [`f37757e`](https://github.com/ljharb/Object.values/commit/f37757e78526e060d7fe2a7ba11414690bbbaa98)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`348a2a3`](https://github.com/ljharb/Object.values/commit/348a2a386b791fe090a08b7f33111d531277a3ab)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`b1e915e`](https://github.com/ljharb/Object.values/commit/b1e915ed9b35bdb9bb2b7ea35cdd5c78e62636d9)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`015b78d`](https://github.com/ljharb/Object.values/commit/015b78db57c4cc46d06b1b4f04f4ea2fdb35fc0c)
- [Tests] up to `node` `v5.9`, `v4.4` [`b33686e`](https://github.com/ljharb/Object.values/commit/b33686ea45a842c84e5186416fb52be31a60cc7e)
- [Dev Deps] update `jscs` [`0a44aa3`](https://github.com/ljharb/Object.values/commit/0a44aa30ab2fd5fe4a15070237c700172e517b3f)
- [Docs] update to reflect ES2017 inclusion. [`c01085b`](https://github.com/ljharb/Object.values/commit/c01085b134408bffadafa060bd1585a61674dd57)
- [Tests] use pretest/posttest for linting/security. [`8f9c6c7`](https://github.com/ljharb/Object.values/commit/8f9c6c7104d5694aaf266a6e6d12a92b9731a709)
- [Tests] fix npm upgrades on older nodes [`68c195f`](https://github.com/ljharb/Object.values/commit/68c195fe88677667002e5102c4efcfcc7bef059b)
- [Tests] up to `node` `v6.2` [`48fac8d`](https://github.com/ljharb/Object.values/commit/48fac8de7265142acb1554b28a29322baf53be20)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`471a794`](https://github.com/ljharb/Object.values/commit/471a7940a7dff28a6544c6c253b90daa60ff3c65)
- [Deps] update `es-abstract` [`7e644f1`](https://github.com/ljharb/Object.values/commit/7e644f1bb7c6db533ed137d88de7856e7b9c55b1)
- [Deps] update `es-abstract` [`1856267`](https://github.com/ljharb/Object.values/commit/18562676e8aa606a47116753c323dd28619dea50)
- [Tests] on `node` `v5.12` [`8a4e0c0`](https://github.com/ljharb/Object.values/commit/8a4e0c03415fb67fc2ac420eba6ece5b660cbfe7)
- [Tests] on `node` `v5.10` [`aaeb1a7`](https://github.com/ljharb/Object.values/commit/aaeb1a7a176eca1e69f4df7a8979f0ba40b12c29)
- [Deps] update `function-bind` [`14d2e88`](https://github.com/ljharb/Object.values/commit/14d2e88045d8f92877e9a31f31a2b624267de54f)
- [Deps] update `es-abstract` [`63e46a9`](https://github.com/ljharb/Object.values/commit/63e46a9efd3081872e92ec94a2ed6dbaae3c615a)
- [Deps] update `es-abstract` [`7c40db6`](https://github.com/ljharb/Object.values/commit/7c40db6381bad3232be3e1c24ef79839b6edaba7)
- [Deps] update `define-properties` [`0a86270`](https://github.com/ljharb/Object.values/commit/0a86270bda6478c4e08045b1fb3f2eb72b2207cd)
- [Tests] on `node` `v4.2` [`8ee831d`](https://github.com/ljharb/Object.values/commit/8ee831dae76ef522d6acaae9869a1a7b465361a4)
- [Tests] on `node` `v5.0` [`b9c88c7`](https://github.com/ljharb/Object.values/commit/b9c88c74f2cbb280cfc1c88064c9cd514b4876ae)

## [v1.0.3](https://github.com/ljharb/Object.values/compare/v1.0.2...v1.0.3) - 2015-10-06

### Commits

- Add test case to cover non-enumerable keys made enumerable by a previous getter. [`9d61739`](https://github.com/ljharb/Object.values/commit/9d617395f0ec81cbdcbc50a46e4489c95253f80b)
- [Dev Deps] update `tape`, `eslint`, `@ljharb/eslint-config` [`cf6dff8`](https://github.com/ljharb/Object.values/commit/cf6dff84131cef953498988249c5306c71c1fe9f)
- [Dev Deps] update `jscs`, `eslint` [`f439029`](https://github.com/ljharb/Object.values/commit/f439029b228e0dab0542caa63e1d032889d53705)
- [Deps] update `es-abstract` [`6879994`](https://github.com/ljharb/Object.values/commit/6879994fec305490b5550d25ce390e4b64c74fe0)

## [v1.0.2](https://github.com/ljharb/Object.values/compare/v1.0.1...v1.0.2) - 2015-09-25

### Fixed

- Not-yet-visited keys deleted on a [[Get]] must not show up in the output [`#1`](https://github.com/ljharb/Object.values/issues/1)

## [v1.0.1](https://github.com/ljharb/Object.values/compare/v1.0.0...v1.0.1) - 2015-09-21

### Commits

- [Tests] on `io.js` `v3.3`, up to `node` `v4.1` [`27aac5f`](https://github.com/ljharb/Object.values/commit/27aac5f80c05f66d584056931b63eed637059621)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`83cde5c`](https://github.com/ljharb/Object.values/commit/83cde5c685db366f769d738bc30f7a6d273bcc3a)
- Add es-shim API keyword [`2c6b885`](https://github.com/ljharb/Object.values/commit/2c6b88550c155b4f9a7c6ece609b8d97d77b2942)
- [Docs] update version badge URL [`626bf99`](https://github.com/ljharb/Object.values/commit/626bf991d9b0f5720819537698918b295c6ea0d0)

## v1.0.0 - 2015-09-02

### Commits

- Dotfiles [`5251629`](https://github.com/ljharb/Object.values/commit/5251629c47404fa0dec7b6d230eda10da0f47eea)
- Tests [`b02e54e`](https://github.com/ljharb/Object.values/commit/b02e54ef157c56117b390cb1db95f7d48163a838)
- package.json [`7b84f4e`](https://github.com/ljharb/Object.values/commit/7b84f4e839f75b227a5233325f064900a6032f9a)
- Read me [`31e115b`](https://github.com/ljharb/Object.values/commit/31e115b31af2eabf4db518343310f2bb42e7fa04)
- Initial commit [`01271a5`](https://github.com/ljharb/Object.values/commit/01271a51976524c62bac00d8af9af3ede72831dd)
- Implementation. [`9a0c315`](https://github.com/ljharb/Object.values/commit/9a0c31554899f411d31d3b8343c754eb2c19842e)
- Clarifying tests that only Symbol *properties* are omitted. [`16b3abb`](https://github.com/ljharb/Object.values/commit/16b3abb7c15790cb63e9b2b842b75fdc4210ad69)
